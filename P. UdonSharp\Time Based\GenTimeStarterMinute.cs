using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GenTimeStarterMinute : UdonSharpBehaviour
{
    public GameObject[] TimedEvents;
    public int[] activationMinuteRange; // 0=0-5min, 1=5-10min, 2=10-15min, etc.
    public int currentMinuteRange, LastMinuteRange;
    public bool IsEnabled;
    private bool timerRunning = false;

    void Start()
    {
        IsEnabled = true;
        StartTimer();
    }

    void OnEnable()
    {
        IsEnabled = true;
        if (!timerRunning){StartTimer();}
    }

    void OnDisable(){IsEnabled = false;}

    private void StartTimer()
    {
        if (!timerRunning)
        {
            timerRunning = true;
            SendCustomEventDelayedSeconds(nameof(UpdateTime), 40f);
        }
    }

    public void UpdateTime()
    {
        if (!IsEnabled)
        {
            timerRunning = false;
            return;
        }

        System.DateTime networkTime = Networking.GetNetworkDateTime().ToUniversalTime();
        int currentMinute = networkTime.Minute;

        // Calculate which 5-minute range we're in (0=0-4min, 1=5-9min, 2=10-14min, etc.)
        currentMinuteRange = currentMinute / 5;

        for (int i = 0; i < activationMinuteRange.Length; i++){
            if (currentMinuteRange == activationMinuteRange[i] && currentMinuteRange != LastMinuteRange)
            {
                if (TimedEvents[i] != null){TimedEvents[i].SetActive(true);}
                Debug.Log("GenTimeStarter triggered for minute range " + (activationMinuteRange[i] * 5) + "-" + ((activationMinuteRange[i] * 5) + 4) + " (range " + currentMinuteRange + ")");
            }
            else if (currentMinuteRange != activationMinuteRange[i] && currentMinuteRange != LastMinuteRange)
            {
                if (TimedEvents[i] != null){TimedEvents[i].SetActive(false);}
            }
        }

        if(currentMinuteRange != LastMinuteRange){LastMinuteRange = currentMinuteRange;}

        if(IsEnabled){SendCustomEventDelayedSeconds(nameof(UpdateTime), 60f);}
        else{timerRunning = false;}
    }

    public void DeleteEverything(){
        for (int i = 0; i < TimedEvents.Length; i++){
            if (TimedEvents[i] != null){Destroy(TimedEvents[i]);}
        }
        Destroy(gameObject);
    }
}
