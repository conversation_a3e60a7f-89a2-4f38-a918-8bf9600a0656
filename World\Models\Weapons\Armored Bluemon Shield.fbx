; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 6
		Day: 21
		Hour: 0
		Minute: 12
		Second: 46
		Millisecond: 314
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\5ughkt6x_Armored Bluemon Shield.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\5ughkt6x_Armored Bluemon Shield.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2410575222944, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 7
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 2
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2410720323456, "Geometry::Scene", "Mesh" {
		Vertices: *72 {
			a: -4471.65069580078,941.23363494873,-1056.10542297363,-2375.08602142334,941.23363494873,353.578567504883,-4466.33415222168,882.356452941895,128.677749633789,-2372.26276397705,882.356452941895,1538.36364746094,2369.89994049072,942.965126037598,342.358779907227,2372.72319793701,884.088134765625,1527.14347839355,4467.21725463867,884.087944030762,117.457962036133,4461.90223693848,942.965126037598,-1067.32521057129,-2369.9182510376,-5911.865234375,1067.33684539795,2375.06790161133,-5910.13336181641,1056.11438751221,-2372.74150848389,-5852.98728942871,-117.446517944336,2372.24445343018,-5851.25579833984,-128.667449951172,-4461.92054748535,-5911.865234375,-342.348861694336,-4467.23594665527,-5852.98728942871,-1527.13088989258,4466.31546020508,-5851.25579833984,-1538.35182189941,4471.63200378418,-5910.13336181641,-353.571319580078,-2372.26276397705,5851.18446350098,1538.36364746094,-2375.08602142334,5910.06126403809,353.580474853516,2369.89994049072,5911.79313659668,342.360305786133,2372.72319793701,5852.91481018066,1527.14347839355,4461.90223693848,5911.79313659668,-1067.32368469238,4467.21725463867,5852.91481018066,117.459487915039,-4466.33415222168,5851.18446350098,128.679656982422,-4471.65069580078,5910.06126403809,-1056.103515625
		} 
		PolygonVertexIndex: *168 {
			a: 0,2,-2,2,3,-2,1,3,-5,3,5,-5,4,5,-7,7,4,-7,8,5,-4,5,8,-10,10,4,-12,4,10,-2,12,0,-14,0,12,-3,6,14,-8,14,6,-16,9,10,-12,10,9,-9,4,14,-12,14,4,-8,11,15,-10,15,11,-15,9,6,-6,6,9,-16,8,13,-11,13,8,-13,10,0,-2,0,10,-14,3,12,-9,12,3,-3,4,7,-7,5,4,-7,5,3,-5,3,1,-5,3,2,-2,2,0,-2,16,18,-18,18,16,-20,19,20,-19,20,19,-22,17,22,-17,22,17,-24,5,16,-4,16,5,-20,6,19,-6,19,6,-22,4,20,-8,20,4,-19,7,21,-7,21,7,-21,0,17,-2,17,0,-24,1,18,-5,18,1,-18,3,22,-3,22,3,-17,2,23,-1,23,2,-23
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *504 {
				a: -0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0.0333260037004948,0.998220145702362,0.049456525593996,0.0333260037004948,0.998220145702362,0.049456525593996,0.0333260037004948,0.998220145702362,0.049456525593996,0.00238445168361068,-0.0691607072949409,0.997602701187134,0.00238445168361068,-0.0691607072949409,0.997602701187134,0.00238421116955578,-0.0691605433821678,0.997602701187134,0.00238445168361068,-0.0691607072949409,0.997602701187134,0.00238445168361068,-0.0691607072949409,0.997602701187134,0.00238469219766557,-0.0691608712077141,0.997602641582489,-0.0023842453956604,0.0691603720188141,-0.997602701187134,-0.0023842453956604,0.0691603720188141,-0.997602701187134,-0.00238436576910317,0.0691604614257812,-0.997602701187134,-0.0023842453956604,0.0691603720188141,-0.997602701187134,-0.0023842453956604,0.0691603720188141,-0.997602701187134,-0.00238412525504827,0.0691602900624275,-0.997602701187134,-0.999989688396454,-0.000957450829446316,0.00443924823775887,-0.999989688396454,-0.000957450829446316,0.00443924823775887,-0.999989748001099,-0.000957501237280667,0.00443876488134265,-0.999989688396454,-0.000957450829446316,0.00443924823775887,-0.999989688396454,-0.000957450829446316,0.00443924823775887,-0.999989628791809,-0.000957400363404304,0.0044397315941751,0.999989688396454,0.000957413169089705,-0.00443909224122763,0.999989688396454,0.000957413169089705,-0.00443909224122763,0.999989748001099,0.000957255659159273,-0.00443845149129629,0.999989688396454,0.000957413169089705,-0.00443909224122763,0.999989688396454,0.000957413169089705,-0.00443909224122763,0.999989628791809,0.000957570679020137,-0.00443973299115896,0.000247116899117827,-0.99876743555069,-0.0496343225240707,0.000247116899117827,-0.99876743555069,-0.0496343225240707,0.000247085030423477,-0.99876743555069,-0.0496341958642006,0.000247116899117827,-0.99876743555069,-0.0496343225240707,0.000247116899117827,-0.99876743555069,-0.0496343225240707,0.000247148738708347,-0.99876743555069,-0.0496344491839409,
-0.557712078094482,0.057130366563797,-0.828065991401672,-0.557712078094482,0.057130366563797,-0.828065991401672,-0.557519495487213,0.0572238899767399,-0.828189253807068,-0.557712078094482,0.057130366563797,-0.828065991401672,-0.557712078094482,0.057130366563797,-0.828065991401672,-0.557904660701752,0.0570368468761444,-0.827942788600922,-0.0332971699535847,-0.998219311237335,-0.0494920164346695,-0.0332971699535847,-0.998219311237335,-0.0494920164346695,-0.0333008579909801,-0.998217463493347,-0.0495269186794758,-0.0332971699535847,-0.998219311237335,-0.0494920164346695,-0.0332971699535847,-0.998219311237335,-0.0494920164346695,-0.0332934819161892,-0.998221158981323,-0.0494571141898632,0.557255148887634,-0.0571521297097206,0.828372061252594,0.557255148887634,-0.0571521297097206,0.828372061252594,0.557442188262939,-0.0572278313338757,0.828240990638733,0.557255148887634,-0.0571521297097206,0.828372061252594,0.557255148887634,-0.0571521297097206,0.828372061252594,0.557068109512329,-0.0570764280855656,0.828503131866455,0.0334842130541801,-0.998201727867126,-0.0497208312153816,0.0334842130541801,-0.998201727867126,-0.0497208312153816,0.0334405340254307,-0.998204946517944,-0.0496855974197388,0.0334842130541801,-0.998201727867126,-0.0497208312153816,0.0334842130541801,-0.998201727867126,-0.0497208312153816,0.0335278883576393,-0.998198509216309,-0.0497560650110245,0.557236969470978,0.0577042475342751,-0.828346014022827,0.557236969470978,0.0577042475342751,-0.828346014022827,0.557049870491028,0.0576283261179924,-0.828477144241333,0.557236969470978,0.0577042475342751,-0.828346014022827,0.557236969470978,0.0577042475342751,-0.828346014022827,0.557424068450928,0.0577801652252674,-0.828214883804321,-0.557694673538208,-0.0576832741498947,0.828039407730103,-0.557694673538208,-0.0576832741498947,0.828039407730103,-0.557887315750122,-0.0575899221003056,0.827916145324707,-0.557694673538208,-0.0576832741498947,0.828039407730103,-0.557694673538208,-0.0576832741498947,0.828039407730103,-0.557502090930939,-0.0577766299247742,0.828162610530853,
-0.0333260037004948,-0.998220145702362,-0.049456525593996,-0.0333260037004948,-0.998220145702362,-0.049456525593996,-0.0333260037004948,-0.998220145702362,-0.049456525593996,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0.000247012882027775,0.998767495155334,0.0496341399848461,-0.000247012882027775,0.998767495155334,0.0496341399848461,-0.000247174990363419,0.998767495155334,0.0496334992349148,-0.000247012882027775,0.998767495155334,0.0496341399848461,-0.000247012882027775,0.998767495155334,0.0496341399848461,-0.000246850744588301,0.99876743555069,0.0496347807347775,0.0333303958177567,0.998218178749084,0.0494924671947956,0.0333303958177567,0.998218178749084,0.0494924671947956,0.0333736315369606,0.998215019702911,0.0495272092521191,0.0333303958177567,0.998218178749084,0.0494924671947956,0.0333303958177567,0.998218178749084,0.0494924671947956,0.0332871600985527,0.998221337795258,0.0494577251374722,-0.0334503650665283,0.998202919960022,0.049719762057066,-0.0334503650665283,0.998202919960022,0.049719762057066,-0.0334466397762299,0.99820476770401,0.0496846474707127,-0.0334503650665283,0.998202919960022,0.049719762057066,-0.0334503650665283,0.998202919960022,0.049719762057066,-0.0334540866315365,0.998201012611389,0.0497548766434193,0.00236463034525514,0,0.999997198581696,0.00236463034525514,0,0.999997198581696,0.00236463034525514,0,0.999997198581696,0.00236463034525514,0,0.999997198581696,0.00236463034525514,0,0.999997198581696,0.00236463034525514,0,0.999997198581696,0.558357059955597,-1.27381213133049e-07,0.829600751399994,0.558357059955597,-1.27381213133049e-07,0.829600751399994,0.558357298374176,0,0.829600632190704,0.558357059955597,-1.27381213133049e-07,0.829600751399994,0.558357059955597,-1.27381213133049e-07,0.829600751399994,0.558356821537018,-2.54762426266097e-07,0.829600870609283,-0.558814287185669,2.54667781973694e-07,-0.829292833805084,-0.558814287185669,2.54667781973694e-07,-0.829292833805084,-0.558814287185669,2.54667781973694e-07,-0.829292833805084,
-0.558814287185669,2.54667781973694e-07,-0.829292833805084,-0.558814287185669,2.54667781973694e-07,-0.829292833805084,-0.558814287185669,2.54667781973694e-07,-0.829292833805084,0.999989926815033,1.37761424490179e-09,-0.00448602251708508,0.999989926815033,1.37761424490179e-09,-0.00448602251708508,0.999989926815033,1.3776144669464e-09,-0.00448602251708508,0.999989926815033,1.37761424490179e-09,-0.00448602251708508,0.999989926815033,1.37761424490179e-09,-0.00448602251708508,0.999989926815033,1.37761413387949e-09,-0.00448602251708508,0.557977080345154,3.18551030886738e-07,-0.829856336116791,0.557977080345154,3.18551030886738e-07,-0.829856336116791,0.557977080345154,3.18551030886738e-07,-0.829856336116791,0.557977080345154,3.18551030886738e-07,-0.829856336116791,0.557977080345154,3.18551030886738e-07,-0.829856336116791,0.557977080345154,3.18551030886738e-07,-0.829856336116791,-0.00236459029838443,3.45475626772895e-07,-0.999997198581696,-0.00236459029838443,3.45475626772895e-07,-0.999997198581696,-0.00236455025151372,3.07089436546448e-07,-0.999997198581696,-0.00236459029838443,3.45475626772895e-07,-0.999997198581696,-0.00236459029838443,3.45475626772895e-07,-0.999997198581696,-0.00236463034525514,3.83861816999342e-07,-0.999997198581696,-0.558434665203094,-1.59216412498608e-07,0.829548537731171,-0.558434665203094,-1.59216412498608e-07,0.829548537731171,-0.558434963226318,-3.18432824997217e-07,0.829548358917236,-0.558434665203094,-1.59216412498608e-07,0.829548537731171,-0.558434665203094,-1.59216412498608e-07,0.829548537731171,-0.558434367179871,0,0.829548716545105,-0.999989926815033,-1.72251213292895e-09,0.00448731053620577,-0.999989926815033,-1.72251213292895e-09,0.00448731053620577,-0.999989926815033,-1.72251224395126e-09,0.00448731100186706,-0.999989926815033,-1.72251213292895e-09,0.00448731053620577,-0.999989926815033,-1.72251213292895e-09,0.00448731053620577,-0.999989926815033,-1.72251202190665e-09,0.00448731053620577
			} 
			NormalsW: *168 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *504 {
				a: -0.000684762489981949,-0,0.999999761581421,-0.000684762489981949,-0,0.999999761581421,-0.000684762489981949,-0,0.999999761581421,-0.00068453949643299,-0,0.999999761581421,-0.00068453949643299,-0,0.999999761581421,-0.00068453949643299,-0,0.999999761581421,-1.05746994449873e-06,-0,1,-1.05746994449873e-06,-0,1,-1.05746994449873e-06,-0,1,-9.76314368017484e-07,-0,1,-9.76314368017484e-07,-0,1,-9.76314368017484e-07,-0,1,0.000690421962644905,-0,0.999999761581421,0.000690421962644905,-0,0.999999761581421,0.000690421962644905,-0,0.999999761581421,-0.000959828379563987,-0.0494520217180252,0.998776018619537,-0.000959828379563987,-0.0494520217180252,0.998776018619537,-0.000959828379563987,-0.0494520217180252,0.998776018619537,0.00016530355787836,0.997605621814728,0.0691605135798454,0.00016530355787836,0.997605621814728,0.0691605135798454,0.000165230201673694,0.997605621814728,0.0691603496670723,0.00016530355787836,0.997605621814728,0.0691605135798454,0.00016530355787836,0.997605621814728,0.0691605135798454,0.000165376928634942,0.997605562210083,0.0691606774926186,0.000165358229423873,0.997605621814728,0.0691601783037186,0.000165358229423873,0.997605621814728,0.0691601783037186,0.000165299628861248,0.997605621814728,0.0691602677106857,0.000165358229423873,0.997605621814728,0.0691601783037186,0.000165358229423873,0.997605621814728,0.0691601783037186,0.000165416829986498,0.997605621814728,0.069160096347332,-0.000957441807258874,0.999999582767487,4.16561988458852e-06,-0.000957441807258874,0.999999582767487,4.16561988458852e-06,-0.000957491807639599,0.999999642372131,4.25064718001522e-06,-0.000957441807258874,0.999999582767487,4.16561988458852e-06,-0.000957441807258874,0.999999582767487,4.16561988458852e-06,-0.000957391690462828,0.999999523162842,4.08059167966712e-06,-0.000957403157372028,0.999999582767487,4.39041195932077e-06,-0.000957403157372028,0.999999582767487,4.39041195932077e-06,-0.000957245007157326,0.999999642372131,4.5285491978575e-06,-0.000957403157372028,0.999999582767487,4.39041195932077e-06,-0.000957403157372028,0.999999582767487,4.39041195932077e-06,
-0.000957561249379069,0.999999523162842,4.2522760850261e-06,1.23020981845912e-05,-0.0496343225240707,0.998767495155334,1.23020981845912e-05,-0.0496343225240707,0.998767495155334,1.22941428344348e-05,-0.0496341958642006,0.998767495155334,1.23020981845912e-05,-0.0496343225240707,0.998767495155334,1.23020981845912e-05,-0.0496343225240707,0.998767495155334,1.23100535347476e-05,-0.0496344491839409,0.998767495155334,0.0319144241511822,0.998366713523865,0.0473851077258587,0.0319144241511822,0.998366713523865,0.0473851077258587,0.0319558009505272,0.99836140871048,0.0474699959158897,0.0319144241511822,0.998366713523865,0.0473851077258587,0.0319144241511822,0.998366713523865,0.0473851077258587,0.0318730063736439,0.998372137546539,0.04730024933815,-0.00164998811669648,-0.0494645088911057,0.998774528503418,-0.00164998811669648,-0.0494645088911057,0.998774528503418,-0.00165133760310709,-0.0494993813335896,0.998772799968719,-0.00164998811669648,-0.0494645088911057,0.998774528503418,-0.00164998811669648,-0.0494645088911057,0.998774528503418,-0.00164863909594715,-0.0494296289980412,0.998776257038116,0.0319004654884338,0.998365521430969,0.0474207401275635,0.0319004654884338,0.998365521430969,0.0474207401275635,0.031953576952219,0.998361170291901,0.0474762469530106,0.0319004654884338,0.998365521430969,0.0474207401275635,0.0319004654884338,0.998365521430969,0.0474207401275635,0.0318473763763905,0.998369872570038,0.0473652146756649,0.00166691734921187,-0.0496928803622723,0.998763203620911,0.00166691734921187,-0.0496928803622723,0.998763203620911,0.00166356202680618,-0.0496577434241772,0.998764991760254,0.00166691734921187,-0.0496928803622723,0.998763203620911,0.00166691734921187,-0.0496928803622723,0.998763203620911,0.00167027581483126,-0.049728024750948,0.998761475086212,-0.0322086066007614,0.998333692550659,0.0478788651525974,-0.0322086066007614,0.998333692550659,0.0478788651525974,-0.0321552865207195,0.998338103294373,0.0478232316672802,-0.0322086066007614,0.998333692550659,0.0478788651525974,-0.0322086066007614,0.998333692550659,0.0478788651525974,
-0.0322619527578354,0.998329341411591,0.0479344762861729,-0.03222331777215,0.998335003852844,0.0478436872363091,-0.03222331777215,0.998335003852844,0.0478436872363091,-0.0321820974349976,0.998340308666229,0.0477588884532452,-0.03222331777215,0.998335003852844,0.0478436872363091,-0.03222331777215,0.998335003852844,0.0478436872363091,-0.0322644896805286,0.99832957983017,0.0479285083711147,-0.000959828379563987,-0.0494520217180252,0.998776018619537,-0.000959828379563987,-0.0494520217180252,0.998776018619537,-0.000959828379563987,-0.0494520217180252,0.998776018619537,0.000690421962644905,0,0.999999761581421,0.000690421962644905,0,0.999999761581421,0.000690421962644905,0,0.999999761581421,-9.76314368017484e-07,-0,1,-9.76314368017484e-07,-0,1,-9.76314368017484e-07,-0,1,-1.05746994449873e-06,-0,1,-1.05746994449873e-06,-0,1,-1.05746994449873e-06,-0,1,-0.00068453949643299,-0,0.999999761581421,-0.00068453949643299,-0,0.999999761581421,-0.00068453949643299,-0,0.999999761581421,-0.000684762489981949,-0,0.999999761581421,-0.000684762489981949,-0,0.999999761581421,-0.000684762489981949,-0,0.999999761581421,0.00237982394173741,-0.0496334135532379,0.998764753341675,0.00237982394173741,-0.0496334135532379,0.998764753341675,0.00237983162514865,-0.0496327728033066,0.998764753341675,0.00237982394173741,-0.0496334135532379,0.998764753341675,0.00237982394173741,-0.0496334135532379,0.998764753341675,0.00237981602549553,-0.0496340543031693,0.99876469373703,-0.00165171350818127,-0.049464900046587,0.998774528503418,-0.00165171350818127,-0.049464900046587,0.998774528503418,-0.00165323016699404,-0.0494996123015881,0.998772799968719,-0.00165171350818127,-0.049464900046587,0.998774528503418,-0.00165171350818127,-0.049464900046587,0.998774528503418,-0.00165019975975156,-0.0494301877915859,0.998776197433472,0.00166300463024527,-0.0496919415891171,0.998763263225555,0.00166300463024527,-0.0496919415891171,0.998763263225555,0.00166184804402292,-0.0496568456292152,0.998764932155609,0.00166300463024527,-0.0496919415891171,0.998763263225555,0.00166300463024527,-0.0496919415891171,0.998763263225555,
0.00166416121646762,-0.0497270375490189,0.998761475086212,8.03658153358811e-08,1,-1.90035986946668e-10,8.03658153358811e-08,1,-1.90035986946668e-10,-0,1,-0,8.03658153358811e-08,1,-1.90035986946668e-10,8.03658153358811e-08,1,-1.90035986946668e-10,1.60731630671762e-07,1,-3.80071973893337e-10,7.11242122974909e-08,1,1.05675546535622e-07,7.11242122974909e-08,1,1.05675546535622e-07,9.62332681397155e-15,1,-6.47691747579352e-15,7.11242122974909e-08,1,1.05675546535622e-07,7.11242122974909e-08,1,1.05675546535622e-07,1.42248353540708e-07,1,2.11351149914663e-07,1.42311989748123e-07,1,2.11194162602624e-07,1.42311989748123e-07,1,2.11194162602624e-07,1.42311989748123e-07,1,2.11194162602624e-07,1.42311989748123e-07,1,2.11194162602624e-07,1.42311989748123e-07,1,2.11194162602624e-07,1.42311989748123e-07,1,2.11194162602624e-07,8.38838790095942e-11,1,3.25789102362251e-07,8.38838790095942e-11,1,3.25789102362251e-07,-1.34337097001946e-09,1,7.6363741996488e-09,8.38838790095942e-11,1,3.25789102362251e-07,8.38838790095942e-11,1,3.25789102362251e-07,1.51113865864971e-09,1,6.4394185983474e-07,-1.7774419802663e-07,1,2.64351598389112e-07,-1.7774419802663e-07,1,2.64351598389112e-07,-1.77744212237485e-07,1,2.64351598389112e-07,-1.7774419802663e-07,1,2.64351598389112e-07,-1.7774419802663e-07,1,2.64351598389112e-07,-1.7774419802663e-07,1,2.64351598389112e-07,8.44926295595627e-10,1,3.45474603591356e-07,8.44926295595627e-10,1,3.45474603591356e-07,7.54143247849726e-10,1,3.07088498630037e-07,8.44926295595627e-10,1,3.45474603591356e-07,8.44926295595627e-10,1,3.45474603591356e-07,9.35712396454846e-10,1,3.83860680130965e-07,-8.89119604607913e-08,1,1.32077744297021e-07,-8.89119604607913e-08,1,1.32077744297021e-07,-1.77824006186711e-07,1,2.64155431750623e-07,-8.89119604607913e-08,1,1.32077744297021e-07,-8.89119604607913e-08,1,1.32077744297021e-07,0,1,-0,-1.72249481344977e-09,1,7.72944642318807e-12,-1.72249481344977e-09,1,7.72944642318807e-12,-1.72249492447207e-09,1,7.72944729054981e-12,-1.72249481344977e-09,1,7.72944642318807e-12,-1.72249481344977e-09,1,7.72944642318807e-12,
-1.72249470242747e-09,1,7.72944642318807e-12
			} 
			BinormalsW: *168 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *504 {
				a: -0.999999761581421,0,-0.000684762489981949,-0.999999761581421,0,-0.000684762489981949,-0.999999761581421,0,-0.000684762489981949,-0.999999761581421,0,-0.00068453949643299,-0.999999761581421,0,-0.00068453949643299,-0.999999761581421,0,-0.00068453949643299,-1,0,-1.05746994449873e-06,-1,0,-1.05746994449873e-06,-1,0,-1.05746994449873e-06,-1,0,-9.76314368017484e-07,-1,0,-9.76314368017484e-07,-1,0,-9.76314368017484e-07,-0.999999761581421,0,0.000690421962644905,-0.999999761581421,0,0.000690421962644905,-0.999999761581421,0,0.000690421962644905,-0.99944406747818,0.0333326868712902,0.000689918466378003,-0.99944406747818,0.0333326868712902,0.000689918466378003,-0.99944406747818,0.0333326868712902,0.000689918466378003,0.999997198581696,2.62244403970158e-09,-0.00239017466083169,0.999997198581696,2.62244403970158e-09,-0.00239017466083169,0.999997198581696,5.87798645312887e-08,-0.00238992972299457,0.999997198581696,2.62244403970158e-09,-0.00239017466083169,0.999997198581696,2.62244403970158e-09,-0.00239017466083169,0.999997198581696,-5.35188640071738e-08,-0.0023904200643301,-0.999997198581696,6.69783943862967e-08,0.00238997279666364,-0.999997198581696,6.69783943862967e-08,0.00238997279666364,-0.999997198581696,-1.25535857750658e-11,0.00239008874632418,-0.999997198581696,6.69783943862967e-08,0.00238997279666364,-0.999997198581696,6.69783943862967e-08,0.00238997279666364,-0.999997198581696,1.33937135160522e-07,0.00238985707983375,0.00443925010040402,8.47446770535498e-08,0.999990165233612,0.00443925010040402,8.47446770535498e-08,0.999990165233612,0.0044387667439878,-5.22710097428103e-10,0.999990165233612,0.00443925010040402,8.47446770535498e-08,0.999990165233612,0.00443925010040402,8.47446770535498e-08,0.999990165233612,0.00443973392248154,1.70012938838227e-07,0.999990165233612,-0.00443909410387278,1.40366466894193e-07,-0.999990165233612,-0.00443909410387278,1.40366466894193e-07,-0.999990165233612,-0.00443845381960273,2.798170726237e-07,-0.999990165233612,-0.00443909410387278,1.40366466894193e-07,-0.999990165233612,
-0.00443909410387278,1.40366466894193e-07,-0.999990165233612,-0.0044397353194654,9.15872655493644e-10,-0.999990165233612,1,0.00024742295499891,-2.14541024945447e-08,1,0.00024742295499891,-2.14541024945447e-08,1,0.000247390707954764,-1.51227457223513e-08,1,0.00024742295499891,-2.14541024945447e-08,1,0.00024742295499891,-2.14541024945447e-08,1,0.000247455114731565,-2.77897918010694e-08,-0.829420685768127,1.70261771348379e-09,0.558624505996704,-0.829420685768127,1.70261771348379e-09,0.558624505996704,-0.829548597335815,0,0.55843460559845,-0.829420685768127,1.70261771348379e-09,0.558624505996704,-0.829420685768127,1.70261771348379e-09,0.558624505996704,-0.829292833805084,-1.69983060960277e-09,0.558814346790314,0.999444186687469,-0.0333380289375782,2.18890789938087e-08,0.999444186687469,-0.0333380289375782,2.18890789938087e-08,0.999444007873535,-0.0333417765796185,2.22178524467154e-08,0.999444186687469,-0.0333380289375782,2.18890789938087e-08,0.999444186687469,-0.0333380289375782,2.18890789938087e-08,0.999444305896759,-0.0333342812955379,2.17448086203831e-08,0.829728305339813,0,-0.558167576789856,0.829728305339813,0,-0.558167576789856,0.829600632190704,1.70552250100542e-09,-0.558357298374176,0.829728305339813,0,-0.558167576789856,0.829728305339813,0,-0.558167576789856,0.829855978488922,0,-0.557977735996246,0.999437868595123,0.0335256792604923,7.29406091082296e-09,0.999437868595123,0.0335256792604923,7.29406091082296e-09,0.999439358711243,0.0334818884730339,5.63879876125384e-09,0.999437868595123,0.0335256792604923,7.29406091082296e-09,0.999437868595123,0.0335256792604923,7.29406091082296e-09,0.999436438083649,0.0335694700479507,8.94930707318053e-09,-0.829728603363037,1.71972069917814e-09,-0.558167040348053,-0.829728603363037,1.71972069917814e-09,-0.558167040348053,-0.829856276512146,5.15237408293956e-09,-0.557977199554443,-0.829728603363037,1.71972069917814e-09,-0.558167040348053,-0.829728603363037,1.71972069917814e-09,-0.558167040348053,-0.829600870609283,1.72198311165772e-09,-0.558356881141663,0.829420506954193,6.8763825744611e-09,0.558624863624573,
0.829420506954193,6.8763825744611e-09,0.558624863624573,0.829292476177216,0,0.558814764022827,0.829420506954193,6.8763825744611e-09,0.558624863624573,0.829420506954193,6.8763825744611e-09,0.558624863624573,0.829548358917236,0,0.558434963226318,0.99944406747818,-0.0333326868712902,-0.000689918466378003,0.99944406747818,-0.0333326868712902,-0.000689918466378003,0.99944406747818,-0.0333326868712902,-0.000689918466378003,0.999999761581421,0,-0.000690421962644905,0.999999761581421,0,-0.000690421962644905,0.999999761581421,0,-0.000690421962644905,1,0,9.76314368017484e-07,1,0,9.76314368017484e-07,1,0,9.76314368017484e-07,1,0,1.05746994449873e-06,1,0,1.05746994449873e-06,1,0,1.05746994449873e-06,0.999999761581421,0,0.00068453949643299,0.999999761581421,0,0.00068453949643299,0.999999761581421,0,0.00068453949643299,0.999999761581421,0,0.000684762489981949,0.999999761581421,0,0.000684762489981949,0.999999761581421,0,0.000684762489981949,0.999997138977051,0.000364828214515001,-0.00236463034525514,0.999997138977051,0.000364828214515001,-0.00236463034525514,0.999997138977051,0.000364989013178274,-0.00236463034525514,0.999997138977051,0.000364828214515001,-0.00236463034525514,0.999997138977051,0.000364828214515001,-0.00236463034525514,0.999997138977051,0.000364667415851727,-0.00236463034525514,-0.999443054199219,0.0333712995052338,-8.56993196407529e-08,-0.999443054199219,0.0333712995052338,-8.56993196407529e-08,-0.999441623687744,0.0334145575761795,1.70270766375324e-06,-0.999443054199219,0.0333712995052338,-8.56993196407529e-08,-0.999443054199219,0.0333712995052338,-8.56993196407529e-08,-0.999444484710693,0.033328041434288,-1.87392799944064e-06,-0.999439001083374,-0.0334916785359383,-2.19759726860502e-06,-0.999439001083374,-0.0334916785359383,-2.19759726860502e-06,-0.999439120292664,-0.0334878973662853,-1.98985298993648e-06,-0.999439001083374,-0.0334916785359383,-2.19759726860502e-06,-0.999439001083374,-0.0334916785359383,-2.19759726860502e-06,-0.999438881874084,-0.0334954522550106,-2.40515555560705e-06,0.999997198581696,-8.03660427095565e-08,-0.00236463034525514,
0.999997198581696,-8.03660427095565e-08,-0.00236463034525514,0.999997198581696,0,-0.00236463034525514,0.999997198581696,-8.03660427095565e-08,-0.00236463034525514,0.999997198581696,-8.03660427095565e-08,-0.00236463034525514,0.999997198581696,-1.60732085419113e-07,-0.00236463034525514,0.829600751399994,-9.59623192405478e-15,-0.558357119560242,0.829600751399994,-9.59623192405478e-15,-0.558357119560242,0.829600632190704,-1.15999510412229e-14,-0.558357238769531,0.829600751399994,-9.59623192405478e-15,-0.558357119560242,0.829600751399994,-9.59623192405478e-15,-0.558357119560242,0.829600930213928,7.59251365391959e-15,-0.558356881141663,-0.829292833805084,0,0.558814287185669,-0.829292833805084,0,0.558814287185669,-0.829292833805084,0,0.558814287185669,-0.829292833805084,0,0.558814287185669,-0.829292833805084,0,0.558814287185669,-0.829292833805084,0,0.558814287185669,-0.00448602251708508,3.25786203347889e-07,-0.999989926815033,-0.00448602251708508,3.25786203347889e-07,-0.999989926815033,-0.00448602251708508,7.63027063754862e-09,-0.999989926815033,-0.00448602251708508,3.25786203347889e-07,-0.999989926815033,-0.00448602251708508,3.25786203347889e-07,-0.999989926815033,-0.00448602251708508,6.43942144051834e-07,-0.999989926815033,-0.829856336116791,-9.4935613664522e-15,-0.557977139949799,-0.829856336116791,-9.4935613664522e-15,-0.557977139949799,-0.829856395721436,-1.89871244269703e-14,-0.557977139949799,-0.829856336116791,-9.4935613664522e-15,-0.557977139949799,-0.829856336116791,-9.4935613664522e-15,-0.557977139949799,-0.829856336116791,-9.4935613664522e-15,-0.557977139949799,-0.999997198581696,2.80180635636729e-11,0.00236459029838443,-0.999997198581696,2.80180635636729e-11,0.00236459029838443,-0.999997198581696,2.80149046322231e-11,0.00236455025151372,-0.999997198581696,2.80180635636729e-11,0.00236459029838443,-0.999997198581696,2.80180635636729e-11,0.00236459029838443,-0.999997198581696,2.80211496367366e-11,0.00236463034525514,0.829548537731171,0,0.558434665203094,0.829548537731171,0,0.558434665203094,0.829548299312592,-1.89800754187832e-14,0.558434963226318,
0.829548537731171,0,0.558434665203094,0.829548537731171,0,0.558434665203094,0.829548716545105,0,0.558434426784515,0.00448731053620577,0,0.999989926815033,0.00448731053620577,0,0.999989926815033,0.00448731100186706,8.02107268921685e-19,0.999989926815033,0.00448731053620577,0,0.999989926815033,0.00448731053620577,0,0.999989926815033,0.00448731053620577,0,0.999989926815033
			} 
			TangentsW: *168 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *248 {
				a: 44.459888458252,-50.5362701416016,23.4942436218262,-36.4423217773438,44.4068450927734,-38.6788291931152,44.4068450927734,-38.6788291931152,23.4942436218262,-36.4423217773438,23.4661273956299,-24.5848693847656,23.4942436218262,-36.4423217773438,-23.9556217193604,-36.5546607971191,23.4661273956299,-24.5848693847656,23.4661273956299,-24.5848693847656,-23.9556217193604,-36.5546607971191,-23.9837341308594,-24.697208404541,-23.9556217193604,-36.5546607971191,-44.9286804199219,-38.7909965515137,-23.9837341308594,-24.697208404541,-44.8756484985352,-50.6484375,-44.9286804199219,-38.7909965515137,-23.9556217193604,-36.5546607971191,0.234528124332428,0,0.234265834093094,0.760076284408569,0.765092074871063,0.760269999504089,0.76535439491272,0.000193746294826269,0.765092074871063,0,0.23426578938961,0.000193703614058904,0.234528079628944,0.760269939899445,0.76535439491272,0.76007616519928,0.132542744278908,0,0,0.0065867337398231,0.0526940859854221,0.766662955284119,0.185236945748329,0.760076284408569,0,0.760076284408569,0.132542863488197,0.766662895679474,0.18523707985878,0.00658669136464596,0.0526945106685162,0,0.76545524597168,0.29024550318718,0.76513946056366,0.157702788710594,0.234313160181046,0.158958077430725,0.23462900519371,0.291500955820084,0.234528079628944,0.760269939899445,0.23426578938961,0.000193703614058904,0,0.000193703614058904,0.000493711675517261,0.760269939899445,0.76513946056366,0.157702788710594,0.76545524597168,0.29024550318718,1,0.132542565464973,0.999405205249786,0,0.76535439491272,0.000193746294826269,0.765092074871063,0.760269999504089,0.999405205249786,0.760269999504089,0.999899089336395,0.000193746294826269,0.23462900519371,0.291500955820084,0.234313160181046,0.158958077430725,0,0.00125529675278813,0.000594638986513019,0.133798032999039,0.765092074871063,0,0.76535439491272,0.76007616519928,0.999899089336395,0.76007616519928,0.999405205249786,0,0.234265834093094,0.760076284408569,0.234528124332428,0,0.000493754341732711,0,0,0.760076284408569,23.9556217193604,-36.5546607971191,44.9286804199219,
-38.7909965515137,44.8756484985352,-50.6484375,23.9837341308594,-24.697208404541,44.9286804199219,-38.7909965515137,23.9556217193604,-36.5546607971191,23.9837341308594,-24.697208404541,23.9556217193604,-36.5546607971191,-23.4661273956299,-24.5848693847656,-23.4661273956299,-24.5848693847656,23.9556217193604,-36.5546607971191,-23.4942436218262,-36.4423217773438,-23.4661273956299,-24.5848693847656,-23.4942436218262,-36.4423217773438,-44.4068450927734,-38.6788291931152,-44.4068450927734,-38.6788291931152,-23.4942436218262,-36.4423217773438,-44.459888458252,-50.5362701416016,-0,1,-0,0.75,1,0.75,1,1,-21.369104385376,-27.9845390319824,-21.3212394714355,-39.8469276428223,-42.2296142578125,-53.961051940918,-42.3023834228516,-42.0986976623535,20.8662433624268,-39.7455444335938,20.8577194213867,-27.8830642700195,41.7867164611816,-41.9972915649414,41.8201560974121,-53.8598175048828,24.0351390838623,28.086498260498,-23.4148540496826,28.0691814422607,-23.4148540496826,77.7574615478516,24.0351390838623,77.774772644043,58.6095352172852,28.0864944458008,33.3625144958496,28.0864963531494,33.3625144958496,77.774772644043,58.6095275878906,77.774772644043,-39.963493347168,28.6752643585205,-65.1898422241211,28.6752643585205,-65.1898345947266,78.3635406494141,-39.9634857177734,78.3635406494141,49.8676223754883,28.6752681732178,38.0196685791016,28.0864963531494,38.0196533203125,77.774772644043,49.8676071166992,78.3635482788086,64.7750244140625,28.6579475402832,39.5108375549316,28.6579475402832,39.5108261108398,78.3462219238281,64.775016784668,78.3462219238281,23.4150714874268,28.6579399108887,-24.0349216461182,28.6752548217773,-24.0349216461182,78.3635330200195,23.4150714874268,78.3462142944336,-32.8803405761719,28.0691814422607,-58.1238555908203,28.0691814422607,-58.1238441467285,77.7574615478516,-32.8803405761719,77.7574615478516,-38.308277130127,28.0691814422607,-50.1562309265137,28.6579532623291,-50.1562118530273,78.3462295532227,-38.3082580566406,77.7574615478516
			} 
			UVIndex: *168 {
				a: 0,2,1,3,5,4,6,8,7,9,11,10,12,14,13,15,17,16,18,20,19,20,18,21,22,24,23,24,22,25,26,28,27,28,26,29,30,32,31,32,30,33,34,36,35,36,34,37,38,40,39,40,38,41,42,44,43,44,42,45,46,48,47,48,46,49,50,52,51,52,50,53,54,56,55,56,54,57,58,60,59,60,58,61,62,64,63,65,67,66,68,70,69,71,73,72,74,76,75,77,79,78,80,82,81,82,80,83,84,86,85,86,84,87,88,90,89,90,88,91,92,94,93,94,92,95,96,98,97,98,96,99,100,102,101,102,100,103,104,106,105,106,104,107,108,110,109,110,108,111,112,114,113,114,112,115,116,118,117,118,116,119,120,122,121,122,120,123
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *56 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2405848769328, "Model::Armored_Bluemon_Shield", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Rotation", "Lcl Rotation", "", "A",4.524489402771,-53.3284149169922,-5.55533504486084
			P: "Lcl Scaling", "Lcl Scaling", "", "A",0.00999999977648258,0.00999999977648258,0.0100000016391277
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2405849613952, "Material::Interstellar_Armor_Texture", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Material: 2405849611552, "Material::gray_glass", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0.122641362249851,0.122641362249851,0.122641362249851
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0.122641362249851,0.122641362249851,0.122641362249851
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2407160990160, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Materials\Galaxy Related\Skybox 12 31\back.png"
			P: "RelPath", "KString", "XRefUrl", "", "..\Materials\Galaxy Related\Skybox 12 31\back.png"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Materials\Galaxy Related\Skybox 12 31\back.png"
		RelativeFilename: "..\Materials\Galaxy Related\Skybox 12 31\back.png"
	}
	Texture: 2407160999760, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Materials\Galaxy Related\Skybox 12 31\back.png"
		RelativeFilename: "..\Materials\Galaxy Related\Skybox 12 31\back.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Armored_Bluemon_Shield, Model::RootNode
	C: "OO",2405848769328,0
	
	;Material::Interstellar_Armor_Texture, Model::Armored_Bluemon_Shield
	C: "OO",2405849613952,2405848769328
	
	;Material::gray_glass, Model::Armored_Bluemon_Shield
	C: "OO",2405849611552,2405848769328
	
	;Geometry::Scene, Model::Armored_Bluemon_Shield
	C: "OO",2410720323456,2405848769328
	
	;Texture::DiffuseColor_Texture, Material::Interstellar_Armor_Texture
	C: "OO",2407160999760,2405849613952
	
	;Texture::DiffuseColor_Texture, Material::Interstellar_Armor_Texture
	C: "OP",2407160999760,2405849613952, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",2407160990160,2407160999760
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
