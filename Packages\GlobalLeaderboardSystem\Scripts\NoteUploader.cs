using System;
using System.Text;
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.Components;
using VRC.SDK3.StringLoading;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Udon.Common.Interfaces;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
    public class NoteUploader : UdonSharpBehaviour
    {
        [Header("Settings")]
        public int dailyNoteUploadLimit = 999;

        [Header("References")]
        public NotePickup notePickup;
        public InputField copyField;
        public VRCUrlInputField pasteField;
        public Text errorText;

        public GameObject noteUploadingAnimationGameobject;
        public Image noteUploadAnimationPanel;
        public Text noteUploadAnimationText;

        public NoteDownloader noteDownloader;
        public PinboardIdGenerator idGenerator;

        public GameObject dataSendPanel;

        public void UpdateCopyData()
        {
            string pinboardId = idGenerator.pinboardId;
            string localPosition = Vector2ToXYString(notePickup.noteTransform.localPosition);
            string angle = (notePickup.noteTransform.localRotation.eulerAngles.z).ToString();
            string colorHue = ColorHueAsString(notePickup.notePanel.color);
            string hashKey = idGenerator.hashKey;

            string userHash = idGenerator.GetUserHash();
            string hash = UdonHashLib.MD5_UTF8(pinboardId + localPosition + angle + colorHue + userHash + hashKey);

            string content = notePickup.noteText.text;

            string noteUploadURL = $"{idGenerator.GetUploadBaseUrl()}?pinboardId={pinboardId}&localPosition={localPosition}&angle={angle}&colorHue={colorHue}&content={content}&userHash={userHash}&hash={hash}";

            copyField.text = noteUploadURL;
        }

        private static char GetHexValue(int value)
        {
            return (char)(value < 10 ? value + '0' : value - 10 + 'A');
        }


        public void TryToUploadNote()
        {
            VRCUrl url = pasteField.GetUrl();

            pasteField.SetUrl(VRCUrl.Empty);
            if (url.ToString() != copyField.text)
            {
                return;
            }
            VRCStringDownloader.LoadUrl(url, (IUdonEventReceiver)this);

            ActivateNoteUploadAnimation(true);

            dataSendPanel.SetActive(false);
        }

        public void ActivateNoteUploadAnimation(bool value)
        {
            noteUploadingAnimationGameobject.SetActive(value);
            noteUploadingAnimationGameobject.transform.position = notePickup.notePanel.transform.position;
            noteUploadingAnimationGameobject.transform.rotation = notePickup.notePanel.transform.rotation;
            noteUploadAnimationPanel.color = notePickup.notePanel.color;
            noteUploadAnimationText.text = notePickup.noteText.text;
        }


        public override void OnStringLoadSuccess(IVRCStringDownload result)
        {
            string resultJson = result.Result;
            noteDownloader.RequestUpdatedData();
        }

        public override void OnStringLoadError(IVRCStringDownload result)
        {
            ActivateNoteUploadAnimation(false);
            Debug.LogError($"Error loading string: {result.ErrorCode} - {result.Error}");
            errorText.text = $"{result.ErrorCode} - {result.Error}";
        }

        public string Vector2ToXYString(Vector3 vector3) { return $"{vector3.x},{vector3.y}"; }
        public string Vector3ToString(Vector3 vector3) { return $"{vector3.x},{vector3.y},{vector3.z}"; }
        public string ColorToString(Color color) { return $"{color.r},{color.g},{color.b}"; }

        public string ColorHueAsString(Color color)
        {
            Color.RGBToHSV(color, out float h, out float s, out float v);
            return ($"{h}");
        }


        public void OnValidate()
        {
            dailyNoteUploadLimit = Mathf.Min(dailyNoteUploadLimit, 999);
        }
    }
}