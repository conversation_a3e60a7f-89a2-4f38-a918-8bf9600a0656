

using System;
using System.Reflection;
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.Core.Pool;
using VRC.SDK3.Data;
using VRC.SDK3.StringLoading;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Udon.Common.Interfaces;


namespace OnlinePhotoPinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
    public class NoteDownloader : UdonSharpBehaviour
    {
        private int startNoteIndex;

        [Header("References")]
        public PinboardIdGenerator idGenerator;
        public NoteUploader noteUploader;
        public NoteSystem noteSystem;
        public PhotoDownloader photoDownloader;
        public Text errorText;
        public GameObject unauthorizedErrorInfo;
        public GameObject uploadLimitExceedeedInfo;
        public Text uploadLimitMessage;
        public Button newNoteButton;
        public PhotoSerializer photoSerializer;
        public ImageApprover imageApprover;
        public PinboardSettings pinboardSettings;
        public int dailyPictureLimit = 1;


        [HideInInspector] public VRCUrl noteDownloadURL;
        [HideInInspector] public VRCUrl pinboardCreateURL;


        [HideInInspector] public DataDictionary notesDataDictionary;
        [HideInInspector]
        [UdonSynced, FieldChangeCallback(nameof(SyncedJsonChange))] public string syncedJson;

        private bool localPlayerHasEnabledUntrustedUrls = true;

        private string localUserHash;

        [HideInInspector] public bool fetchImageAgain;
        bool createdPinboard;

        void Start()
        {
            newNoteButton.interactable = false;
            localUserHash = idGenerator.GetUserHash();
            if (Networking.IsOwner(gameObject))
            {
                DownloadPhotoData();
            }
        }

        public string SyncedJsonChange
        {
            get => syncedJson;
            set
            {
                noteUploader.ActivateNoteUploadAnimation(false);
                syncedJson = value;
                if (VRCJson.TryDeserializeFromJson(syncedJson, out DataToken result))
                {
                    //Debug.Log($"Was success!! {result.TokenType}");
                    ProcessNoteData(result.DataDictionary);
                }
                else
                {
                    Debug.LogError(result.ToString());
                }
            }

        }

        public override void OnPlayerJoined(VRCPlayerApi player)
        {
            if(Networking.IsOwner(gameObject) && !localPlayerHasEnabledUntrustedUrls)
            {
                GiveOwnershipToNextPlayer();
            } else if (Networking.IsOwner(gameObject)) {
                RequestSerialization();
            }
        }

        public void GiveOwnershipToNextPlayer()
        {
            VRCPlayerApi[] players = new VRCPlayerApi[VRCPlayerApi.GetPlayerCount()];
            VRCPlayerApi.GetPlayers(players);

            for (int i = 0;i < players.Length;i++)
            {
                for(int j=i+1;j<players.Length;j++)
                {
                    if (players[i].playerId > players[j].playerId)
                    {
                        VRCPlayerApi p = players[i];
                        players[i] = players[j];
                        players[j] = p;
                    }
                }
            }

            for (int i = 0; i < players.Length;i++)
            {
                VRCPlayerApi player = players[i];
                if (player.playerId > Networking.LocalPlayer.playerId)
                {
                    if (!player.IsValid()) continue;
                    Networking.SetOwner(player, gameObject);
                    break;
                }
            }
        }

        public override void OnOwnershipTransferred(VRCPlayerApi player)
        {
            if (!player.isLocal) return;
            if (syncedJson.Length == 0)
            {
                DownloadPhotoData();
            }
        }

        public void DownloadPhotoData()
        {
            //Debug.Log($"Attempting to download photo data from URL: {noteDownloadURL}");
            VRCStringDownloader.LoadUrl(noteDownloadURL, (IUdonEventReceiver)this);
        }

        public void CreatePinboard()
        {
            //Debug.Log($"Attempting to create pinboard from URL: {pinboardCreateURL}");
            createdPinboard = true;
            VRCStringDownloader.LoadUrl(pinboardCreateURL, (IUdonEventReceiver)this);
        }

        public override void OnStringLoadSuccess(IVRCStringDownload result)
        {
            if(createdPinboard)
            {
                createdPinboard = false;
                DownloadPhotoData();
                return;
            }
            unauthorizedErrorInfo.SetActive(false);
            string resultJson = result.Result;

            SetJsonData(resultJson);
        }

        public void SetJsonData(string newJson)
        {
            SetProgramVariable(nameof(syncedJson), newJson);
            RequestSerialization();
        }

        public override void OnStringLoadError(IVRCStringDownload result)
        {
            //Code for if pinboard has not been created yet
            if (result.ErrorCode == 418)
            {
                fetchImageAgain = true;
                CreatePinboard();
                return;
            }
            Debug.LogError($"Error loading string: {result.ErrorCode} - {result.Error}");
            errorText.text = $"{result.ErrorCode} - {result.Error}";

            //Allow untrusted urls is not on
            if(result.ErrorCode == 401)
            {
                localPlayerHasEnabledUntrustedUrls = false;
                GiveOwnershipToNextPlayer();
                unauthorizedErrorInfo.SetActive(true);
            }
        }

        public void SetAllNotesToApproved()
        {
            SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(SetAllNotesToApprovedOwner));
        }

        public void SetAllNotesToApprovedOwner()
        {
            if (!VRCJson.TryDeserializeFromJson(syncedJson, out DataToken result))
            {
                Debug.LogWarning($"TryDeserializeFromJson failed!!");
                return;
            }
            DataDictionary imageDataDictionary = result.DataDictionary;

            if (!imageDataDictionary.TryGetValue("imageData", TokenType.DataList, out DataToken imageDataListToken))
            {
                Debug.LogError($"[{name}] Couldn't get imageData from imageDataDictionary, Tokentype: {imageDataListToken}");
                return;
            }
            DataList notesDataList = imageDataListToken.DataList;

            for (int i = 0; i < notesDataList.Count; i++)
            {
                if (!notesDataList.TryGetValue(i, TokenType.DataDictionary, out DataToken noteToken))
                {
                    Debug.LogError($"[{name}] Couldn't get noteToken from notesDataList, Tokentype: {noteToken}");
                    return;
                }
                DataDictionary tokenDictionary = noteToken.DataDictionary;

                if (!tokenDictionary.TryGetValue("status", TokenType.String, out DataToken statusToken))
                {
                    Debug.LogError($"[{name}] Couldn't get noteToken from notesDataList, Tokentype: {statusToken}");
                    return;
                }
                if (statusToken.String == "not-approved")
                {
                    tokenDictionary.SetValue("status", "approved");
                }
                notesDataList.SetValue(i, tokenDictionary);
            }
            imageDataDictionary.SetValue("imageData", notesDataList);

            if (!VRCJson.TrySerializeToJson(imageDataDictionary, JsonExportType.Minify, out DataToken newJsonSting))
            {
                Debug.LogError($"[{name}] failed to serialize to json, Tokentype: {newJsonSting}");
                return;
            }
            SetJsonData(newJsonSting.String);
        }

        public void DeleteImageAtIndex(int index)
        {
            if (!VRCJson.TryDeserializeFromJson(syncedJson, out DataToken result))
            {
                Debug.LogWarning($"TryDeserializeFromJson failed!!");
                return;
            }
            DataDictionary imageDataDictionary = result.DataDictionary;

            if (!imageDataDictionary.TryGetValue("imageData", TokenType.DataList, out DataToken imageDataListToken))
            {
                Debug.LogError($"[{name}] Couldn't get imageData from imageDataDictionary, Tokentype: {imageDataListToken}");
                return;
            }
            DataList notesDataList = imageDataListToken.DataList;

            if(!notesDataList.TryGetValue(index, TokenType.DataDictionary, out DataToken noteToken))
            {
                Debug.LogError($"[{name}] Couldn't get noteToken from notesDataList, Tokentype: {noteToken}");
                return;
            }
            DataDictionary tokenDictionary = noteToken.DataDictionary;
            tokenDictionary.SetValue("status", "deleted");

            notesDataList.SetValue(index, tokenDictionary);
            imageDataDictionary.SetValue("imageData", notesDataList);

            if(!VRCJson.TrySerializeToJson(imageDataDictionary, JsonExportType.Minify, out DataToken newJsonSting))
            {
                Debug.LogError($"[{name}] failed to serialize to json, Tokentype: {newJsonSting}");
                return;
            }
            SetJsonData(newJsonSting.String);
        }




        public void ProcessNoteData(DataDictionary imageDataDictionary)
        {
            newNoteButton.interactable = true;
            //DataDictionary imageDataDictionary = GetDictionaryListFromJSON(jsonData);

            if (!imageDataDictionary.TryGetValue("imageData", TokenType.DataList, out DataToken imageDataListToken))
            {
                Debug.LogError($"[{name}] Couldn't get imageData from imageDataDictionary, Tokentype: {imageDataListToken}");
                return;
            }
            if (!imageDataDictionary.TryGetValue("index", TokenType.Double, out DataToken indexToken))
            {
                Debug.LogError($"[{name}] Couldn't get index from imageDataDictionary");
                return;
            }

            DataList notesDataList = imageDataListToken.DataList;
            startNoteIndex = (int)indexToken.Double;


            //Debug.Log($"count and index {notesDataList.Count} {startNoteIndex}");
            if (notesDataList == null)
            {
                Debug.LogWarning($"[{name}] Could not get DataList from json.");
                return;
            }


            for (int i = 0; i < notesDataList.Count; i++)
            {
                if(!notesDataList.TryGetValue(i, TokenType.DataDictionary, out DataToken noteDataInfoToken))
                {
                    Debug.LogError($"[{name}] Coulnd't get noteDataInfo from notesDataList. TokenType: {noteDataInfoToken.TokenType}");
                    continue;
                }

                DataDictionary noteDataDictionary = noteDataInfoToken.DataDictionary;

                CreateNoteFromDataDictionary(noteDataDictionary);

                if (noteDataDictionary.TryGetValue("status", TokenType.String, out DataToken statusToken))
                {
                    if (statusToken.String == "not-approved")
                    {
                        imageApprover.EnableApproveButton(true);
                    }
                }

                //photoSerializer.SetPhotoSectionData(noteDataDictionary, i);
            }

            //photoSerializer.SetSyncedDataList()

            int localPlayerNotePostCount = GetLocalPlayerNoteCountPast24Hours(notesDataList, out int cooldownTimeHours);

            if(localPlayerNotePostCount >= dailyPictureLimit)
            {
                newNoteButton.gameObject.SetActive(false);
                uploadLimitExceedeedInfo.SetActive(true);
                uploadLimitMessage.text = $"Cooldown Remaining {cooldownTimeHours} hours left.";
            }
        }

        public long UnixTimeNowSeconds()
        {
            var timeSpan = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0));
            return (long)timeSpan.TotalSeconds;
        }

        public void CreateNoteFromDataDictionary(DataDictionary data)
        {


            if (!data.TryGetValue("localPosition", TokenType.String, out DataToken localPositionToken))
            {
                Debug.LogWarning($"could not parse localPosition");
                return;
            }
            if (!data.TryGetValue("angle", TokenType.String, out DataToken angleToken))
            {
                Debug.LogWarning($"could not parse angle from index");
                return;
            }
            if (!data.TryGetValue("colorHue", TokenType.String, out DataToken colorHueToken))
            {
                Debug.LogWarning($"could not parse color from index");
                return;
            }
            if (!data.TryGetValue("status", TokenType.String, out DataToken statusToken))
            {
                Debug.LogWarning($"could not parse status from index");
                return;
            }
            if (!data.TryGetValue("index", TokenType.Double, out DataToken indexToken))
            {
                Debug.LogWarning($"could not parse status from index");
                return;
            }


            int index = (int)indexToken.Double;
            Vector3 localposition = GetVector3FromString(localPositionToken.String);
            float angle = GetFloatFromString(angleToken.String);
            float hue = GetFloatFromString(colorHueToken.String);
            string status = statusToken.String;
            //string content = contenttoken.string;

            noteSystem.CreateNote(index, startNoteIndex, localposition, angle, hue, status);
        }


        public DataDictionary GetDictionaryListFromJSON(string jsonData)
        {
            if (VRCJson.TryDeserializeFromJson(jsonData, out DataToken result))
            {
                if (result.TokenType == TokenType.DataDictionary)
                {
                    return result.DataDictionary;
                }
                else
                {
                    Debug.LogWarning($"{result.TokenType} No photos added yet");

                }
            }
            return null;
        }


        public float GetFloatFromString(string s)
        {
            if(float.TryParse(s, out float result))
            {
                return result;
            }

            Debug.LogWarning($"Couldn't parse Float from String {s}");
            return 0.0f;
        }




        public Vector3 GetVector3FromString(string s)
        {
            string[] sValues = s.Split(',');

            if (sValues.Length != 2)
            {
                Debug.LogWarning($"Could not parse Vector3 from {s}!");
                return Vector3.zero;
            }

            Vector3 result = new Vector3();
            result.x = float.Parse(sValues[0]);
            result.y = float.Parse(sValues[1]);
            result.z = 0;

            return result;
        }
        public int GetLocalPlayerNoteCountPast24Hours(DataList notesDataList, out int cooldownTimeHours)
        {
            cooldownTimeHours = 0;
            double oneDayinSeconds = 24 * 60 * 60;
            long unixTimeNowSeconds = UnixTimeNowSeconds();
            int localPlayerNoteCountPast24hours = 0;

            for (int i = notesDataList.Count - 1; i >= 0; i--)
            {
                if (notesDataList[i].TokenType != TokenType.DataDictionary)
                {
                    Debug.LogWarning($"Index {i} didn't contain datadictionary!");
                    continue;
                }

                DataDictionary noteData = notesDataList[i].DataDictionary;

                if (!noteData.TryGetValue("userHash", TokenType.String, out DataToken userHashToken))
                {
                    Debug.LogWarning($"Could not parse localPosition from index {i}");
                    continue;
                }
                if (!noteData.TryGetValue("timestamp", TokenType.Double, out DataToken timestampToken))
                {
                    Debug.LogWarning($"Could not parse timestampToken from index {i}");
                    continue;
                }

                string userHash = userHashToken.ToString();
                double timestampSeconds = timestampToken.Double;

                if ((unixTimeNowSeconds - timestampSeconds) > oneDayinSeconds) break;
                if (userHash != localUserHash) continue;

                TimeSpan timeSpan = TimeSpan.FromSeconds(oneDayinSeconds - (unixTimeNowSeconds - timestampSeconds));
                cooldownTimeHours = timeSpan.Hours;

                localPlayerNoteCountPast24hours++;
            }
            return localPlayerNoteCountPast24hours;
        }

        //public override void OnPreSerialization()
        //{
        //    if (VRCJson.TrySerializeToJson(notesDataDictionary, JsonExportType.Minify, out DataToken result))
        //    {
        //        syncedJson = result.String;
        //        Debug.Log("Sssyncing noteDownloader");
        //    }
        //    else
        //    {
        //        Debug.LogError(result.ToString());
        //    }
        //}

        //public override void OnDeserialization()
        //{
        //    Debug.Log("OnDeserialization!!");
        //    if (VRCJson.TryDeserializeFromJson(syncedJson, out DataToken result))
        //    {
        //        Debug.Log("Was success!!");
        //        ProcessNoteData(syncedJson);
        //    }
        //    else
        //    {
        //        Debug.LogError(result.ToString());
        //    }
        //}

        public void DeleteImageAtIndexSync(int index)
        {
            if (index == 0) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex0));
            else if (index == 1) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex1));
            else if (index == 2) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex2));
            else if (index == 3) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex3));
            else if (index == 4) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex4));
            else if (index == 5) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex5));
            else if (index == 6) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex6));
            else if (index == 7) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex7));
            else if (index == 8) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex8));
            else if (index == 9) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex9));
            else if (index == 10) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex10));
            else if (index == 11) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex11));
            else if (index == 12) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex12));
            else if (index == 13) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex13));
            else if (index == 14) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex14));
            else if (index == 15) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex15));
            else if (index == 16) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex16));
            else if (index == 17) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex17));
            else if (index == 18) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex18));
            else if (index == 19) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex19));
            else if (index == 20) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex20));
            else if (index == 21) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex21));
            else if (index == 22) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex22));
            else if (index == 23) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex23));
            else if (index == 24) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex24));
            else if (index == 25) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex25));
            else if (index == 26) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex26));
            else if (index == 27) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex27));
            else if (index == 28) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex28));
            else if (index == 29) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex29));
            else if (index == 30) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex30));
            else if (index == 31) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex31));
            else if (index == 32) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex32));
            else if (index == 33) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex33));
            else if (index == 34) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex34));
            else if (index == 35) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex35));
            else if (index == 36) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex36));
            else if (index == 37) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex37));
            else if (index == 38) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex38));
            else if (index == 39) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex39));
            else if (index == 40) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex40));
            else if (index == 41) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex41));
            else if (index == 42) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex42));
            else if (index == 43) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex43));
            else if (index == 44) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex44));
            else if (index == 45) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex45));
            else if (index == 46) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex46));
            else if (index == 47) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex47));
            else if (index == 48) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex48));
            else if (index == 49) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex49));
            else if (index == 50) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex50));
            else if (index == 51) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex51));
            else if (index == 52) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex52));
            else if (index == 53) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex53));
            else if (index == 54) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex54));
            else if (index == 55) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex55));
            else if (index == 56) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex56));
            else if (index == 57) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex57));
            else if (index == 58) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex58));
            else if (index == 59) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex59));
            else if (index == 60) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex60));
            else if (index == 61) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex61));
            else if (index == 62) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex62));
            else if (index == 63) SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DeleteImageFromIndex63));
        }

        public void DeleteImageFromIndex0() { DeleteImageAtIndex(0); }
        public void DeleteImageFromIndex1() { DeleteImageAtIndex(1); }
        public void DeleteImageFromIndex2() { DeleteImageAtIndex(2); }
        public void DeleteImageFromIndex3() { DeleteImageAtIndex(3); }
        public void DeleteImageFromIndex4() { DeleteImageAtIndex(4); }
        public void DeleteImageFromIndex5() { DeleteImageAtIndex(5); }
        public void DeleteImageFromIndex6() { DeleteImageAtIndex(6); }
        public void DeleteImageFromIndex7() { DeleteImageAtIndex(7); }
        public void DeleteImageFromIndex8() { DeleteImageAtIndex(8); }
        public void DeleteImageFromIndex9() { DeleteImageAtIndex(9); }
        public void DeleteImageFromIndex10() { DeleteImageAtIndex(10); }
        public void DeleteImageFromIndex11() { DeleteImageAtIndex(11); }
        public void DeleteImageFromIndex12() { DeleteImageAtIndex(12); }
        public void DeleteImageFromIndex13() { DeleteImageAtIndex(13); }
        public void DeleteImageFromIndex14() { DeleteImageAtIndex(14); }
        public void DeleteImageFromIndex15() { DeleteImageAtIndex(15); }
        public void DeleteImageFromIndex16() { DeleteImageAtIndex(16); }
        public void DeleteImageFromIndex17() { DeleteImageAtIndex(17); }
        public void DeleteImageFromIndex18() { DeleteImageAtIndex(18); }
        public void DeleteImageFromIndex19() { DeleteImageAtIndex(19); }
        public void DeleteImageFromIndex20() { DeleteImageAtIndex(20); }
        public void DeleteImageFromIndex21() { DeleteImageAtIndex(21); }
        public void DeleteImageFromIndex22() { DeleteImageAtIndex(22); }
        public void DeleteImageFromIndex23() { DeleteImageAtIndex(23); }
        public void DeleteImageFromIndex24() { DeleteImageAtIndex(24); }
        public void DeleteImageFromIndex25() { DeleteImageAtIndex(25); }
        public void DeleteImageFromIndex26() { DeleteImageAtIndex(26); }
        public void DeleteImageFromIndex27() { DeleteImageAtIndex(27); }
        public void DeleteImageFromIndex28() { DeleteImageAtIndex(28); }
        public void DeleteImageFromIndex29() { DeleteImageAtIndex(29); }
        public void DeleteImageFromIndex30() { DeleteImageAtIndex(30); }
        public void DeleteImageFromIndex31() { DeleteImageAtIndex(31); }
        public void DeleteImageFromIndex32() { DeleteImageAtIndex(32); }
        public void DeleteImageFromIndex33() { DeleteImageAtIndex(33); }
        public void DeleteImageFromIndex34() { DeleteImageAtIndex(34); }
        public void DeleteImageFromIndex35() { DeleteImageAtIndex(35); }
        public void DeleteImageFromIndex36() { DeleteImageAtIndex(36); }
        public void DeleteImageFromIndex37() { DeleteImageAtIndex(37); }
        public void DeleteImageFromIndex38() { DeleteImageAtIndex(38); }
        public void DeleteImageFromIndex39() { DeleteImageAtIndex(39); }
        public void DeleteImageFromIndex40() { DeleteImageAtIndex(40); }
        public void DeleteImageFromIndex41() { DeleteImageAtIndex(41); }
        public void DeleteImageFromIndex42() { DeleteImageAtIndex(42); }
        public void DeleteImageFromIndex43() { DeleteImageAtIndex(43); }
        public void DeleteImageFromIndex44() { DeleteImageAtIndex(44); }
        public void DeleteImageFromIndex45() { DeleteImageAtIndex(45); }
        public void DeleteImageFromIndex46() { DeleteImageAtIndex(46); }
        public void DeleteImageFromIndex47() { DeleteImageAtIndex(47); }
        public void DeleteImageFromIndex48() { DeleteImageAtIndex(48); }
        public void DeleteImageFromIndex49() { DeleteImageAtIndex(49); }
        public void DeleteImageFromIndex50() { DeleteImageAtIndex(50); }
        public void DeleteImageFromIndex51() { DeleteImageAtIndex(51); }
        public void DeleteImageFromIndex52() { DeleteImageAtIndex(52); }
        public void DeleteImageFromIndex53() { DeleteImageAtIndex(53); }
        public void DeleteImageFromIndex54() { DeleteImageAtIndex(54); }
        public void DeleteImageFromIndex55() { DeleteImageAtIndex(55); }
        public void DeleteImageFromIndex56() { DeleteImageAtIndex(56); }
        public void DeleteImageFromIndex57() { DeleteImageAtIndex(57); }
        public void DeleteImageFromIndex58() { DeleteImageAtIndex(58); }
        public void DeleteImageFromIndex59() { DeleteImageAtIndex(59); }
        public void DeleteImageFromIndex60() { DeleteImageAtIndex(60); }
        public void DeleteImageFromIndex61() { DeleteImageAtIndex(61); }
        public void DeleteImageFromIndex62() { DeleteImageAtIndex(62); }
        public void DeleteImageFromIndex63() { DeleteImageAtIndex(63); }
    }
}