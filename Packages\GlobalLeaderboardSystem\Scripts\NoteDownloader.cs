using System;
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.Data;
using VRC.SDK3.StringLoading;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Udon.Common.Interfaces;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
    public class NoteDownloader : UdonSharpBehaviour
    {
        [Header("Settings")]
        public int noteLimit = 500;
        public int notesPerFrame = 25;
        int currentKeyIndex;
        int currentNoteIndex;

        [Header("References")]
        public PinboardIdGenerator idGenerator;
        public NoteUploader noteUploader;
        public NoteSystem noteSystem;
        public Text errorText;


        [HideInInspector] public VRCUrl noteDownloadURL;
        [HideInInspector] public VRCUrl pinboardCreateURL;
        [HideInInspector] public DataDictionary notesDataDictionary;
        [HideInInspector] public DataList keys;

        [UdonSynced, FieldChangeCallback(nameof(JsonDataChange))] private string json;

        private bool localPlayerHasEnabledUntrustedUrls = true;

        private string localUserHash;

        void Start()
        {
            localUserHash = idGenerator.GetUserHash();
            if (Networking.IsOwner(gameObject))
            {
                DownloadNoteData();
            }
        }

        private void Update()
        {
            for (int i = 0; i < notesPerFrame; i++)
            {
                if (currentKeyIndex >= keys.Count) return;

                DataToken key = keys[currentKeyIndex];
                CreateNoteFromKey(key);
                currentKeyIndex++;
                currentNoteIndex++;
            }
        }

        public string JsonDataChange
        {
            set
            {
                json = value;
                ProcessNoteData(json);
            }
            get => json;
        }

        public void RequestUpdatedData()
        {
            SendCustomNetworkEvent(NetworkEventTarget.Owner, nameof(DownloadNoteData));
        }

        public void SetJsonData(string newJson)
        {
            SetProgramVariable(nameof(json), newJson);
            RequestSerialization();
        }

        public override void OnPlayerJoined(VRCPlayerApi player)
        {
            if(Networking.IsOwner(gameObject) && !localPlayerHasEnabledUntrustedUrls)
            {
                GiveOwnershipToNextPlayer();
            }
        }

        public void GiveOwnershipToNextPlayer()
        {
            VRCPlayerApi[] players = new VRCPlayerApi[VRCPlayerApi.GetPlayerCount()];
            VRCPlayerApi.GetPlayers(players);

            for (int i = 0;i < players.Length;i++)
            {
                for(int j=i+1;j<players.Length;j++)
                {
                    if (players[i].playerId > players[j].playerId)
                    {
                        VRCPlayerApi p = players[i];
                        players[i] = players[j];
                        players[j] = p;
                    }
                }
            }

            for (int i = 0; i < players.Length;i++)
            {
                VRCPlayerApi player = players[i];
                if (player.playerId > Networking.LocalPlayer.playerId)
                {
                    if (!player.IsValid()) continue;
                    Networking.SetOwner(player, gameObject);
                    break;
                }
            }
        }

        public override void OnOwnershipTransferred(VRCPlayerApi player)
        {
            if (!player.isLocal) return;
            if(json.Length == 0)
            {
                DownloadNoteData();
            }
        }
        public void DownloadNoteData()
        {
            VRCStringDownloader.LoadUrl(noteDownloadURL, (IUdonEventReceiver)this);
        }

        public void CreatePinboard()
        {
            VRCStringDownloader.LoadUrl(pinboardCreateURL, (IUdonEventReceiver)this);
        }

        public override void OnStringLoadSuccess(IVRCStringDownload result)
        {
            string resultJson = result.Result;
            SetJsonData(resultJson);
            //ProcessNoteData(resultJson);
        }

        public override void OnStringLoadError(IVRCStringDownload result)
        {
            //Code for if pinboard has not been created yet
            if (result.ErrorCode == 418)
            {
                CreatePinboard();
                return;
            }
            Debug.LogError($"Error loading string: {result.ErrorCode} - {result.Error}");
            errorText.text = $"{result.ErrorCode} - {result.Error}";

            //Allow untrusted urls is not on
            if(result.ErrorCode == 401)
            {
                localPlayerHasEnabledUntrustedUrls = false;
                GiveOwnershipToNextPlayer();
            }
        }



        public void ProcessNoteData(string jsonData)
        {
            noteUploader.ActivateNoteUploadAnimation(false);
            notesDataDictionary = GetDataDictionaryFromJSON(jsonData);
            if (notesDataDictionary == null)
            {
                Debug.Log("[NoteDownloader] Could not get DataDictionary from json.");
                return;
            }

            keys = notesDataDictionary.GetKeys();
            currentNoteIndex = 0;
            currentKeyIndex = Mathf.Max(0, keys.Count - noteLimit);

            int localPlayerNotePostCount = GetLocalPlayerNoteCountPast24Hours(out int cooldownTimeHours);
        }

        public long UnixTimeNow()
        {
            var timeSpan = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0));
            return (long)timeSpan.TotalMilliseconds;
        }


        public int GetLocalPlayerNoteCountPast24Hours(out int cooldownTimeHours)
        {
            cooldownTimeHours = 0;
            double oneDay = 24 * 60 * 60 * 1000;
            long unixTimeNow = UnixTimeNow();
            int localPlayerNoteCountPast24hours = 0;

            for (int i = keys.Count-1; i >= 0; i--)
            {
                DataToken key = keys[i];
                CreateNoteFromKey(key);

                if (notesDataDictionary[key].TokenType != TokenType.DataDictionary)
                {
                    Debug.LogWarning($"Key {key} didn't contain datadictionary!");
                    continue;
                }
                DataDictionary noteData = notesDataDictionary[key].DataDictionary;

                if (!noteData.TryGetValue("userHash", TokenType.String, out DataToken userHashToken))
                {
                    Debug.LogWarning($"Could not parse localPosition from key {key}");
                    continue;
                }
                if (!noteData.TryGetValue("timestamp", TokenType.Double, out DataToken timestampToken))
                {
                    Debug.LogWarning($"Could not parse timestampToken from key {key}");
                    continue;
                }

                string userHash = userHashToken.ToString();
                double timestamp = timestampToken.Double;

                if ((unixTimeNow - timestamp) > oneDay) break;
                if (userHash != localUserHash) continue;

                TimeSpan timeSpan = TimeSpan.FromMilliseconds(oneDay - (unixTimeNow - timestamp));
                cooldownTimeHours = timeSpan.Hours;

                localPlayerNoteCountPast24hours++;
            }
            return localPlayerNoteCountPast24hours;
        }

        public void CreateNoteFromKey(DataToken key)
        {
            if (notesDataDictionary[key].TokenType != TokenType.DataDictionary)
            {
                Debug.LogWarning($"Key {key} didn't contain datadictionary!");
                return;
            }

            DataDictionary noteData = notesDataDictionary[key].DataDictionary;

            if (!noteData.TryGetValue("localPosition", TokenType.String, out DataToken localPositionToken))
            {
                Debug.LogWarning($"Could not parse localPosition from key {key}");
                return;
            }
            if (!noteData.TryGetValue("angle", TokenType.String, out DataToken angleToken))
            {
                Debug.LogWarning($"Could not parse angle from key {key}");
                return;
            }
            if (!noteData.TryGetValue("colorHue", TokenType.String, out DataToken colorHueToken))
            {
                Debug.LogWarning($"Could not parse color from key {key}");
                return;
            }
            if (!noteData.TryGetValue("content", TokenType.String, out DataToken contentToken))
            {
                Debug.LogWarning($"Could not parse content from key {key}");
                return;
            }

            Vector3 localPosition = GetVector3FromString(localPositionToken.String);
            float angle = GetFloatFromString(angleToken.String);
            float hue = GetFloatFromString(colorHueToken.String);
            string content = contentToken.String;

            noteSystem.CreateNote(currentNoteIndex, localPosition, angle, hue, content);
        }

        public DataDictionary GetDataDictionaryFromJSON(string jsonData)
        {
            if (VRCJson.TryDeserializeFromJson(jsonData, out DataToken result))
            {
                if (result.TokenType == TokenType.DataDictionary)
                {
                    return result.DataDictionary;
                }
            }
            return null;
        }


        public float GetFloatFromString(string s)
        {
            if(float.TryParse(s, out float result))
            {
                return result;
            }

            Debug.LogWarning($"Couldn't parse Float from String {s}");
            return 0.0f;
        }




        public Vector3 GetVector3FromString(string s)
        {
            string[] sValues = s.Split(',');

            if (sValues.Length != 2)
            {
                Debug.LogWarning($"Could not parse Vector3 from {s}!");
                return Vector3.zero;
            }

            Vector3 result = new Vector3();
            result.x = float.Parse(sValues[0]);
            result.y = float.Parse(sValues[1]);
            result.z = 0;

            return result;
        }
    }
}