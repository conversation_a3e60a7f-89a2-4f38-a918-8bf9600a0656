﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicBomb : UdonSharpBehaviour
{
    public GameObject ExplosionObject;
    
    public MeshRenderer Renderer;
    public Rigidbody rb;
    public Collider collider;
    public ParticleSystem[] BonusParticles;
    public int BombType; //0 is deletable, 1 is Reanablable
    public float DeleteDelay = 1f;

    public void OnEnable(){
        rb.velocity = Vector3.zero;
        rb.angularVelocity = Vector3.zero;
        rb.isKinematic = false;
        Renderer.enabled = true;
        if(collider != null){collider.enabled = false;}
        rb.useGravity = true;
        ExplosionObject.SetActive(false);
        gameObject.transform.parent = null;
        SendCustomEventDelayedSeconds(nameof(ActivateCollider), 2f);
    }
    public void ActivateCollider(){if(collider != null){collider.enabled = true;}}

    public void Delete(){
        if(BombType == 0){Destroy(gameObject);}
        else if(BombType == 1){
            gameObject.SetActive(false);
            ExplosionObject.SetActive(false);
        }
    }

    //when collision happens
    public void OnCollisionEnter(Collision collision)
    {
        if(BonusParticles != null){
            for(int i = 0; i < BonusParticles.Length; i++){
                BonusParticles[i].Stop();
            }
        }
        rb.velocity = Vector3.zero;
        rb.angularVelocity = Vector3.zero;
        rb.isKinematic = true;
        Renderer.enabled = false;
        collider.enabled = false;
        rb.useGravity = false;
        ExplosionObject.SetActive(true);
        SendCustomEventDelayedSeconds(nameof(Delete), DeleteDelay);
    }
}
