using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.Continuous)]
public class EdibleCubixGlobal : UdonSharpBehaviour
{
    public VRC_Pickup pickup;

    //Actual Script
    private VRCPlayerApi localPlayer;
    [UdonSynced] public bool HasBeenEaten;
    public int CubixType; //0 is blue, 1 is Green, 2 is Red

    public Transform originalPosition;

    // Reference to the trigger collider
    private Collider triggerCollider;

    //Effects
    public ParticleSystem Particle;
    public GameObject Body;
    public AudioSource EatingSound;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        pickup = GetComponent<VRC_Pickup>();
        triggerCollider = GetComponent<Collider>();

        HasBeenEaten = false;
        if (triggerCollider != null){triggerCollider.isTrigger = true;}
        RequestSerialization();
    }

    public override void OnPickup()
    {
        HasBeenEaten = false;
        RequestSerialization();
    }

    public override void OnDrop(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(BreakCube));}
    public void BreakCube(){
        HasBeenEaten = true;
        pickup.pickupable = false;
        Particle.Play();
        Body.SetActive(false);
        EatingSound.Play();
        RequestSerialization();
        SendCustomEventDelayedSeconds(nameof(ResetPositionOfCube), 0.5f);
        SendCustomEventDelayedSeconds(nameof(ReactivateEffects), 5f);
    }
    public void ResetPositionOfCube(){
        HasBeenEaten = false;
        transform.position = originalPosition.position;
        RequestSerialization();
    }

    public void ReactivateEffects(){
        pickup.pickupable = true;
        Body.SetActive(true);
    }

    public void OnPlayerTriggerStay(VRCPlayerApi player)
    {
        if (player != localPlayer) return;

        if(HasBeenEaten == true){
            if(CubixType == 0){
                float SetWalkSpeedValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;     // Steps of 0.5
                float SetRunSpeedValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;
                float SetStrafeSpeedValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;
                float SetJumpImpulseValue = Mathf.Round(Random.Range(0.1f, 20f) * 2f) / 2f;
                float SetGravityStrengthValue = Mathf.Round(Random.Range(0.1f, 2f) * 10f) / 10f;  // Steps of 0.1

                localPlayer.SetWalkSpeed(SetWalkSpeedValue);
                localPlayer.SetRunSpeed(SetRunSpeedValue);
                localPlayer.SetStrafeSpeed(SetStrafeSpeedValue);
                localPlayer.SetJumpImpulse(SetJumpImpulseValue);
                localPlayer.SetGravityStrength(SetGravityStrengthValue);

                GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
                PlayerWorldStartSystem playerSystem = PlayerSystemObject.GetComponent<PlayerWorldStartSystem>();
                if(playerSystem){
                    int RandomHealth = Random.Range(1, playerSystem.MAXHEALTH+1);
                    playerSystem.HEALTH = RandomHealth;
                }
            }
            if(CubixType == 1){
                localPlayer.SetWalkSpeed(4f);
                localPlayer.SetRunSpeed(7f);
                localPlayer.SetStrafeSpeed(8f);
                localPlayer.SetJumpImpulse(5f);
                localPlayer.SetGravityStrength(1f);

                GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
                PlayerWorldStartSystem playerSystem = PlayerSystemObject.GetComponent<PlayerWorldStartSystem>();
                if(playerSystem){playerSystem.HEALTH = playerSystem.MAXHEALTH;}
            }
            if(CubixType == 2){
                float SetWalkSpeedValue = Mathf.Round(Random.Range(0.1f, 5f) * 2f) / 2f;     // Steps of 0.5
                float SetRunSpeedValue = Mathf.Round(Random.Range(0.1f, 5f) * 2f) / 2f;
                float SetStrafeSpeedValue = Mathf.Round(Random.Range(0.1f, 5f) * 2f) / 2f;
                float SetJumpImpulseValue = Mathf.Round(Random.Range(0.1f, 5f) * 2f) / 2f;
                float SetGravityStrengthValue = Mathf.Round(Random.Range(0.1f, 1f) * 10f) / 10f;  // Steps of 0.1

                localPlayer.SetWalkSpeed(SetWalkSpeedValue);
                localPlayer.SetRunSpeed(SetRunSpeedValue);
                localPlayer.SetStrafeSpeed(SetStrafeSpeedValue);
                localPlayer.SetJumpImpulse(SetJumpImpulseValue);

                GameObject PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
                PlayerWorldStartSystem playerSystem = PlayerSystemObject.GetComponent<PlayerWorldStartSystem>();
                if(playerSystem){
                    int RandomHealth = Random.Range(1, playerSystem.MAXHEALTH/6);
                    playerSystem.HEALTH = RandomHealth;
                }
            }
        }
    }
}
