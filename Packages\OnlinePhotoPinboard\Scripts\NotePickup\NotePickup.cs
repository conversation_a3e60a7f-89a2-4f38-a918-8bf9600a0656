﻿
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.Components;
using VRC.SDKBase;
using VRC.Udon;


namespace OnlinePhotoPinboard
{

    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class NotePickup : UdonSharpBehaviour
    {

        [Header("Note Color")]
        public Color defaultNoteColor;

        [Header("References")]
        public Slider noteColorSlider;
        public Slider noteRotationSlider;
        public Material previewNoteMaterial;

        public Button finishButton;

        public Transform noteTransform;
        public Transform noteEditSettingsTransform;
        public BoxCollider pinboardCanvasBoxCollider;
        public BoxCollider noteBoxCollider;

        public VRCPickup pickup;
        public NoteUploader noteUploader;

        public GameObject dataSendPanel;
        public Transform noteStartLocation;
        public Transform cameraStartLocation;


        public CameraPickup cameraPickup;

        public float distanceFromBoard;

        void Start()
        {
            ShowNote(false);
        }

        private void Update()
        {
            if (!pickup.IsHeld) return;
            MoveToClosestPointInPinboard();
        }

        public void OnValidate()
        {
            previewNoteMaterial.color = defaultNoteColor;

            //box collider size changes for some reason when importing to unity2019
#if UNITY_2019
        pinboardCanvasBoxCollider.size = Vector3.one;
        noteBoxCollider.size = Vector3.one;
#endif
        }

        public override void OnDrop()
        {
            MoveToClosestPointInPinboard();
            transform.position = noteEditSettingsTransform.position;
            transform.rotation = noteEditSettingsTransform.rotation;
        }


        #region UI Inputs

        public void FinishButtonPressed()
        {
            dataSendPanel.SetActive(true);
            SetInteractable(false);
            noteUploader.UpdateCopyData();
        }

        public void BackButtonPressed()
        {
            dataSendPanel.SetActive(false);
            SetInteractable(true);
        }

        public void NoteRotationChange()
        {
            Vector3 newRotation = noteTransform.rotation.eulerAngles;
            newRotation.z = noteRotationSlider.value;
            noteTransform.rotation = Quaternion.Euler(newRotation);
        }

        public void NoteColorChange()
        {
            Color.RGBToHSV(defaultNoteColor, out float h, out float s, out float v);
            Color noteColor = Color.HSVToRGB(noteColorSlider.value, s, v);
            previewNoteMaterial.color = noteColor;
        }

        public void CreateNewNotePressed()
        {
            bool noteEditingActive = noteTransform.gameObject.activeInHierarchy;
            ResetNote();
            ResetCamera();
            ShowNote(!noteEditingActive);
        }

        #endregion

        public void MoveToClosestPointInPinboard()
        {
            Vector3 closestPosition = pinboardCanvasBoxCollider.ClosestPoint(transform.position);

            noteTransform.position = closestPosition;
            noteEditSettingsTransform.position = closestPosition;
            noteEditSettingsTransform.rotation = pinboardCanvasBoxCollider.transform.rotation;

            noteTransform.localPosition += new Vector3(0, 0, -distanceFromBoard);
        }

        public void ResetNote()
        {
            dataSendPanel.SetActive(false);
            ShowNote(false);

            transform.position = noteStartLocation.position;
            MoveToClosestPointInPinboard();
        }

        public void ResetCamera()
        {
            cameraPickup.transform.position = cameraStartLocation.position;
        }


        public void SetInteractable(bool value)
        {

            noteColorSlider.interactable = value;
            noteRotationSlider.interactable = value;
            noteBoxCollider.enabled = value;


        }
        public void ShowNote(bool value)
        {
            noteTransform.gameObject.SetActive(value);
            SetInteractable(value);
            noteEditSettingsTransform.gameObject.SetActive(value);
            finishButton.interactable = false;
            cameraPickup.gameObject.SetActive(value);
        }
    }
}
