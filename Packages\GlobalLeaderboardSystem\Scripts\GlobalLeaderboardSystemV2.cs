using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using VRC.SDK3.Data;
using TMPro;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class GlobalLeaderboardSystemV2 : UdonSharpBehaviour
    {
        public NotePickup NotePickup;
        public Text noteText;
        public TextMeshPro leaderboardDisplay;

        public GameObject notesContainer

        private const string POINTS_KEY = "EmperCoins";

        VRCPlayerApi localPlayer;

        void Start()
        {
            localPlayer = Networking.LocalPlayer;
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 30f);
        }

        public void UpdateCustom()
        {
            int currentPoints = PlayerData.GetInt(Networking.LocalPlayer, POINTS_KEY);
            string Text = localPlayer.displayName + " " + currentPoints.ToString();
            noteText.text = Text;

            //Search for notes with the name Note (0) (CLone) and get each text from within
            foreach (Transform child in notesContainer)
            {
                if (child.name.Contains("Note (0) (Clone)"))
                {
                    TextMeshPro noteText = child.GetComponentInChildren<TextMeshPro>();
                    leaderboardDisplay.text += noteText.text + "\n";
                }
            }

            SendCustomEventDelayedSeconds(nameof(TextFinished), 1f);
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 30f);
        }

        public void TextFinished(){NotePickup.TextFinished();}
    }
}
