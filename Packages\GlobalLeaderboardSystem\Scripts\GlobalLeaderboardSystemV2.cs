using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using VRC.SDK3.Data;
using TMPro;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class GlobalLeaderboardSystemV2 : UdonSharpBehaviour
    {
        public NotePickup NotePickup;
        public Text noteText;
        public TextMeshPro leaderboardDisplay;

        public Button finishButton;

        public Transform notesContainer;

        private const string POINTS_KEY = "BattleTowerFloor";

        VRCPlayerApi localPlayer;

        void Start()
        {
            leaderboardDisplay.text = "Global Battle Tower Floors Leaderboards: \n\n Global Leaderboards are loading!";
            localPlayer = Networking.LocalPlayer;
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 30f);
        }

        public void UpdateCustom()
        {
            int currentPoints = PlayerData.GetInt(Networking.LocalPlayer, POINTS_KEY);
            string Text = localPlayer.displayName + " " + currentPoints.ToString();
            noteText.text = Text;

            leaderboardDisplay.text = "Global Battle Tower Floors Leaderboards: \n\n";

            // Collect all note entries for sorting
            string[] noteEntries = new string[100];
            int entryCount = 0;

            foreach (Transform child in notesContainer)
            {
                if (child.name.Contains("Note (0)(Clone)") && entryCount < noteEntries.Length)
                {
                    Text legacyText = child.GetComponentInChildren<Text>();
                    if (legacyText != null && !string.IsNullOrEmpty(legacyText.text))
                    {
                        noteEntries[entryCount] = legacyText.text;
                        entryCount++;
                    }
                }
            }

            // Sort entries by the complete number at the end of each line (highest first)
            for (int i = 0; i < entryCount - 1; i++)
            {
                for (int j = 0; j < entryCount - i - 1; j++)
                {
                    int score1 = ExtractScoreFromText(noteEntries[j]);
                    int score2 = ExtractScoreFromText(noteEntries[j + 1]);

                    if (score1 < score2) // Sort descending (highest first)
                    {
                        string temp = noteEntries[j];
                        noteEntries[j] = noteEntries[j + 1];
                        noteEntries[j + 1] = temp;
                    }
                }
            }

            // Display sorted entries
            for (int i = 0; i < entryCount; i++){leaderboardDisplay.text += noteEntries[i] + "\n";}

            SendCustomEventDelayedSeconds(nameof(TextFinished), 1f);
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 30f);
        }

        public void TextFinished(){NotePickup.TextFinished();}

        private int ExtractScoreFromText(string text)
        {
            if (string.IsNullOrEmpty(text)) return 0;

            // Find the last complete number in the text
            string numberString = "";
            bool foundNumber = false;

            // Start from the end and work backwards to find the last number
            for (int i = text.Length - 1; i >= 0; i--)
            {
                char c = text[i];
                if (char.IsDigit(c)){numberString = c + numberString; foundNumber = true;}
                else if (foundNumber){break;}
            }

            if (foundNumber && int.TryParse(numberString, out int score)){return score;}

            return 0;
        }
    }
}
