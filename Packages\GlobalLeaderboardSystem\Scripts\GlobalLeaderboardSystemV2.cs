using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using VRC.SDK3.Data;
using TMPro;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class GlobalLeaderboardSystemV2 : UdonSharpBehaviour
    {
        public NotePickup NotePickup;
        public Text noteText;
        public TextMeshPro leaderboardDisplay;

        public Transform notesContainer;

        private const string POINTS_KEY = "EmperCoins";

        VRCPlayerApi localPlayer;

        void Start()
        {
            leaderboardDisplay.text = "Global Empercoins Leaderboards: \n\n Global Leaderboards are loading!";
            localPlayer = Networking.LocalPlayer;
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 30f);
        }

        public void UpdateCustom()
        {
            int currentPoints = PlayerData.GetInt(Networking.LocalPlayer, POINTS_KEY);
            string Text = localPlayer.displayName + " " + currentPoints.ToString();
            noteText.text = Text;

            leaderboardDisplay.text = "Global Empercoins Leaderboards: \n\n";

            foreach (Transform child in notesContainer)
            {
                if (child.name.Contains("Note (0)(Clone)"))
                {
                    //Find Text (Legacy) within Note (0)(Clone)
                    Text legacyText = child.GetComponentInChildren<Text>();
                    
                    //If Text (Legacy) is not null and is not empty, add it to leaderboardDisplay
                    if (legacyText != null && !string.IsNullOrEmpty(legacyText.text))
                    {
                        leaderboardDisplay.text += legacyText.text + "\n";
                    }
                }
            }

            SendCustomEventDelayedSeconds(nameof(TextFinished), 1f);
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 30f);
        }

        public void TextFinished(){NotePickup.TextFinished();}
    }
}
