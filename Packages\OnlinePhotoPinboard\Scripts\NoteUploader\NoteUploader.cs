﻿
using System;
using System.Text;
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.Components;
using VRC.SDK3.Data;
using VRC.SDK3.StringLoading;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Udon.Common.Interfaces;

namespace OnlinePhotoPinboard
{

    [UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
    public class NoteUploader : UdonSharpBehaviour
    {
        [Header("Settings")]


        [Header("References")]
        public NotePickup notePickup;
        public InputField copyField;
        public VRCUrlInputField pasteField;
        public Text errorText;
        public GameObject unauthorizedErrorInfo;

        public GameObject noteUploadingAnimationGameobject;
        public Image noteUploadAnimationPanel;
        public Text noteUploadAnimationText;

        public NoteDownloader noteDownloader;
        public PinboardIdGenerator idGenerator;
        public PhotoTaker photoTaker;

        public PhotoSerializer photoSerializer;


        string pinboardId = "";
        string localPosition = "";
        string angle = "";
        string colorHue = "";
        string hashKey = "";

        string userHash = "";





        public void UpdateCopyData()
        {
            string data = photoTaker.GetPictureData();
            if (data == "") return;

            pinboardId = idGenerator.pinboardId;
            localPosition = Vector2ToXYString(notePickup.noteTransform.localPosition);
            angle = (notePickup.noteTransform.localRotation.eulerAngles.z).ToString();
            colorHue = ColorHueAsString(notePickup.previewNoteMaterial.color);
            hashKey = idGenerator.hashKey;

            userHash = idGenerator.GetUserHash();
            string hash = UdonHashLib.MD5_UTF8(pinboardId + localPosition + angle + colorHue + data + userHash + hashKey);

            string noteUploadURL = $"{idGenerator.GetUploadBaseUrl()}?pinboardId={pinboardId}&localPosition={localPosition}&angle={angle}&colorHue={colorHue}&userHash={userHash}&hash={hash}&data={data}";

            copyField.text = noteUploadURL;
        }
        private static char GetHexValue(int value)
        {
            return (char)(value < 10 ? value + '0' : value - 10 + 'A');
        }


        public void TryToUploadNote()
        {
            VRCUrl url = pasteField.GetUrl();

            pasteField.SetUrl(VRCUrl.Empty);
            if (url.ToString() != copyField.text)
            {
                return;
            }
            //noteDownloader.newNoteButton.interactable = false;
            VRCStringDownloader.LoadUrl(url, (IUdonEventReceiver)this);

            ActivateNoteUploadAnimation(true);
            notePickup.ResetNote();
        }

        public void ActivateNoteUploadAnimation(bool value)
        {
            noteUploadingAnimationGameobject.SetActive(value);
            noteUploadingAnimationGameobject.transform.position = notePickup.noteTransform.position;
            noteUploadingAnimationGameobject.transform.rotation = notePickup.noteTransform.rotation;
            noteUploadAnimationPanel.color = notePickup.previewNoteMaterial.color;
        }


        public override void OnStringLoadSuccess(IVRCStringDownload result)
        {
            string resultJson = result.Result;
            photoSerializer.SyncTakenPhoto();

            //noteDownloader.RequestUpdatedData();
        }

        public override void OnStringLoadError(IVRCStringDownload result)
        {
            ActivateNoteUploadAnimation(false);
            Debug.LogError($"Error loading string: {result.ErrorCode} - {result.Error}");
            errorText.text = $"{result.ErrorCode} - {result.Error}";

            if(result.ErrorCode == 401)
            {
                unauthorizedErrorInfo.SetActive(true);
            }
        }

        public string Vector2ToXYString(Vector3 vector3) { return $"{vector3.x},{vector3.y}"; }
        public string Vector3ToString(Vector3 vector3) { return $"{vector3.x},{vector3.y},{vector3.z}"; }
        public string ColorToString(Color color) { return $"{color.r},{color.g},{color.b}"; }

        public string ColorHueAsString(Color color)
        {
            Color.RGBToHSV(color, out float h, out float s, out float v);
            return ($"{h}");
        }


    }
}