﻿
using UdonSharp;
using UnityEngine;
using VRC.SDK3.Image;
using VRC.SDKBase;
using VRC.SDKBase.Midi;
using VRC.Udon;
using VRC.Udon.Common.Interfaces;

namespace OnlinePhotoPinboard
{
    public class PhotoDownloader : UdonSharpBehaviour
    {
        [HideInInspector] public VRCUrl getPhotoURL;
        private VRCImageDownloader imageDownloader;
        public PhotoSerializer photoSerializer;
        public GameObject unauthorizedErrorInfo;
        public Material preview;

        [HideInInspector] public bool imageReady;
        void Start()
        {
            imageDownloader = new VRCImageDownloader();
            DownloadPhotos();
        }



        public void DownloadPhotos()
        {
            imageDownloader.DownloadImage(getPhotoURL, preview, (IUdonEventReceiver)this);
        }
        public override void OnImageLoadSuccess(IVRCImageDownload result)
        {
            Texture2D image = result.Result;
            image.filterMode = FilterMode.Point;
            photoSerializer.SyncImageData(image);
            imageReady = true;
            unauthorizedErrorInfo.SetActive(false);

        }
        public override void OnImageLoadError(IVRCImageDownload result)
        {
            Debug.LogError($"Error: {result.Error} {result.ErrorMessage}");
            //unauthorizedErrorInfo.SetActive(true);
        }

        public Texture2D GetPhotoFromIndex(int index)
        {
            if (index < 0 || index >= photoSerializer.smallImages.Length)
            {
                Debug.LogWarning($"index {index} out of range 0-{photoSerializer.smallImages.Length - 1}");
                return null;
            }
            return photoSerializer.smallImages[index];
        }

    }
}