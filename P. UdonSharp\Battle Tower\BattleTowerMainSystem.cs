﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BattleTowerMainSystem : UdonSharpBehaviour
{
    public GameObject PlayerSystemObject;

    public int Floor = 1;
    public bool FloorComplete;
    private const string FLOOR_KEY = "BattleTowerFloor";
    private VRCPlayerApi localPlayer;

    //GameObjects
    public GameObject[] Doors, DoorClosed, DoorOpen;
    public GameObject NextFloorSystem;

    public Collider[] Collider; //Next Door Colliders
    public TextMeshPro FloorText;

    //Enemies
    public int EnemyDifficultyPool;
    public int EnemiesPerFloor;
    public GameObject[] EnemyPool1, BossPool1, EnemyPool2, BossPool2, EnemyPool3, BossPool3, EnemyPool4, BossPool4, EnemyPool5, BossPool5, EnemyPool6;
    public GameObject[] RemainingEnemies;
    public int EnemiesRemain;
    public Transform[] EnemySpawnPos;

    public bool IsEnabled = true;

    void Start()
    {
        // Initialize local player reference
        localPlayer = Networking.LocalPlayer;
        // Always start from floor 1 - saved value is only for high score tracking
        Floor = 1;

        PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
    }

    void OnEnable()
    {
        for (int i = 0; i < Doors.Length; i++){Doors[i].SetActive(false);}
        for (int i = 0; i < Collider.Length; i++){Collider[i].enabled = false;}
        int RandomDoor = Random.Range(0, Doors.Length);
        int RandomDoor2 = Random.Range(0, Doors.Length);
        Doors[RandomDoor].SetActive(true);
        Doors[RandomDoor2].SetActive(true);
        Collider[RandomDoor].enabled = true;
        Collider[RandomDoor2].enabled = true;

        if(Floor > 0 && Floor < 10){
            EnemiesPerFloor = 5;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                int RandomEnemy = Random.Range(0, EnemyPool1.Length);
                RemainingEnemies[i] = Instantiate(EnemyPool1[RandomEnemy], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if(Floor == 10){
            EnemiesPerFloor = 3;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                RemainingEnemies[i] = Instantiate(BossPool1[i], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if (Floor > 10 && Floor < 20){
            EnemiesPerFloor = 6;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                int RandomEnemy = Random.Range(0, EnemyPool2.Length);
                RemainingEnemies[i] = Instantiate(EnemyPool2[RandomEnemy], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if(Floor == 20){
            EnemiesPerFloor = 4;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                RemainingEnemies[i] = Instantiate(BossPool2[i], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if (Floor > 20 && Floor < 30){
            EnemiesPerFloor = 8;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                int RandomEnemy = Random.Range(0, EnemyPool3.Length);
                RemainingEnemies[i] = Instantiate(EnemyPool3[RandomEnemy], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if(Floor == 30){
            EnemiesPerFloor = 5;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                RemainingEnemies[i] = Instantiate(BossPool3[i], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if (Floor > 30 && Floor < 40){
            EnemiesPerFloor = 10;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                int RandomEnemy = Random.Range(0, EnemyPool4.Length);
                RemainingEnemies[i] = Instantiate(EnemyPool4[RandomEnemy], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if(Floor == 40){
            EnemiesPerFloor = 3;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                RemainingEnemies[i] = Instantiate(BossPool4[i], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if (Floor > 40 && Floor < 50){
            EnemiesPerFloor = 7;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                int RandomEnemy = Random.Range(0, EnemyPool5.Length);
                RemainingEnemies[i] = Instantiate(EnemyPool5[RandomEnemy], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if(Floor == 50){
            EnemiesPerFloor = 5;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                RemainingEnemies[i] = Instantiate(BossPool5[i], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }
        else if (Floor > 50){
            EnemiesPerFloor = 10;
            RemainingEnemies = new GameObject[EnemiesPerFloor];
            for (int i = 0; i < EnemiesPerFloor; i++){
                int RandomEnemy = Random.Range(0, EnemyPool6.Length);
                RemainingEnemies[i] = Instantiate(EnemyPool6[RandomEnemy], EnemySpawnPos[Random.Range(0, EnemySpawnPos.Length)].position, Quaternion.identity);
            }
        }

        SendCustomEventDelayedSeconds(nameof(SavingFloor), 60f);
        SendCustomEventDelayedSeconds(nameof(CustomUpdate), 1f);
        IsEnabled = true;
        FloorComplete = false;
    }

    void OnDisable()
    {
        IsEnabled = false;
        
        for (int i = 0; i < RemainingEnemies.Length; i++){Destroy(RemainingEnemies[i]);}
    }


    public void CustomUpdate()
    {
        if(IsEnabled){
            EnemiesRemain = 0;
            for (int i = 0; i < RemainingEnemies.Length; i++){if(RemainingEnemies[i] != null){EnemiesRemain++;}}

            if(EnemiesRemain == 0){
                FloorComplete = true;
                FloorText.text = "Floor: " + Floor + " (Complete!)";
                for (int i = 0; i < Doors.Length; i++){
                    DoorClosed[i].SetActive(false);
                    DoorOpen[i].SetActive(true);
                }
                if(NextFloorSystem != null){
                    NextFloorSystem.SetActive(true);
                }

                //GiveCoinsEqualToTheFloor
                if(PlayerSystemObject != null){
                    PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>().AddPoints(Floor/2);
                }
            }
            else{
                FloorText.text = "Floor: " + Floor + " (InProgress)";
                for (int i = 0; i < Doors.Length; i++){
                    DoorClosed[i].SetActive(true);
                    DoorOpen[i].SetActive(false);
                }
                if(NextFloorSystem != null){NextFloorSystem.SetActive(false);}
                SendCustomEventDelayedSeconds(nameof(CustomUpdate), 1f);
            }
        }
    }

    public void SavingFloor()
    {
        // Only save if current floor is higher than saved high score
        int savedHighScore = PlayerData.GetInt(localPlayer, FLOOR_KEY);
        if(Floor > savedHighScore){PlayerData.SetInt(FLOOR_KEY, Floor);}
        if(IsEnabled){SendCustomEventDelayedSeconds(nameof(SavingFloor), 60f);}
    }
}
