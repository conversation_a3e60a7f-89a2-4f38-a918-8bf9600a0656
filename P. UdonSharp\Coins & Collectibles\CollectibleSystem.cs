using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CollectibleSystem : UdonSharpBehaviour
{
    public GameObject MainObject, DecorationObject;
    public GameObject[] EntityObjects;
    public string PlayerDataKey;
    public bool CollectibleOwned;

    //Effects
    public ParticleSystem CollectibleEffect;
    public AudioSource CollectibleAudio;
    public AudioClip CollectibleAudioClip;

    private VRCPlayerApi localPlayer;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;

        int PlayerDataKeyInt = PlayerData.GetInt(Networking.LocalPlayer, PlayerDataKey);

        //Turn ItemX into int (X is the number)
        int ArraySelector = PlayerDataKey.Substring(8);

        for (int i = 0; i < EntityObjects.Length; i++)
        {
            if (i == ArraySelector){EntityObjects[i].SetActive(true);}
            else{EntityObjects[i].SetActive(false);}
        }

        if (PlayerDataKeyInt == 1){CollectibleOwned = true; MainObject.SetActive(false);}
        else{CollectibleOwned = false; MainObject.SetActive(true);}
    }

    public void CloseMainObject(){MainObject.SetActive(false);}

    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player.isLocal)
        {
            if (CollectibleOwned == false)
            {
                CollectibleOwned = true;
                DecorationObject.SetActive(false);
                CollectibleEffect.Play();
                CollectibleAudio.PlayOneShot(CollectibleAudioClip);
                PlayerData.SetInt(PlayerDataKey, 1);

                SendCustomEventDelayedSeconds(nameof(CloseMainObject), 1f);
            }
        }
    }
}
