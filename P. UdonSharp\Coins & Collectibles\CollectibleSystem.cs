using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class CollectibleSystem : UdonSharpBehaviour
{
    public GameObject SpawnMenuSystemObject;

    public GameObject MainObject, DecorationObject;
    public GameObject[] EntityObjects;
    public string PlayerDataKey;
    public int ArraySelector;
    public bool CollectibleOwned;

    //Effects
    public ParticleSystem CollectibleEffect;
    public AudioSource CollectibleAudio;
    public AudioClip CollectibleAudioClip;

    private VRCPlayerApi localPlayer;

    void Start()
    {
        SpawnMenuSystemObject = GameObject.Find("SpawnMenuSystem");
        localPlayer = Networking.LocalPlayer;

        ArraySelector = int.Parse(PlayerDataKey.Substring(4)) - 1;

        for (int i = 0; i < EntityObjects.Length; i++)
        {
            if (i == ArraySelector && EntityObjects[i] != null){EntityObjects[i].SetActive(true);}
            else if(EntityObjects[i] != null){EntityObjects[i].SetActive(false);}
        }

        MainObject.SetActive(false);

        SendCustomEventDelayedSeconds(nameof(RefreshCollectible), 5f);
    }

    public void RefreshCollectible(){
        if (localPlayer == null) return;

        string playerDataValue = PlayerData.GetString(localPlayer, PlayerDataKey);
        CollectibleOwned = !string.IsNullOrEmpty(playerDataValue);

        if (CollectibleOwned){MainObject.SetActive(false);}
        else{MainObject.SetActive(true);}
    }

    public void CloseMainObject(){MainObject.SetActive(false);}

    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if (player.isLocal)
        {
            if (!CollectibleOwned)
            {
                CollectibleOwned = true;
                DecorationObject.SetActive(false);
                CollectibleEffect.Play();
                CollectibleAudio.PlayOneShot(CollectibleAudioClip);

                if(SpawnMenuSystemObject != null){
                    SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
                    if(spawnMenuDataSystem != null){spawnMenuDataSystem.UnlockItem(ArraySelector);}
                }

                SendCustomEventDelayedSeconds(nameof(CloseMainObject), 1f);
            }
        }
    }
}
