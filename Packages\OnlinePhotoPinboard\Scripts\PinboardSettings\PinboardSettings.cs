﻿
using System.Linq;
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;


namespace OnlinePhotoPinboard
{
    public enum Style
    {
        Plastic,
        Fabric,
        Wood
    }

    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class PinboardSettings : UdonSharpBehaviour
    {
        [Header("Main Settings")]
        public bool safeMode;
        public Style style;
        public string[] adminUsernames = new string[] { "[1] Local Player" };

        [Header("References")]
        //public bool showReferences = false;
        public GameObject[] models;
        public GameObject deleteToolGameobject;
        public GameObject imageApproveGameobject;
        public GameObject safeModeInfo;
        void Start()
        {
            bool localPlayerIsAdmin = LocalPlayerIsInAdmin();
            deleteToolGameobject.SetActive(localPlayerIsAdmin);

            if (safeMode)
            {
                imageApproveGameobject.SetActive(localPlayerIsAdmin);
            }
        }



        public bool LocalPlayerIsInAdmin()
        {
            foreach (string username in adminUsernames)
            {
                if (username == Networking.LocalPlayer.displayName) return true;
            }
            return false;
        }


        private void OnValidate()
        {
            SomethingChanged();
        }

        public void SomethingChanged()
        {
            imageApproveGameobject.SetActive(safeMode);
            safeModeInfo.SetActive(safeMode);


            int index = 0;
            foreach (GameObject model in models)
            {
                if (index >= models.Length) break;
                if ((int)style == index) models[index].SetActive(true);
                else models[index].SetActive(false);
                index++;
            }
        }
    }
}
