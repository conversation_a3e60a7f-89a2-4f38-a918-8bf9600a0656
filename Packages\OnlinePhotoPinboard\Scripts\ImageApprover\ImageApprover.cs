
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.StringLoading;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Udon.Common.Interfaces;

namespace OnlinePhotoPinboard
{
    public class ImageApprover : UdonSharpBehaviour
    {
        [HideInInspector] public VRCUrl imageApproveUrl;
        public Button approveAllImagesButton;
        public NoteDownloader noteDownloader;
        public ParticleSystem blinkingParticle;

        public void ApproveAllImages()
        {
            VRCStringDownloader.LoadUrl(imageApproveUrl, (IUdonEventReceiver)this);
            EnableApproveButton(false);
        }
        public override void OnStringLoadSuccess(IVRCStringDownload result)
        {
            string resultJson = result.Result;
            //Debug.Log("Images approved successfully");
            noteDownloader.SetAllNotesToApproved();
        }

        public override void OnStringLoadError(IVRCStringDownload result)
        {
            Debug.LogError($"Error loading string: {result.ErrorCode} - {result.Error}");
        }

        public void EnableApproveButton(bool value)
        {
            if (value) blinkingParticle.Play();
            else blinkingParticle.Stop();

            approveAllImagesButton.interactable = value;
        }
    }
}