%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c333ccfdd0cbdbc4ca30cef2dd6e6b9b, type: 3}
  m_Name: SpawnMenuDataSystem
  m_EditorClassIdentifier: 
  serializedUdonProgramAsset: {fileID: 11400000, guid: 005cf819b782557498a1c7383dab6b0d,
    type: 2}
  udonAssembly: 
  assemblyError: 
  sourceCsScript: {fileID: 11500000, guid: 3c555ca0ad5e50642bbe1d9d2ee5a32f, type: 3}
  scriptVersion: 2
  compiledVersion: 2
  behaviourSyncMode: 1
  hasInteractEvent: 0
  scriptID: -8852017910574287729
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: fieldDefinitions
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[UdonSharp.Compiler.FieldDefinition,
        UdonSharp.Editor]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: Items
    - Name: $v
      Entry: 7
      Data: 2|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: Items
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 3|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.GameObject[], UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 4|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: ItemLockers
    - Name: $v
      Entry: 7
      Data: 5|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: ItemLockers
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 6|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
