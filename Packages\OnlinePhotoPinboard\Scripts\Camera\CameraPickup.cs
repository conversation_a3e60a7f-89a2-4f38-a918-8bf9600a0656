﻿
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

namespace OnlinePhotoPinboard
{
    public class CameraPickup : UdonSharpBehaviour
    {
        public Material notePreviewImageMat;
        public Material cameraScreenMaterial;
        public RenderTexture cameraRenderTexture;

        bool photoPreviewMode;

        public NotePickup notePickup;
        public PhotoTaker photoTaker;

        private void Start()
        {
            cameraScreenMaterial.mainTexture = cameraRenderTexture;
        }


        public override void OnPickupUseDown()
        {
            if (photoPreviewMode)
            {
                photoPreviewMode = false;
                cameraScreenMaterial.mainTexture = cameraRenderTexture;
                return;
            }
            else
            {
                photoPreviewMode = true;
                photoTaker.RequestCapture();
            }
        }


        public void SetPhotoPreviewTexture(Texture2D newTexture)
        {
            cameraScreenMaterial.mainTexture = newTexture;
            notePreviewImageMat.mainTexture = newTexture;
            notePickup.finishButton.interactable = true;
        }
    }
}