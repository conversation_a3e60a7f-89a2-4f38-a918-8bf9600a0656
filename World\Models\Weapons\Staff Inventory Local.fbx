; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 4
		Day: 10
		Hour: 4
		Minute: 58
		Second: 6
		Millisecond: 603
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\8s8r8aom_Staff Inventory Local.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\8s8r8aom_Staff Inventory Local.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2338053343888, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2329017805568, "Geometry::Scene", "Mesh" {
		Vertices: *93 {
			a: 1.99999809265137,-63.7086272239685,1.99997425079346,-1.99999809265137,-63.7086272239685,1.99997425079346,-1.99999809265137,-12.020468711853,1.99999809265137,1.99999809265137,-12.020468711853,1.99999809265137,-1.99999809265137,-63.7086272239685,-2.00002193450928,-1.99999809265137,-12.0203971862793,-1.99999809265137,1.99999809265137,-63.7086272239685,-2.00002193450928,1.99999809265137,-12.0203971862793,-1.99999809265137,1.99999809265137,-66.1176919937134,-2.00002193450928,-1.99999809265137,-66.1176919937134,-2.00002193450928,-1.99999809265137,-66.1176800727844,1.99997425079346,1.99999809265137,-66.1176800727844,1.99997425079346,-7.76841640472412,-64.9131536483765,-2.38418579101562e-05,7.76841640472412,-64.9131536483765,-2.38418579101562e-05,-0,-70.5634832382202,0,-1.99999809265137,-8.49712491035461,1.99999809265137,1.99999809265137,-8.49712491035461,1.99999809265137,1.99999809265137,-8.49705338478088,-1.99999809265137,-1.99999809265137,-8.49705338478088,-1.99999809265137,-5.39411306381226,-12.9289329051971,-5.39411306381226,5.39411306381226,-12.9289329051971,-5.39411306381226,-5.39411306381226,-5.50733208656311,-5.39411306381226,-5.39411306381226,-5.50752282142639,5.39411306381226,5.39411306381226,-5.50733208656311,-5.39411306381226,5.39411306381226,-12.9291296005249,5.39411306381226,5.39411306381226,-5.50752282142639,5.39411306381226,-5.39411306381226,-12.9291296005249,5.39411306381226,-0,-10.258686542511,-10.2121949195862,-0,-10.258823633194,10.2121949195862,10.6464862823486,-10.2587521076202,0,-10.6464862823486,-10.2587580680847,0
		} 
		PolygonVertexIndex: *174 {
			a: 4,12,-2,9,12,-5,1,12,-11,10,12,-10,0,13,-7,11,13,-1,6,13,-9,8,13,-12,8,14,-10,11,14,-9,9,14,-11,10,14,-12,19,27,-21,21,27,-20,20,27,-24,23,27,-22,24,28,-27,25,28,-25,26,28,-23,22,28,-26,20,29,-25,23,29,-21,24,29,-26,25,29,-24,26,30,-20,22,30,-27,19,30,-22,21,30,-23,0,2,-2,2,0,-4,1,5,-5,5,1,-3,6,5,-8,5,6,-5,0,7,-4,7,0,-7,6,9,-5,9,6,-9,1,11,-1,11,1,-11,18,16,-18,16,18,-16,5,20,-8,20,5,-20,18,22,-16,22,18,-22,17,21,-19,21,17,-24,7,24,-4,24,7,-21,16,23,-18,23,16,-26,3,26,-3,26,3,-25,15,25,-17,25,15,-23,2,19,-6,19,2,-27
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *522 {
				a: -0.204405158758163,0.978886425495148,0,-0.204405158758163,0.978886425495148,0,-0.204405158758163,0.978886425495148,0,-0.327584117650986,0,-0.944822072982788,-0.327584117650986,0,-0.944822072982788,-0.327584117650986,0,-0.944822072982788,-0.327584117650986,0,0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.204406142234802,-0.978886187076569,2.91731089419045e-06,-0.204406142234802,-0.978886187076569,2.91731089419045e-06,-0.204406142234802,-0.978886187076569,2.91731089419045e-06,0.204405158758163,0.978886425495148,0,0.204405158758163,0.978886425495148,0,0.204405158758163,0.978886425495148,0,0.327584117650986,0,0.944822013378143,0.327584117650986,0,0.944822013378143,0.327584117650986,0,0.944822013378143,0.327584117650986,0,-0.944822072982788,0.327584117650986,0,-0.944822072982788,0.327584117650986,0,-0.944822072982788,0.204406142234802,-0.978886187076569,2.91731089419045e-06,0.204406142234802,-0.978886187076569,2.91731089419045e-06,0.204406142234802,-0.978886187076569,2.91731089419045e-06,-0,-0.410265028476715,-0.911966383457184,-0,-0.410265028476715,-0.911966383457184,-0,-0.410265028476715,-0.911966383457184,0.911968350410461,-0.410260498523712,1.22267272217869e-06,0.911968350410461,-0.410260498523712,1.22267272217869e-06,0.911968350410461,-0.410260498523712,1.22267272217869e-06,-0.911968350410461,-0.410260498523712,1.22267272217869e-06,-0.911968350410461,-0.410260498523712,1.22267272217869e-06,-0.911968350410461,-0.410260498523712,1.22267272217869e-06,-0,-0.410255968570709,0.911970436573029,-0,-0.410255968570709,0.911970436573029,-0,-0.410255968570709,0.911970436573029,-0,-0.87465512752533,-0.484745711088181,-0,-0.87465512752533,-0.484745711088181,-0,-0.87465512752533,-0.484745711088181,-0.666162669658661,0,-0.745806455612183,-0.666162669658661,0,-0.745806455612183,-0.666162669658661,0,-0.745806455612183,0.666162669658661,0,-0.745806455612183,0.666162669658661,0,-0.745806455612183,0.666162669658661,0,-0.745806455612183,-0,0.712020099163055,-0.70215904712677,
-0,0.712020099163055,-0.70215904712677,-0,0.712020099163055,-0.70215904712677,-0,-0.874650537967682,0.484754025936127,-0,-0.874650537967682,0.484754025936127,-0,-0.874650537967682,0.484754025936127,0.666162669658661,0,0.745806455612183,0.666162669658661,0,0.745806455612183,0.666162669658661,0,0.745806455612183,-0.666162669658661,0,0.745806455612183,-0.666162669658661,0,0.745806455612183,-0.666162669658661,0,0.745806455612183,-0,0.712024033069611,0.70215505361557,-0,0.712024033069611,0.70215505361557,-0,0.712024033069611,0.70215505361557,0.45319014787674,-0.891413867473602,-1.62526193889789e-05,0.45319014787674,-0.891413867473602,-1.62526193889789e-05,0.45319014787674,-0.891413867473602,-1.62526193889789e-05,0.71645724773407,0,-0.697631061077118,0.71645724773407,0,-0.697631061077118,0.71645724773407,0,-0.697631061077118,0.716457188129425,0,0.697631061077118,0.716457188129425,0,0.697631061077118,0.716457188129425,0,0.697631061077118,0.670849442481995,0.741593539714813,1.31113065435784e-05,0.670849442481995,0.741593539714813,1.31113065435784e-05,0.670849442481995,0.741593539714813,1.31113065435784e-05,-0.453189373016357,-0.891414284706116,-1.62526284839259e-05,-0.453189373016357,-0.891414284706116,-1.62526284839259e-05,-0.453189373016357,-0.891414284706116,-1.62526284839259e-05,-0.716457188129425,0,0.697631061077118,-0.716457188129425,0,0.697631061077118,-0.716457188129425,0,0.697631061077118,-0.71645724773407,0,-0.697631061077118,-0.71645724773407,0,-0.697631061077118,-0.71645724773407,0,-0.697631061077118,-0.670849919319153,0.7415931224823,1.31112992676208e-05,-0.670849919319153,0.7415931224823,1.31112992676208e-05,-0.670849919319153,0.7415931224823,1.31112992676208e-05,-0,-4.61263425677316e-07,1,-0,-4.61263425677316e-07,1,-0,-4.61263425677316e-07,1,-0,-4.61263425677316e-07,1,-0,-4.61263425677316e-07,1,-0,-4.61263425677316e-07,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,4.61262828821418e-07,-1,-0,4.61262828821418e-07,-1,-0,4.61262828821418e-07,-1,-0,4.61262828821418e-07,-1,-0,4.61262828821418e-07,-1,-0,4.61262828821418e-07,-1,
1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,-0.965990900993347,0.258576184511185,-0,-0.965990900993347,0.258576184511185,-0,-0.965990900993347,0.258576184511185,-0,-0.965990900993347,0.258576184511185,-0,-0.965990900993347,0.258576184511185,-0,-0.965990900993347,0.258576184511185,0.660981595516205,0.750402092933655,1.34182473630062e-05,0.660981833934784,0.75040191411972,1.32670384118683e-05,0.660981714725494,0.750401973724365,1.33426428874373e-05,0.660981833934784,0.75040191411972,1.32670384118683e-05,0.660981595516205,0.750402092933655,1.34182473630062e-05,0.660981714725494,0.750401973724365,1.33426428874373e-05,-0,0.750395476818085,0.660989165306091,-0,0.75039541721344,0.660989165306091,-0,0.75039541721344,0.660989165306091,-0,0.75039541721344,0.660989165306091,-0,0.750395476818085,0.660989165306091,-0,0.75039541721344,0.660989165306091,-0.258592277765274,-0.965986549854279,-1.72732015926158e-05,-0.258592963218689,-0.965986371040344,-1.76122575794579e-05,-0.25859260559082,-0.965986490249634,-1.74427295860369e-05,-0.258592963218689,-0.965986371040344,-1.76122575794579e-05,-0.258592277765274,-0.965986549854279,-1.72732015926158e-05,-0.25859260559082,-0.965986490249634,-1.74427295860369e-05,-0.660981893539429,0.75040191411972,1.34182446345221e-05,-0.660981714725494,0.750401973724365,1.32670402308577e-05,-0.660981774330139,0.750401973724365,1.33426419779425e-05,-0.660981714725494,0.750401973724365,1.32670402308577e-05,-0.660981893539429,0.75040191411972,1.34182446345221e-05,-0.660981774330139,0.750401973724365,1.33426419779425e-05,-0,-0.965981960296631,-0.258609384298325,-0,-0.965981960296631,-0.258609414100647,-0,-0.965981960296631,-0.258609414100647,-0,-0.965981960296631,-0.258609414100647,-0,-0.965981960296631,-0.258609384298325,-0,-0.965981960296631,-0.258609414100647,-0,0.750408530235291,-0.660974323749542,
-0,0.750408530235291,-0.660974383354187,-0,0.750408530235291,-0.660974383354187,-0,0.750408530235291,-0.660974383354187,-0,0.750408530235291,-0.660974323749542,-0,0.750408530235291,-0.660974383354187,0.258593320846558,-0.965986311435699,-1.72731997736264e-05,0.25859260559082,-0.965986490249634,-1.76122593984473e-05,0.258592963218689,-0.965986371040344,-1.74427295860369e-05,0.25859260559082,-0.965986490249634,-1.76122593984473e-05,0.258593320846558,-0.965986311435699,-1.72731997736264e-05,0.258592963218689,-0.965986371040344,-1.74427295860369e-05
			} 
			NormalsW: *174 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *522 {
				a: 0,-0,1.00000011920929,0,-0,1.00000011920929,0,-0,1.00000011920929,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,5.96316283463239e-07,2.85571536551288e-06,1,5.96316283463239e-07,2.85571536551288e-06,1,5.96316283463239e-07,2.85571536551288e-06,1,0,-0,1.00000011920929,0,-0,1.00000011920929,0,-0,1.00000011920929,-0,1,-0,-0,1,-0,-0,1,-0,0,1,0,0,1,0,0,1,0,-5.96316283463239e-07,2.85571536551288e-06,1,-5.96316283463239e-07,2.85571536551288e-06,1,-5.96316283463239e-07,2.85571536551288e-06,1,0,0.911966383457184,-0.410265028476715,0,0.911966383457184,-0.410265028476715,0,0.911966383457184,-0.410265028476715,0.410260498523712,0.911968350410461,2.62356167013422e-07,0.410260498523712,0.911968350410461,2.62356167013422e-07,0.410260498523712,0.911968350410461,2.62356167013422e-07,-0.410260498523712,0.911968350410461,2.62356167013422e-07,-0.410260498523712,0.911968350410461,2.62356167013422e-07,-0.410260498523712,0.911968350410461,2.62356167013422e-07,-0,0.911970436573029,0.410255968570709,-0,0.911970436573029,0.410255968570709,-0,0.911970436573029,0.410255968570709,0,-0.484745711088181,0.87465512752533,0,-0.484745711088181,0.87465512752533,0,-0.484745711088181,0.87465512752533,0,1,-0,0,1,-0,0,1,-0,0,1,0,0,1,0,0,1,0,0,0.70215904712677,0.712020099163055,0,0.70215904712677,0.712020099163055,0,0.70215904712677,0.712020099163055,-0,0.484754025936127,0.874650537967682,-0,0.484754025936127,0.874650537967682,-0,0.484754025936127,0.874650537967682,-0,1,-0,-0,1,-0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0.70215505361557,0.712024033069611,0,-0.70215505361557,0.712024033069611,0,-0.70215505361557,0.712024033069611,7.36552692615078e-06,-1.44878104038071e-05,1,7.36552692615078e-06,-1.44878104038071e-05,1,7.36552692615078e-06,-1.44878104038071e-05,1,0,1.00000011920929,0,0,1.00000011920929,0,0,1.00000011920929,0,-0,1,-0,-0,1,-0,-0,1,-0,-8.79571325640427e-06,-9.72326142800739e-06,1,-8.79571325640427e-06,-9.72326142800739e-06,1,-8.79571325640427e-06,-9.72326142800739e-06,1,-7.36551828595111e-06,-1.4487825865217e-05,1,-7.36551828595111e-06,-1.4487825865217e-05,1,
-7.36551828595111e-06,-1.4487825865217e-05,1,0,1,-0,0,1,-0,0,1,-0,0,1.00000011920929,-0,0,1.00000011920929,-0,0,1.00000011920929,-0,8.79571507539367e-06,-9.72325051407097e-06,1,8.79571507539367e-06,-9.72325051407097e-06,1,8.79571507539367e-06,-9.72325051407097e-06,1,-0,1,4.61263425677316e-07,-0,1,4.61263425677316e-07,-0,1,4.61263425677316e-07,-0,1,4.61263425677316e-07,-0,1,4.61263425677316e-07,-0,1,4.61263425677316e-07,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,4.61262828821418e-07,0,1,4.61262828821418e-07,0,1,4.61262828821418e-07,0,1,4.61262828821418e-07,0,1,4.61262828821418e-07,0,1,4.61262828821418e-07,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,-0,0.258576184511185,0.965990900993347,-0,0.258576184511185,0.965990900993347,-0,0.258576184511185,0.965990900993347,-0,0.258576184511185,0.965990900993347,-0,0.258576184511185,0.965990900993347,-0,0.258576184511185,0.965990900993347,-8.86921588971745e-06,-1.00690804174519e-05,1,-8.76927151693963e-06,-9.95561094896402e-06,1,-8.81924370332854e-06,-1.00123461379553e-05,1,-8.76927151693963e-06,-9.95561094896402e-06,1,-8.86921588971745e-06,-1.00690804174519e-05,1,-8.81924370332854e-06,-1.00123461379553e-05,1,0,-0.660989165306091,0.750395476818085,0,-0.660989165306091,0.75039541721344,0,-0.660989165306091,0.75039541721344,0,-0.660989165306091,0.75039541721344,0,-0.660989165306091,0.750395476818085,0,-0.660989165306091,0.75039541721344,-4.46671674581012e-06,-1.66856807481963e-05,1,-4.55440567748155e-06,-1.70132007042412e-05,1,-4.51056121164584e-06,-1.68494407262187e-05,1,-4.55440567748155e-06,-1.70132007042412e-05,1,-4.46671674581012e-06,-1.66856807481963e-05,1,-4.51056121164584e-06,-1.68494407262187e-05,1,8.86921679921215e-06,-1.00690749604837e-05,1,8.76927060744492e-06,-9.95561458694283e-06,1,8.81924370332854e-06,-1.00123443189659e-05,1,8.76927060744492e-06,-9.95561458694283e-06,1,
8.86921679921215e-06,-1.00690749604837e-05,1,8.81924370332854e-06,-1.00123443189659e-05,1,0,-0.258609384298325,0.965981960296631,0,-0.258609414100647,0.965981960296631,0,-0.258609414100647,0.965981960296631,0,-0.258609414100647,0.965981960296631,0,-0.258609384298325,0.965981960296631,0,-0.258609414100647,0.965981960296631,0,0.660974323749542,0.750408530235291,0,0.660974383354187,0.750408530235291,0,0.660974383354187,0.750408530235291,0,0.660974383354187,0.750408530235291,0,0.660974323749542,0.750408530235291,0,0.660974383354187,0.750408530235291,4.4667335714621e-06,-1.66856752912281e-05,1,4.55440022051334e-06,-1.701320434222e-05,1,4.51056666861405e-06,-1.68494389072293e-05,1,4.55440022051334e-06,-1.701320434222e-05,1,4.4667335714621e-06,-1.66856752912281e-05,1,4.51056666861405e-06,-1.68494389072293e-05,1
			} 
			BinormalsW: *174 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *522 {
				a: -0.978886425495148,-0.204405158758163,0,-0.978886425495148,-0.204405158758163,0,-0.978886425495148,-0.204405158758163,0,-0.944822013378143,0,0.327584087848663,-0.944822013378143,0,0.327584087848663,-0.944822013378143,0,0.327584087848663,0.944822013378143,0,0.327584117650986,0.944822013378143,0,0.327584117650986,0.944822013378143,0,0.327584117650986,0.978886187076569,-0.204406142234802,0,0.978886187076569,-0.204406142234802,0,0.978886187076569,-0.204406142234802,0,-0.978886425495148,0.204405158758163,0,-0.978886425495148,0.204405158758163,0,-0.978886425495148,0.204405158758163,0,0.944822013378143,0,-0.327584117650986,0.944822013378143,0,-0.327584117650986,0.944822013378143,0,-0.327584117650986,-0.944822013378143,0,-0.327584087848663,-0.944822013378143,0,-0.327584087848663,-0.944822013378143,0,-0.327584087848663,0.978886187076569,0.204406142234802,0,0.978886187076569,0.204406142234802,0,0.978886187076569,0.204406142234802,0,-1,0,0,-1,0,0,-1,0,0,1.22267329061287e-06,-2.62353836433249e-07,-1,1.22267329061287e-06,-2.62353836433249e-07,-1,1.22267329061287e-06,-2.62353836433249e-07,-1,1.22267329061287e-06,2.62353836433249e-07,1,1.22267329061287e-06,2.62353836433249e-07,1,1.22267329061287e-06,2.62353836433249e-07,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.745806455612183,0,0.666162729263306,-0.745806455612183,0,0.666162729263306,-0.745806455612183,0,0.666162729263306,-0.745806455612183,0,-0.666162729263306,-0.745806455612183,0,-0.666162729263306,-0.745806455612183,0,-0.666162729263306,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,0.745806455612183,0,-0.666162669658661,0.745806455612183,0,-0.666162669658661,0.745806455612183,0,-0.666162669658661,0.745806455612183,0,0.666162669658661,0.745806455612183,0,0.666162669658661,0.745806455612183,0,0.666162669658661,-1,0,0,-1,0,0,-1,0,0,0.891413867473602,0.45319014787674,0,0.891413867473602,0.45319014787674,0,0.891413867473602,0.45319014787674,0,-0.697631061077118,0,-0.71645724773407,-0.697631061077118,0,-0.71645724773407,-0.697631061077118,0,-0.71645724773407,0.697631061077118,0,-0.71645724773407,
0.697631061077118,0,-0.71645724773407,0.697631061077118,0,-0.71645724773407,-0.741593599319458,0.670849502086639,0,-0.741593599319458,0.670849502086639,0,-0.741593599319458,0.670849502086639,0,0.891414284706116,-0.453189373016357,-4.84366073725401e-13,0.891414284706116,-0.453189373016357,-4.84366073725401e-13,0.891414284706116,-0.453189373016357,-4.84366073725401e-13,0.697631061077118,0,0.71645724773407,0.697631061077118,0,0.71645724773407,0.697631061077118,0,0.71645724773407,-0.697631061077118,0,0.71645724773407,-0.697631061077118,0,0.71645724773407,-0.697631061077118,0,0.71645724773407,-0.741593182086945,-0.670849978923798,0,-0.741593182086945,-0.670849978923798,0,-0.741593182086945,-0.670849978923798,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.750402092933655,0.66098165512085,-3.99894960895208e-13,-0.75040191411972,0.660981833934784,0,-0.75040203332901,0.660981774330139,0,-0.75040191411972,0.660981833934784,0,-0.750402092933655,0.66098165512085,-3.99894960895208e-13,-0.75040203332901,0.660981774330139,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.965986549854279,-0.258592277765274,0,0.965986371040344,-0.258592963218689,0,0.965986490249634,-0.25859260559082,5.19833850444462e-13,0.965986371040344,-0.258592963218689,0,0.965986549854279,-0.258592277765274,0,0.965986490249634,-0.25859260559082,5.19833850444462e-13,-0.750401854515076,-0.660981893539429,3.99894852474991e-13,-0.75040203332901,-0.660981714725494,-3.95388610090544e-13,-0.750401973724365,-0.660981774330139,1.19292509898061e-12,-0.75040203332901,-0.660981714725494,-3.95388610090544e-13,-0.750401854515076,-0.660981893539429,3.99894852474991e-13,-0.750401973724365,-0.660981774330139,1.19292509898061e-12,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,
0.965986311435699,0.258593320846558,5.1478146832068e-13,0.965986490249634,0.25859260559082,0,0.965986371040344,0.258592963218689,5.19833850444462e-13,0.965986490249634,0.25859260559082,0,0.965986311435699,0.258593320846558,5.1478146832068e-13,0.965986371040344,0.258592963218689,5.19833850444462e-13
			} 
			TangentsW: *174 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *288 {
				a: 0.0199999809265137,-0.637086272239685,-0.0199999809265137,-0.637086272239685,-0.0199999809265137,-0.12020467966795,0.0199999809265137,-0.12020467966795,0.0199997425079346,-0.637086272239685,-0.0200002193450928,-0.637086272239685,-0.0199999809265137,-0.120203971862793,0.0199999809265137,-0.12020468711853,0.0199999809265137,-0.637086272239685,-0.0199999809265137,-0.637086272239685,-0.0199999809265137,-0.120203979313374,0.0199999809265137,-0.120203979313374,0.0200002193450928,-0.637086272239685,-0.0199997425079346,-0.637086272239685,-0.0199999809265137,-0.12020468711853,0.0199999809265137,-0.120203971862793,-0.0199999809265137,-0.637086272239685,0.0199999809265137,-0.637086272239685,-0.0199999809265137,-0.661176919937134,0.0199999809265137,-0.661176919937134,-0.0199999809265137,-0.637086272239685,0.0199999809265137,-0.637086272239685,-0.0199999809265137,-0.661176800727844,0.0199999809265137,-0.661176800727844,0.149801433086395,-0.0200002193450928,0.149801433086395,0.0199997425079346,0.208729803562164,-2.38418579101562e-07,0.0123446695506573,-0.661176919937134,0.0123446695506573,-0.637086272239685,0.0733976364135742,-0.649131536483765,-0.0123448241502047,-0.637086272239685,-0.0123448241502047,-0.661176800727844,-0.0733977854251862,-0.649131536483765,0.115570895373821,0.0199978426098824,0.115570917725563,-0.020002119243145,0.0566425174474716,-2.13847783925303e-06,-0.149801433086395,0.0199997425079346,-0.149801433086395,-0.0200002193450928,-0.208729803562164,-2.38418579101562e-07,0.0123448241502047,-0.661176800727844,0.0123448241502047,-0.637086272239685,0.0733977854251862,-0.649131536483765,-0.0123446695506573,-0.637086272239685,-0.0123446695506573,-0.661176919937134,-0.0733976364135742,-0.649131536483765,-0.115570917725563,-0.020002119243145,-0.115570895373821,0.0199978426098824,-0.0566425174474716,-2.13847783925303e-06,-0.0199999809265137,-0.594765722751617,0.0199999809265137,-0.594765722751617,0,-0.643515229225159,-0.0199997164309025,-0.594767153263092,0.0200002454221249,-0.594767272472382,-0,
-0.643516659736633,-0.0200002454221249,-0.594767272472382,0.0199997164309025,-0.594767153263092,0,-0.643516659736633,-0.0199999809265137,-0.594768702983856,0.0199999809265137,-0.594768702983856,0,-0.643518090248108,0.0199999809265137,0.0200015008449554,-0.0199999809265137,0.0200015008449554,-0.0199999809265137,-0.0199984610080719,0.0199999809265137,-0.0199984610080719,-0.0199999809265137,-0.0504016801714897,0.0199999809265137,-0.0504016801714897,-0.0539411306381226,-0.085537776350975,0.0539411306381226,-0.085537776350975,-0.041155930608511,-0.0199989471584558,-0.0411564037203789,0.0200010146945715,0.00407508574426174,-0.0539400987327099,0.004073825199157,0.0539421625435352,-0.0199999809265137,0.0411567054688931,0.0199999809265137,0.0411567054688931,-0.0539411306381226,-0.00407431228086352,0.0539411306381226,-0.00407431228086352,0.0504035316407681,-0.0199980642646551,0.050403717905283,0.0200018975883722,0.0855396315455437,-0.0539392158389091,0.0855401381850243,0.053943045437336,0.0411564335227013,0.0200010146945715,0.0411559604108334,-0.0199989471584558,-0.00407379912212491,0.0539421625435352,-0.00407505966722965,-0.0539400987327099,0.0199999809265137,0.0504056811332703,-0.0199999809265137,0.0504056811332703,0.0539411306381226,0.0855421051383018,-0.0539411306381226,0.0855421051383018,0.0199999809265137,-0.0411556586623192,-0.0199999809265137,-0.0411556586623192,0.0539411306381226,0.00407457305118442,-0.0539411306381226,0.00407457305118442,-0.0504038371145725,0.0200018975883722,-0.0504036508500576,-0.0199980642646551,-0.0855402573943138,0.053943045437336,-0.0855397507548332,-0.0539392158389091,-0.0539411306381226,0.0154925640672445,0.0539411306381226,0.0154925640672445,-0,-0.0395929366350174,0.00429607601836324,-0.0550733208656311,0.00429607601836324,-0.129289329051971,-0.0680298283696175,-0.10258686542511,-0.00429607601836324,-0.129289329051971,-0.00429607601836324,-0.0550733208656311,0.0680298283696175,-0.10258686542511,-0.0539411306381226,-0.0770773962140083,0.0539411306381226,-0.0770773962140083,0,-0.144745171070099,
0.0539411306381226,-0.0154948374256492,-0.0539411306381226,-0.0154948374256492,0,0.0395909585058689,0.00429607601836324,-0.0550752282142639,0.00429607601836324,-0.129291296005249,-0.0680298283696175,-0.10258823633194,-0.00429607601836324,-0.129291296005249,-0.00429607601836324,-0.0550752282142639,0.0680298283696175,-0.10258823633194,0.0539411306381226,0.0770787298679352,-0.0539411306381226,0.0770787298679352,0,0.144746124744415,-0.0105087785050273,-0.0539388619363308,-0.0105096697807312,0.0539433993399143,0.0484126023948193,2.27043847189634e-06,0.00101550575345755,-0.0550733208656311,0.00101550575345755,-0.129289329051971,-0.0742731988430023,-0.102587521076202,-0.00101550261024386,-0.129291296005249,-0.00101550261024386,-0.0550752282142639,0.0742731988430023,-0.102587521076202,-0.0769495815038681,0.0539411902427673,-0.0769483000040054,-0.0539410710334778,-0.147774443030357,6.10508834597567e-08,0.0105095468461514,0.0539433993399143,0.0105086555704474,-0.0539388619363308,-0.048412699252367,2.27043983613839e-06,0.00101550261024386,-0.0550752282142639,0.00101550261024386,-0.129291296005249,-0.0742731988430023,-0.102587580680847,-0.00101550575345755,-0.129289329051971,-0.00101550575345755,-0.0550733208656311,0.0742731988430023,-0.102587580680847,0.076948307454586,-0.0539410710334778,0.0769495815038681,0.0539411902427673,0.14777447283268,6.10501516007389e-08
			} 
			UVIndex: *174 {
				a: 24,26,25,27,29,28,30,32,31,33,35,34,36,38,37,39,41,40,42,44,43,45,47,46,48,50,49,51,53,52,54,56,55,57,59,58,96,98,97,99,101,100,102,104,103,105,107,106,108,110,109,111,113,112,114,116,115,117,119,118,120,122,121,123,125,124,126,128,127,129,131,130,132,134,133,135,137,136,138,140,139,141,143,142,0,2,1,2,0,3,4,6,5,6,4,7,9,11,10,11,9,8,13,15,14,15,13,12,16,19,17,19,16,18,20,23,21,23,20,22,63,61,62,61,63,60,64,67,65,67,64,66,68,71,69,71,68,70,72,75,73,75,72,74,76,79,77,79,76,78,80,83,81,83,80,82,84,87,85,87,84,86,88,91,89,91,88,90,92,95,93,95,92,94
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2340634030016, "Model::Staff_Inventory_Local", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2339809959952, "Material::BlackHole_small", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0,0,0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0,0,0
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Staff_Inventory_Local, Model::RootNode
	C: "OO",2340634030016,0
	
	;Material::BlackHole_small, Model::Staff_Inventory_Local
	C: "OO",2339809959952,2340634030016
	
	;Geometry::Scene, Model::Staff_Inventory_Local
	C: "OO",2329017805568,2340634030016
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
