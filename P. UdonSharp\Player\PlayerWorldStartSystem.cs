using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using UnityEngine.UI;
using System;
using VRC.SDK3.Video.Components.Base;
using VRC.SDK3.Video.Components;
using UdonSharp.Video;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class PlayerWorldStartSystem : UdonSharpBehaviour
{
    //Health Variables
    public int HEALTH = 100,MAXHEALTH = 100, ARMOR = 0, MAXARMOR = 3, ProtectionBoost = 1, DamageReduction = 1, DamageReceived = 1;
    public bool ImmunityFrames, ImmunityFramesConstant, HealImmunityFrames;
    public bool SpecialImmunityFrames ,IsDead, CanChangeHealth;
    public TextMeshProUGUI[] HealthText, ArmorText;
    public Slider[] HealthSlider, ArmorSlider;

    //Class Related
    public int ClassEquipped;
    public DoubleJump DoubleJumpScript;

    //SpeedBoost Related
    public float SpeedMultiplierCache;
    public bool IsSpeedBoosted;
    //BurnBoos Related
    public GameObject FireBoostObject;
    public bool IsFireBoosted, IsAlreadyOnFire;
    //Protection Boost Related
    public GameObject ProtectionBoostObject;
    public bool IsProtected, IsAlreadyProtected;

    //DeathScreen
    public GameObject DeathScreen, RespawnButton, DeathMusic, DeathSpeech, DeathParticles;
    public AudioClip DeathSound;

    //Additional
    public bool Heightmode = true; //true is Human, false is Konrusk
    public bool FriendlyFire = true;
    public GameObject[] FriendlyFireKnockbackObjects;

    //PrisonRelated
    public bool PlayerInPrison;
    public Collider PrisonCollider;
    public int PrisonCooldown;

    //Effects and Sounds
    public AudioSource AudioSource;
    public AudioClip HurtSound, ArmorHurtSound;
    public GameObject HurtIndicator;
    public MeshRenderer HurtMeshRenderer;
    public GameObject LogObject1, LogObject2, LogObject3;
    public LayerMask damageLayer;

    //VideoplayerVolume
    public VideoPlayerManager videoPlayerManager;
    public float VolumeCache;

    //SpawnMenu
    public GameObject[] SpawnMenu;
    public SpawnMenuSystem SpawnMenuSystem;

    //BattleTower
    public BattleTowerMainSystem BattleTowerMainSystem;

    private VRCPlayerApi localPlayer;

    private void Start()
    {
        localPlayer = Networking.LocalPlayer;

        CanChangeHealth = true;

        //Health
        HEALTH = MAXHEALTH;
        for (int i = 0; i < HealthText.Length; i++){
            HealthText[i].text = HEALTH.ToString();
            HealthSlider[i].value = HEALTH;
        }
        //Armor
        ARMOR = MAXARMOR;
        for (int i = 0; i < ArmorText.Length; i++){
            ArmorText[i].text = ARMOR.ToString();
            ArmorSlider[i].value = ARMOR;
        }

        DeathScreen.SetActive(false);
        HurtIndicator.SetActive(false);

        //FriendlyFire knockback objects
        FriendlyFireKnockbackToggle();

        SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.1f);
    }

    #region CustomUpdate
    public void CustomUpdate()
    {
        //LogObject1.SetActive(true);
        if(!IsDead && HEALTH > 0){
            //Health
            for (int i = 0; i < HealthText.Length; i++){
                HealthText[i].text = HEALTH.ToString();
                HealthSlider[i].value = HEALTH;
            }
            //Armor
            for (int i = 0; i < ArmorText.Length; i++){
                ArmorText[i].text = ARMOR.ToString();
                ArmorSlider[i].value = ARMOR;
            }
        }
        if(!IsDead && HEALTH <= 0){HasDiedFunction();}

        //HurtIndicator
        var color = HurtMeshRenderer.material.color;
        if(color.a > 0){color.a -= 0.05f; HurtMeshRenderer.material.color = color;}
        else{HurtIndicator.SetActive(false);}

        if(PrisonCollider != null && PlayerInPrison){
            if(!PrisonCollider.bounds.Contains(localPlayer.GetPosition())){
                localPlayer.TeleportTo(PrisonCollider.transform.position, PrisonCollider.transform.rotation);
                if(HEALTH <= 0){
                    for (int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(true);}

                    // Restore video volume when respawning in prison
                    if(videoPlayerManager != null){
                        videoPlayerManager.SetVolume(VolumeCache);
                    }

                    IsDead = false;
                    DeathScreen.SetActive(false);
                    HEALTH = MAXHEALTH;
                    ARMOR = MAXARMOR;
                }
            }
        }

        //CustomTimer
        if(!IsDead){SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.1f);}
    }
    #endregion



    #region Classes / Modules

    public void Class1(){
        if(CanChangeHealth == true && !IsDead){
            MAXHEALTH = 100;
            for (int i = 0; i < HealthSlider.Length; i++){HealthSlider[i].value = 100;}
            HEALTH = MAXHEALTH;
        }
        ClassEquipped = 0;
        DamageReduction = 1;
        DamageReceived = 1;
        DoubleJumpScript.IsEnabled = false;
        ClassSpeedFunction();
    }
    public void Class2(){
        if(CanChangeHealth == true && !IsDead){
            MAXHEALTH = 125;
            for (int i = 0; i < HealthSlider.Length; i++){HealthSlider[i].value = 100;}
            HEALTH = MAXHEALTH;
        }
        ClassEquipped = 1;
        DamageReduction = 2;
        DamageReceived = 1;
        DoubleJumpScript.IsEnabled = false;
        ClassSpeedFunction();
    }
    public void Class3(){
        if(CanChangeHealth == true && !IsDead){
            MAXHEALTH = 50;
            for (int i = 0; i < HealthSlider.Length; i++){HealthSlider[i].value = 100;}
            HEALTH = MAXHEALTH;
        }
        ClassEquipped = 2;
        DamageReduction = 1;
        DamageReceived = 2;
        DoubleJumpScript.IsEnabled = true;
        ClassSpeedFunction();
    }

    public void ClassSpeedFunction(){
        if(ClassEquipped == 0){
            localPlayer.SetWalkSpeed(4f); 
            localPlayer.SetRunSpeed(7f); 
            localPlayer.SetStrafeSpeed(8f); 
            localPlayer.SetJumpImpulse(5f); 
            localPlayer.SetGravityStrength(1f);
        }
        else if(ClassEquipped == 1){
            localPlayer.SetWalkSpeed(2f); 
            localPlayer.SetRunSpeed(3.5f); 
            localPlayer.SetStrafeSpeed(4f); 
            localPlayer.SetJumpImpulse(3f); 
            localPlayer.SetGravityStrength(1.5f);
            SendCustomEventDelayedSeconds(nameof(ClassSpeedFunction), 0.2f);
        }
        else if(ClassEquipped == 2){
            localPlayer.SetWalkSpeed(8f); 
            localPlayer.SetRunSpeed(14f); 
            localPlayer.SetStrafeSpeed(16f); 
            localPlayer.SetJumpImpulse(10f); 
            localPlayer.SetGravityStrength(1f);
            SendCustomEventDelayedSeconds(nameof(ClassSpeedFunction), 0.2f);
        }
    }
    #endregion

    #region SpeedBoost
    //Speed Boost Stuff
    public void SpeedBoostedFunction(float Speed)
    {
        IsSpeedBoosted = true;
        float SetWalkSpeedValue = localPlayer.GetWalkSpeed();
        float SetRunSpeedValue = localPlayer.GetRunSpeed();
        float SetStrafeSpeedValue = localPlayer.GetStrafeSpeed();
        float SetJumpImpulseValue = localPlayer.GetJumpImpulse();
        float SetGravityStrengthValue = localPlayer.GetGravityStrength();

        localPlayer.SetWalkSpeed(SetWalkSpeedValue*Speed);
        localPlayer.SetRunSpeed(SetRunSpeedValue*Speed);
        localPlayer.SetStrafeSpeed(SetStrafeSpeedValue*Speed);
        localPlayer.SetJumpImpulse(SetJumpImpulseValue*Speed);
        localPlayer.SetGravityStrength(SetGravityStrengthValue*Speed);
        SpeedMultiplierCache = Speed;
        SendCustomEventDelayedSeconds(nameof(SpeedDecreaseFunction), 10f);
    }
    public void SpeedDecreaseFunction()
    {
        IsSpeedBoosted = false;
        float SetWalkSpeedValue = localPlayer.GetWalkSpeed();
        float SetRunSpeedValue = localPlayer.GetRunSpeed();
        float SetStrafeSpeedValue = localPlayer.GetStrafeSpeed();
        float SetJumpImpulseValue = localPlayer.GetJumpImpulse();
        float SetGravityStrengthValue = localPlayer.GetGravityStrength();

        localPlayer.SetWalkSpeed(SetWalkSpeedValue/SpeedMultiplierCache);
        localPlayer.SetRunSpeed(SetRunSpeedValue/SpeedMultiplierCache);
        localPlayer.SetStrafeSpeed(SetStrafeSpeedValue/SpeedMultiplierCache);
        localPlayer.SetJumpImpulse(SetJumpImpulseValue/SpeedMultiplierCache);
        localPlayer.SetGravityStrength(SetGravityStrengthValue/SpeedMultiplierCache);
        SpeedMultiplierCache = 0;
    }
    #endregion

    #region FireBoost
    public void FireBoostedFunction()
    {
        IsFireBoosted = true;
        FireBoostObject.SetActive(true);
        if(!IsAlreadyOnFire){SendCustomEventDelayedSeconds(nameof(FireDecreaseFunction), 30f);}
        IsAlreadyOnFire = true;
    }
    public void FireDecreaseFunction()
    {
        IsFireBoosted = false;
        IsAlreadyOnFire = false;
        FireBoostObject.SetActive(false);
    }
    #endregion

    #region Protection Boost
    public void ProtectionBoostedFunction()
    {
        IsProtected = true;
        ProtectionBoost = 2;
        ProtectionBoostObject.SetActive(true);
        if(!IsAlreadyProtected){SendCustomEventDelayedSeconds(nameof(ProtectionDecreaseFunction), 15f);}
        IsAlreadyProtected = true;
    }
    public void ProtectionDecreaseFunction()
    {
        localPlayer.SetWalkSpeed(4f);
        localPlayer.SetRunSpeed(7f);
        localPlayer.SetStrafeSpeed(8f);
        localPlayer.SetJumpImpulse(5f);
        localPlayer.SetGravityStrength(1f);

        IsProtected = false;
        IsAlreadyProtected = false;
        ProtectionBoostObject.SetActive(false);
        ProtectionBoost = 1;
    }
    #endregion

    #region Health

    public void HurtIndicatorFunction(int DamageColor)
    {
        if(HEALTH > MAXHEALTH){HEALTH = MAXHEALTH;}
        HurtIndicator.SetActive(true);
        var color = HurtMeshRenderer.material.color;
        color = new Color(0, 0, 0, 0.25f);
        if(DamageColor == 0){color.r = 1.0f;} // Red
        else if(DamageColor == 1){color.g = 0.5f;} // Green
        else if(DamageColor == 2){color.b = 0.5f;} // Blue
        else if(DamageColor == 3){color.g = 1.0f;} // Healing
        else if(DamageColor == 4){color.b = 1.0f;} // MovementIncrease
        else if(DamageColor == 5){color.r = 1.0f; color.g = 1.0f;} // Fire
        else if(DamageColor == 6){color.g = 1.0f; color.b = 1.0f;} // Cold
        else if(DamageColor == 7){color.r = 1.0f; color.b = 1.0f;} // Storm
        HurtMeshRenderer.material.color = color;
    }
    public void HasDiedFunction()
    {
        for (int i = 0; i < HealthText.Length; i++){
            HealthText[i].text = "0";
            HealthSlider[i].value = 0;
        }
        var color = HurtMeshRenderer.material.color;
        color.a = 0;
        HurtMeshRenderer.material.color = color;
        IsDead = true;
        AudioSource.PlayOneShot(DeathSound);
        DeathScreen.SetActive(true);
        DeathMusic.SetActive(true);
        DeathSpeech.SetActive(false);
        DeathParticles.SetActive(false);
        RespawnButton.SetActive(true);
        for (int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(false);}

        // Mute video audio during death screen
        if(videoPlayerManager != null){
            VolumeCache = videoPlayerManager.GetVolume();
            videoPlayerManager.SetVolume(0);
        }

        //Floor Reset
        if(BattleTowerMainSystem != null && BattleTowerMainSystem.IsEnabled == true){
            BattleTowerMainSystem.Floor = 1;
            BattleTowerMainSystem.SavingFloor();
            BattleTowerMainSystem.FloorComplete = false;
        }


        if (localPlayer != null){localPlayer.TeleportTo(DeathScreen.transform.position, DeathScreen.transform.rotation);}
        SendCustomEventDelayedSeconds(nameof(CheckIfRespawned), 30f);
    }
    public void CheckIfRespawned()
    {
        if(IsDead == true){
            DeathSpeech.SetActive(true);
            DeathParticles.SetActive(true);
            RespawnButton.SetActive(false);
            DeathMusic.SetActive(false);
            SendCustomEventDelayedSeconds(nameof(HasRespawnedFunction), 5.1f);
        }
    }
    public void HasRespawnedFunction()
    {
        if(IsDead == true){
            IsDead = false;
            DeathScreen.SetActive(false);
            HEALTH = MAXHEALTH;
            ARMOR = MAXARMOR;
            if (localPlayer != null){localPlayer.TeleportTo(new Vector3(0,0,0), new Quaternion(0,0,0,0));}

            if(videoPlayerManager != null){videoPlayerManager.SetVolume(VolumeCache);}

            // Spawn Menu
            for (int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(true);}
            SpawnMenuSystem.CloseMenu();
            SpawnMenuSystem.MinimizedMode = true;
            SpawnMenuSystem.MenuContainer.SetActive(false);
            SpawnMenuSystem.spawnpoint.gameObject.SetActive(false);
            SpawnMenuSystem.MinimizedMenu.SetActive(true);
            SpawnMenuSystem.PickupCollider.enabled = false;

            SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.1f);
        }
    }

    public void DeactivateImmunityFrames(){ImmunityFrames = false;  ImmunityFramesConstant = false;}
    public void DeactivateHealImmunityFrames(){HealImmunityFrames = false;}

    #endregion

    #region Extras

    public void FriendlyFireToggle(){
        FriendlyFire = !FriendlyFire;
        FriendlyFireKnockbackToggle();
    }
    public void FriendlyFireKnockbackToggle(){
        if(FriendlyFire == true){
            for (int i = 0; i < FriendlyFireKnockbackObjects.Length; i++){
                if(FriendlyFireKnockbackObjects[i] != null){
                    FriendlyFireKnockbackObjects[i].SetActive(true);
                }
            }
        }
        else if(FriendlyFire == false){
            for (int i = 0; i < FriendlyFireKnockbackObjects.Length; i++){
                if(FriendlyFireKnockbackObjects[i] != null){
                    FriendlyFireKnockbackObjects[i].SetActive(false);
                }
            }
        }
    }


    public void DeactivatePrisonCooldown(){PrisonCooldown = 0;}

    public void DeleteEverything(){Destroy(gameObject);}
    #endregion

    #region Damage
    public void OnTriggerEnter(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        PlayerDamageInput DamageInput = collision.gameObject.GetComponent<PlayerDamageInput>();
        if(!IsDead && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0)
        {
            if((FriendlyFire == true || !DamageInput.IsFriendlyFireDamage) && !DamageInput.IsHealing && !ImmunityFrames && SpecialImmunityFrames == false){
                if(ARMOR > 0){
                    ARMOR -= 1;

                    if(HEALTH < MAXHEALTH/3){HEALTH -= 0/ProtectionBoost/DamageReduction; AudioSource.PlayOneShot(ArmorHurtSound);}
                    else if(HEALTH < MAXHEALTH/2 && HEALTH >= MAXHEALTH/3){HEALTH -= DamageInput.Damage/4/ProtectionBoost/DamageReduction*DamageReceived; AudioSource.PlayOneShot(ArmorHurtSound);}
                    else if(HEALTH <= MAXHEALTH && HEALTH >= MAXHEALTH/2){HEALTH -= DamageInput.Damage/2/ProtectionBoost/DamageReduction*DamageReceived; AudioSource.PlayOneShot(ArmorHurtSound);}
                }
                else{HEALTH -= DamageInput.Damage/ProtectionBoost/DamageReduction*DamageReceived; AudioSource.PlayOneShot(HurtSound);}
                ImmunityFrames = true;
                HurtIndicatorFunction(DamageInput.DamageColor);
                SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.2f);
            }
            if(DamageInput.IsHealing && !HealImmunityFrames && HEALTH < MAXHEALTH){
                HEALTH += DamageInput.Damage;
                HealImmunityFrames = true;
                HurtIndicatorFunction(3);
                SendCustomEventDelayedSeconds(nameof(DeactivateHealImmunityFrames), 0.4f);
            }
        }
        if(!IsDead && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && DamageInput.IsPrisonWeapon && PrisonCooldown == 0){
            PlayerInPrison = !PlayerInPrison;
            if(PlayerInPrison){localPlayer.TeleportTo(PrisonCollider.transform.position, PrisonCollider.transform.rotation);}
            PrisonCooldown = 1;
            SendCustomEventDelayedSeconds(nameof(DeactivatePrisonCooldown), 1f);
        }
        else if(IsDead && DamageInput != null && DamageInput.DamageType == 0 && DamageInput.Damage != 0 && DamageInput.IsPrisonWeapon && PrisonCooldown == 0){
            PlayerInPrison = true;
            HasRespawnedFunction();
            if(PlayerInPrison){localPlayer.TeleportTo(PrisonCollider.transform.position, PrisonCollider.transform.rotation);}
            PrisonCooldown = 1;
            SendCustomEventDelayedSeconds(nameof(DeactivatePrisonCooldown), 1f);
        }
    }
    public void OnTriggerStay(Collider collision)
    {
        if ((damageLayer.value & (1 << collision.gameObject.layer)) == 0) return;

        PlayerDamageInput DamageInput = collision.gameObject.GetComponent<PlayerDamageInput>();
        if(!IsDead && DamageInput != null && DamageInput.DamageType == 1 && DamageInput.Damage != 0)
        {
            if((FriendlyFire == true || !DamageInput.IsFriendlyFireDamage) && !DamageInput.IsHealing && !ImmunityFramesConstant && SpecialImmunityFrames == false){
                HEALTH -= DamageInput.Damage/ProtectionBoost;
                ImmunityFramesConstant = true;
                AudioSource.PlayOneShot(HurtSound);
                HurtIndicatorFunction(DamageInput.DamageColor);
                SendCustomEventDelayedSeconds(nameof(DeactivateImmunityFrames), 0.2f);
            }
            if(DamageInput.IsHealing && !HealImmunityFrames && HEALTH < MAXHEALTH){
                HEALTH += DamageInput.Damage;
                HealImmunityFrames = true;
                HurtIndicatorFunction(3);
                SendCustomEventDelayedSeconds(nameof(DeactivateHealImmunityFrames), 0.4f);
            }
        }

        PlayerSpeedInput SpeedInput = collision.gameObject.GetComponent<PlayerSpeedInput>();
        if(!IsDead && SpeedInput != null && SpeedInput.SpeedMultiplier != 0 && IsSpeedBoosted == false){
            SpeedBoostedFunction(SpeedInput.SpeedMultiplier);
            HurtIndicatorFunction(4);
        }
    }
    #endregion
}


