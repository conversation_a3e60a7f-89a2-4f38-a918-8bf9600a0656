﻿using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.Components;
using VRC.SDKBase;
using VRC.Udon;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class NotePickup : UdonSharpBehaviour
    {

        [Header("References")]
        public Image notePanel;
        public Text noteText;
        public Text noteUnfinishedText;

        public Transform noteTransform;

        public NoteUploader noteUploader;
        public NoteSystem noteSystem;

        public GameObject dataSendPanel;

        public void FinishButtonPressed()
        {
            // Delete previous note with same player name before uploading new one
            if (noteSystem != null)
            {
                string currentPlayerName = Networking.LocalPlayer.displayName;
                noteSystem.DeletePreviousNoteWithSameName(currentPlayerName);
            }

            dataSendPanel.SetActive(true);
            noteUploader.UpdateCopyData();

            SendCustomEventDelayedSeconds(nameof(BackButtonPressed), 180f);
        }

        public void BackButtonPressed(){dataSendPanel.SetActive(false);}

        public void TextFinished()
        {
            string text = noteUnfinishedText.text.Replace("{", "").Replace("}", "");
            noteText.text = text;
        }
    }
}
