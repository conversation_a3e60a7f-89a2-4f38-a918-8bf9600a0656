using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.Components;
using VRC.SDKBase;
using VRC.Udon;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class NotePickup : UdonSharpBehaviour
    {

        [Header("References")]
        public Image notePanel;
        public Text noteText;
        public Text noteUnfinishedText;

        public Button finishButton;

        public Transform noteTransform;
        public Transform noteEditSettingsTransform;
        public BoxCollider pinboardCanvasBoxCollider;

        public VRCPickup pickup;
        public NoteUploader noteUploader;

        public GameObject dataSendPanel;

        void Start(){SendCustomEventDelayedSeconds(nameof(UpdateCustom), 5f);}

        public void UpdateCustom()
        {
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.1f);

            if (!pickup.IsHeld) return;
            MoveToClosestPointInPinboard();
        }

        public override void OnDrop()
        {
            MoveToClosestPointInPinboard();
            transform.position = noteEditSettingsTransform.position;
            transform.rotation = noteEditSettingsTransform.rotation;
        }


        #region UI Inputs

        public void FinishButtonPressed()
        {
            dataSendPanel.SetActive(true);
            noteUploader.UpdateCopyData();

            SendCustomEventDelayedSeconds(nameof(BackButtonPressed), 60f);
        }

        public void BackButtonPressed()
        {
            dataSendPanel.SetActive(false);
        }

        public void TextFinished()
        {
            string text = noteUnfinishedText.text.Replace("{", "").Replace("}", "");
            noteText.text = text;
            bool noteHasText = noteText.text.Length > 0;

            finishButton.interactable = noteHasText;
        }

        public void CreateNewNotePressed()
        {
            bool noteEditingActive = noteTransform.gameObject.activeInHierarchy;
            ShowNote(!noteEditingActive);
        }

        #endregion


        public void MoveToClosestPointInPinboard()
        {
            Vector3 closestPosition = pinboardCanvasBoxCollider.ClosestPoint(transform.position);

            noteTransform.position = closestPosition;
            noteEditSettingsTransform.position = closestPosition;
            noteEditSettingsTransform.rotation = pinboardCanvasBoxCollider.transform.rotation;
        }

        public void ShowNote(bool value)
        {
            noteTransform.gameObject.SetActive(value);
            noteEditSettingsTransform.gameObject.SetActive(value);
            noteUnfinishedText.text = "";
            noteText.text = "";
            finishButton.interactable = false;
        }
    }
}
