using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.NoVariableSync)]
public class EconomyleaderboardSystem : UdonSharpBehaviour
{
    public TextMeshPro playerListText;
    private const string POINTS_KEY = "EmperCoins";
    private const string FLOOR_KEY = "BattleTowerFloor";
    public int LeaderboardType; //0 = EmperCoins, 1 = Total EmperCoins, 2 = Floor
    public string LeaderboardName = "EmperCoins Leaderboard";

    void Start(){UpdateLeaderboard();}

    public void UpdateLeaderboard()
    {
        VRCPlayerApi[] players = new VRCPlayerApi[VRCPlayerApi.GetPlayerCount()];
        VRCPlayerApi.GetPlayers(players);

        string leaderboard = LeaderboardName + "\n"; //0 = EmperCoins, 1 = Total EmperCoins, 2 = Floor

        for (int i = 0; i < players.Length - 1; i++)
        {
            for (int j = 0; j < players.Length - i - 1; j++)
            {
                int coins1 = GetPlayerCoins(players[j]);
                int coins2 = GetPlayerCoins(players[j + 1]);

                if (coins1 < coins2)
                {
                    VRCPlayerApi temp = players[j];
                    players[j] = players[j + 1];
                    players[j + 1] = temp;
                }
            }
        }

        if (LeaderboardType == 1) // Total EmperCoins in instance
        {
            int totalCoins = 0;
            foreach (VRCPlayerApi player in players){totalCoins += GetPlayerCoins(player);}
            leaderboard += totalCoins + "!";
        }
        else // Individual leaderboards (type 0 = EmperCoins, type 2 = Floor)
        {
            int rank = 1;
            foreach (VRCPlayerApi player in players)
            {
                int coins = GetPlayerCoins(player);
                string platformIndicator = player.IsUserInVR() ? "[VR]" : "[Desktop]";
                leaderboard += rank + ". " + player.displayName + " [" + coins + "] " + platformIndicator + "\n";
                rank++;
            }
        }

        playerListText.text = leaderboard;
        SendCustomEventDelayedSeconds(nameof(UpdateLeaderboard), 15f);
    }

    private int GetPlayerCoins(VRCPlayerApi player)
    {
        if(LeaderboardType == 0){return PlayerData.GetInt(player, POINTS_KEY);} // EmperCoins
        else if(LeaderboardType == 1){return PlayerData.GetInt(player, POINTS_KEY);} // Total EmperCoins (uses same data)
        else if(LeaderboardType == 2){return PlayerData.GetInt(player, FLOOR_KEY);} // Floor
        else {return PlayerData.GetInt(player, POINTS_KEY);} // Default to EmperCoins
    }
}