using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class NoteSystem : UdonSharpBehaviour
    {
        [Header("References")]
        public GameObject notePrefab;
        public Transform notesContainer;
        public BoxCollider pinboardCanvasBoxCollider;
        public NotePickup notePickup;
        public NoteDownloader noteDownloader;

        private GameObject[] noteGameobjects;
        private Image[] noteImages;
        private Text[] noteTexts;

        private void Start()
        {
            noteGameobjects = new GameObject[noteDownloader.noteLimit];
            noteImages = new Image[noteDownloader.noteLimit];
            noteTexts = new Text[noteDownloader.noteLimit];
        }

        public void DeleteAllNotes()
        {
            foreach (Transform child in notesContainer)
            {
                Destroy(child.gameObject);
            }
        }

        public void CreateNote(int index, Vector3 localPos, float angle, float hue, string content)
        {
            if(index >= noteDownloader.noteLimit)
            {
                Debug.LogWarning("Index over note limit!");
                return;
            }

            GameObject note = null;
            Image noteImage = null;
            Text noteText = null;

            if (noteGameobjects[index] == null)
            {
                note = Instantiate(notePrefab, notesContainer);
                noteImage = note.GetComponentInChildren<Image>();
                noteText = note.GetComponentInChildren<Text>();

                noteGameobjects[index] = note;
                noteImages[index] = noteImage;
                noteTexts[index] = noteText;
            }
            else
            {
                note = noteGameobjects[index];
                noteImage = noteImages[index];
                noteText = noteTexts[index];
            }

            note.transform.localPosition = new Vector3(localPos.x, localPos.y, 0);
            note.transform.position = pinboardCanvasBoxCollider.ClosestPoint(note.transform.position);

            Quaternion localRotation = Quaternion.Euler(0, 0, angle);
            note.transform.localRotation = localRotation;

            noteText.text = content;
        }
    }
}