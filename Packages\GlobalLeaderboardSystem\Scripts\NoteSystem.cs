using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class NoteSystem : UdonSharpBehaviour
    {
        [Header("References")]
        public GameObject notePrefab;
        public Transform notesContainer;
        public BoxCollider pinboardCanvasBoxCollider;
        public NotePickup notePickup;
        public NoteDownloader noteDownloader;

        private GameObject[] noteGameobjects;
        private Image[] noteImages;
        private Text[] noteTexts;

        private void Start()
        {
            noteGameobjects = new GameObject[noteDownloader.noteLimit];
            noteImages = new Image[noteDownloader.noteLimit];
            noteTexts = new Text[noteDownloader.noteLimit];
        }

        public void DeleteAllNotes()
        {
            foreach (Transform child in notesContainer)
            {
                Destroy(child.gameObject);
            }
        }

        public void DeleteSpecificNote(string noteContent)
        {
            foreach (Transform child in notesContainer)
            {
                if (child.name.Contains("Note (0)(Clone)"))
                {
                    Text legacyText = child.GetComponentInChildren<Text>();
                    if (legacyText != null && legacyText.text == noteContent)
                    {
                        Destroy(child.gameObject);
                        break;
                    }
                }
            }
        }

        public void DeleteNoteWithLowestScore()
        {
            Transform lowestScoreNote = null;
            int lowestScore = int.MaxValue;

            foreach (Transform child in notesContainer)
            {
                if (child.name.Contains("Note (0)(Clone)"))
                {
                    Text legacyText = child.GetComponentInChildren<Text>();
                    if (legacyText != null && !string.IsNullOrEmpty(legacyText.text))
                    {
                        int score = ExtractScoreFromText(legacyText.text);
                        if (score < lowestScore)
                        {
                            lowestScore = score;
                            lowestScoreNote = child;
                        }
                    }
                }
            }

            if (lowestScoreNote != null)
            {
                Destroy(lowestScoreNote.gameObject);
            }
        }

        private int ExtractScoreFromText(string text)
        {
            if (string.IsNullOrEmpty(text)) return 0;

            string numberString = "";
            bool foundNumber = false;

            for (int i = text.Length - 1; i >= 0; i--)
            {
                char c = text[i];
                if (char.IsDigit(c))
                {
                    numberString = c + numberString;
                    foundNumber = true;
                }
                else if (foundNumber)
                {
                    break;
                }
            }

            if (foundNumber && int.TryParse(numberString, out int score))
            {
                return score;
            }

            return 0;
        }

        public void CreateNote(int index, Vector3 localPos, float angle, float hue, string content)
        {
            if(index >= noteDownloader.noteLimit)
            {
                Debug.LogWarning("Index over note limit!");
                return;
            }

            GameObject note = null;
            Image noteImage = null;
            Text noteText = null;

            if (noteGameobjects[index] == null)
            {
                note = Instantiate(notePrefab, notesContainer);
                noteImage = note.GetComponentInChildren<Image>();
                noteText = note.GetComponentInChildren<Text>();

                noteGameobjects[index] = note;
                noteImages[index] = noteImage;
                noteTexts[index] = noteText;
            }
            else
            {
                note = noteGameobjects[index];
                noteImage = noteImages[index];
                noteText = noteTexts[index];
            }

            note.transform.localPosition = new Vector3(localPos.x, localPos.y, 0);
            note.transform.position = pinboardCanvasBoxCollider.ClosestPoint(note.transform.position);

            Quaternion localRotation = Quaternion.Euler(0, 0, angle);
            note.transform.localRotation = localRotation;

            noteText.text = content;
        }
    }
}