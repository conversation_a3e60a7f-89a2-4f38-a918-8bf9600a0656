using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.NoVariableSync)]
public class Pokeball3 : UdonSharpBehaviour
{
    public VRC_Pickup pickup;

    //Actual Script
    public int Pokevoid3Type; //0 is yellow, 1 is Purple
    private VRCPlayerApi localPlayer;  
    public GameObject RadiusObject, PurpleDamageRadius;
    public Rigidbody rb;
    public bool IsActive;
    public bool HasAlreadyStarted;

    public float Scale = 30f;
    public float PurpleScaleMultiplier = 3.17f;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        RadiusObject.SetActive(false);
        if(PurpleDamageRadius != null){PurpleDamageRadius.SetActive(false);}

        rb = GetComponent<Rigidbody>();
        pickup = GetComponent<VRC_Pickup>();

        HasAlreadyStarted = false;
    }

    public override void OnPickup()
    {

    }

    public override void OnPickupUseDown()
    {
        if(!IsActive){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(Activate));}
        else{SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(Deactivate));}

        //if(!IsActive){SendCustomEventDelayedSeconds(nameof(Activate), 0.1f);}
        //else{SendCustomEventDelayedSeconds(nameof(Deactivate), 0.1f);}
    }

    public override void OnDrop(){

    }

    public void Activate(){
        if(!IsActive){
            IsActive = true;
            RadiusObject.SetActive(true);

            if(Pokevoid3Type == 1){
                Scale = 30f;
                CustomUpdate();
            }
        }
    }

    public void Deactivate(){
        if(IsActive){
            IsActive = false;
            RadiusObject.SetActive(false);

            if(Pokevoid3Type == 1){
                Scale = 30f;
                CustomUpdate();
            }
        }
    }

    public void CustomUpdate(){
        if(Scale > 0.2f){Scale -= 0.1f;}

        RadiusObject.transform.localScale = new Vector3(Scale, Scale, Scale);

        if(PurpleDamageRadius != null){
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < Scale*PurpleScaleMultiplier){PurpleDamageRadius.SetActive(false);}
            else{PurpleDamageRadius.SetActive(true);}
        }

        if(IsActive){SendCustomEventDelayedSeconds(nameof(CustomUpdate), 0.5f);}
    }
}