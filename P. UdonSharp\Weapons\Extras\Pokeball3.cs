using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.Continuous)]
public class Pokeball3 : UdonSharpBehaviour
{
    public VRC_Pickup pickup;

    //Actual Script
    public int Pokevoid3Type; //0 is yellow, 1 is Purple
    private VRCPlayerApi localPlayer;  
    public GameObject RadiusObject;
    public GameObject PurpleDamageRadius;
    public Rigidbody rb;
    [UdonSynced] public bool IsActive;
    public float Scale;
    public bool HasAlreadyStarted;
    public float PurpleScaleMultiplier;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        RadiusObject.SetActive(false);
        if(PurpleDamageRadius != null){PurpleDamageRadius.SetActive(false);}

        rb = GetComponent<Rigidbody>();
        pickup = GetComponent<VRC_Pickup>();

        HasAlreadyStarted = false;

        if(Pokevoid3Type == 1){
            Scale = 30f;
            RadiusObject.transform.localScale = new Vector3(Scale, Scale, Scale);
        }

    }

    public override void OnPickup()
    {
        if (pickup.currentPlayer == Networking.LocalPlayer){if (!Networking.IsOwner(gameObject)){Networking.SetOwner(Networking.LocalPlayer, gameObject);}}
    }

    public override void OnPickupUseDown()
    {

    }

    public override void OnDrop(){
        if(Networking.IsOwner(gameObject)){IsActive = !IsActive;}
        if(IsActive){
            if(Pokevoid3Type == 1){
                HasAlreadyStarted = false;
                Scale = 30f;
                RadiusObject.transform.localScale = new Vector3(Scale, Scale, Scale);
            }
            RadiusObject.SetActive(true);
        }
        else{
            IsActive = false;
            if(Pokevoid3Type != 0){HasAlreadyStarted = false;}
            RadiusObject.SetActive(false);
            if(PurpleDamageRadius != null){PurpleDamageRadius.SetActive(false);}
        }
        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(CustomUpdate));
    }

    public void CustomUpdate(){
        if(IsActive){
            if(Pokevoid3Type == 1 && HasAlreadyStarted == false){
                Scale = 30f;
                SendCustomEventDelayedSeconds(nameof(UpdateScale), 1f);
                HasAlreadyStarted = true;
            }
            RadiusObject.SetActive(true);
        }
        else{
            if(Pokevoid3Type != 0 && HasAlreadyStarted == true){HasAlreadyStarted = false;}
            RadiusObject.SetActive(false);
            if(PurpleDamageRadius != null){PurpleDamageRadius.SetActive(false);}
        }
    }

    public void UpdateScale(){
        if(!Networking.IsOwner(gameObject)) return;
        else if(Pokevoid3Type == 1){if(Scale > 0.2f){Scale -= 0.1f;}}

        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(ApplyScale));

        if(IsActive && Pokevoid3Type == 1 && Scale > 0){SendCustomEventDelayedSeconds(nameof(UpdateScale), 1f);}
    }

    public void ApplyScale(){
        RadiusObject.transform.localScale = new Vector3(Scale, Scale, Scale);

        if(Pokevoid3Type == 1 && PurpleDamageRadius != null){
            if(Vector3.Distance(localPlayer.GetPosition(), transform.position) < Scale*PurpleScaleMultiplier){PurpleDamageRadius.SetActive(false);}
            else{PurpleDamageRadius.SetActive(true);}
        }
    }
}