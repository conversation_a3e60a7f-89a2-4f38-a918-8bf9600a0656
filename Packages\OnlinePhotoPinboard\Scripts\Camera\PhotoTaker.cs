﻿
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;

namespace OnlinePhotoPinboard
{
    public class PhotoTaker : UdonSharpBehaviour
    {
        [SerializeField] RenderTexture renderTexture;
        public Camera photoCamera;
        public bool captureRequested = false;
        //public InputField colorInputField;
        public TextureFormat textureFormat;
        public CameraPickup cameraPickup;

        public Texture2D currentPhoto;


        public void RequestCapture()
        {
            captureRequested = true;
        }

        void OnPostRender()
        {
            if (captureRequested)
            {
                TakeSnapshot();
            }
        }

        public string GetPictureData()
        {
            if (currentPhoto == null) return "";
            Color[] pixels = currentPhoto.GetPixels();
            string resultString = "";

            for (int i = pixels.Length - 1; i >= 0; i--)
            {
                string colorString = ColorTo18BitRGBString(pixels[i]);
                resultString += colorString;
            }
            return resultString;
        }

        //public void UpdateColorImputField(Color[] pixels)
        //{
        //    string resultString = "";
        //    for (int i = pixels.Length - 1; i >= 0; i--)
        //    {
        //        string colorString = ColorTo18BitRGBString(pixels[i]);
        //        resultString += colorString;
        //    }

        //    //colorInputField.text = resultString;
        //}

        private Color[] ConvertTo18bitRGB(Color[] pixels)
        {
            Color[] modifiedPixels = new Color[pixels.Length];

            for (int i = 0; i < pixels.Length; i++)
            {
                Color pixel = pixels[i];
                float red = Mathf.RoundToInt((pixel.linear.r * 255)) / 255f;
                //Debug.Log($"{pixel.r}, {pixel.r * 255}, {Mathf.RoundToInt((pixel.r * 255))}, {red}");
                float green = Mathf.RoundToInt((pixel.linear.g * 255)) / 255f;
                float blue = Mathf.RoundToInt((pixel.linear.b * 255)) / 255f;

                Color modifiedPixel = new Color(red, green, blue);
                modifiedPixels[i] = modifiedPixel;
            }

            return modifiedPixels;
        }


        private string EncodingSet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";
        public string ColorTo18BitRGBString(Color color)
        {
            // Convert color components from 0-1 range to 0-63 range (6 bits each)
            int r = Mathf.Clamp((int)(color.r * 63), 0, 63);
            int g = Mathf.Clamp((int)(color.g * 63), 0, 63);
            int b = Mathf.Clamp((int)(color.b * 63), 0, 63);

            // Combine the R, G, B values into a single 18-bit integer
            int combinedValue = (r << 12) | (g << 6) | b;

            // Convert the 18-bit integer to a 3-character string
            char char1 = EncodingSet[(combinedValue >> 12) & 0x3F]; // Extract the first 6 bits
            char char2 = EncodingSet[(combinedValue >> 6) & 0x3F];  // Extract the middle 6 bits
            char char3 = EncodingSet[combinedValue & 0x3F];          // Extract the last 6 bits

            // Return the encoded string
            return new string(new char[] { char1, char2, char3 });
        }


        public void TakeSnapshot()
        {
            photoCamera.targetTexture = renderTexture;
            Rect rect = new Rect(0, 0, renderTexture.width, renderTexture.height);

            Texture2D photoTexture = new Texture2D(renderTexture.width, renderTexture.height, textureFormat, false, true);
            photoTexture.filterMode = FilterMode.Point;

            photoTexture.ReadPixels(rect, 0, 0, true);
            photoTexture.Apply();

            //Color[] pixels = photoTexture.GetPixels();
            //Color[] modifiedPixels = ConvertTo18bitRGB(pixels);

            //Texture2D modifiedPhotoTexture = new Texture2D(renderTexture.width, renderTexture.height, TextureFormat.RGBA32, false, true);
            //modifiedPhotoTexture.filterMode = FilterMode.Point;
            //modifiedPhotoTexture.SetPixels(modifiedPixels);
            //modifiedPhotoTexture.Apply();

            captureRequested = false;

            currentPhoto = photoTexture;
            cameraPickup.SetPhotoPreviewTexture(photoTexture);
        }
    }
}