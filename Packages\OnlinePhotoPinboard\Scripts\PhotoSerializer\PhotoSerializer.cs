﻿
using System.Collections;
using UdonSharp;
using UnityEngine;
using VRC.SDK3.Data;
using VRC.SDKBase;
using VRC.Udon;


namespace OnlinePhotoPinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
    public class PhotoSerializer : UdonSharpBehaviour
    {
        //public Texture2D testTexture;

        [HideInInspector] public int photoStartIndex = -1;
        public NoteSystem noteSystem;
        public NoteUploader noteUploader;
        public NoteDownloader noteDownloader;
        public PhotoDownloader photoDownloader;
        //public Material outputMaterial;

        private PhotoSectionSerializer[] sectionSerializers;

        [HideInInspector] public Texture2D[] originalImages = new Texture2D[64];
        [HideInInspector] public Texture2D[] smallImages = new Texture2D[64];
        private int smallImageDimensions = 64;
        private int imagesInRowInBigImage = 8;

        private int bigImageDimensions;
        private int sectionPixelsCount;

        private bool firstTime = true;

        private int photoCounter = 99999;

        public int pictureFrameDelay = 1;
        private int pictureFrameDelayCounter = 0;






        void Start()
        {
            sectionSerializers = GetComponentsInChildren<PhotoSectionSerializer>();

            bigImageDimensions = smallImageDimensions * imagesInRowInBigImage;
            if (sectionSerializers.Length != 64)
            {
                Debug.LogError($"bigImageDimensions is not cleanly divisible by sectionserializers.lenght!  {bigImageDimensions} % {sectionSerializers.Length} != 0");
                return;
            }

            sectionPixelsCount = (smallImageDimensions * smallImageDimensions);
        }

        private void Update()
        {
            if (photoCounter >= smallImages.Length) return;
            pictureFrameDelayCounter++;
            if (pictureFrameDelayCounter <= pictureFrameDelay) return; 
            pictureFrameDelayCounter = 0;

            int i = photoCounter;
            photoCounter++;

            byte[] byteData = Texture2DToByteArray(smallImages[i]);

            //Debug.Log($"{i}.");
            if (AreTexturesEqual(smallImages[i], originalImages[i]))
            {
                //Debug.Log($"{i}. Textures Equal or Empty");
                sectionSerializers[i].UpdateImage(byteData);
                return;
            }
            //Texture2D imageSection = GetSmallImageFromImage(imageToBeSynced, i);

            if (Networking.LocalPlayer.IsOwner(noteDownloader.gameObject))
            {
                sectionSerializers[i].SetSyncedImageByteData(byteData);
            }
            else
            {
                //Debug.Log($"{i}. Local player not the owner");
                sectionSerializers[i].UpdateImage(byteData);
            }




        }




        public void SyncTakenPhoto()
        {
            //Debug.Log("Sending Sync");
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, nameof(DownloadData));
            //PhotoSectionSerializer serializer = sectionSerializers[noteDownloader.startNoteIndex];
        }

        public void DownloadData()
        {
            //Debug.Log("Downloading Data...");
            noteDownloader.DownloadPhotoData();
            photoDownloader.DownloadPhotos();
        }


        public void UpdateImageAtIndex(byte[] byteData, int sectionIndex)
        {
            Texture2D image = ReconstructImageSectionFromByteData(byteData);
            noteSystem.SetNoteImage(image, sectionIndex);
        }

        public Texture2D ReconstructImageSectionFromByteData(byte[] byteData)
        {
            Color32[] pixels = new Color32[sectionPixelsCount];
            int index = 0;

            for (int i = 0; i < pixels.Length; i++)
            {

                byte red = (byte)((byteData[index++] << 2) | 0x02);
                byte green = (byte)((byteData[index++] << 2) | 0x02);
                byte blue = (byte)((byteData[index++] << 2) | 0x02);

                pixels[i] = new Color32(red, green, blue, 255);
            }

            Texture2D section = new Texture2D(smallImageDimensions, smallImageDimensions, TextureFormat.RGBA32, false, true);

            section.SetPixels32(0, 0, 64, 64, pixels);
            section.filterMode = FilterMode.Point;
            section.Apply();
            return section;
        }



        public void SyncImageData(Texture2D imageToBeSynced)
        {

            smallImages = SplitImage(imageToBeSynced);

            if (firstTime)
            {
                firstTime = false;
                originalImages = DeepCopyTexture2DArray(smallImages);
            }
            photoCounter = 0;

        }

        public Texture2D[] DeepCopyTexture2DArray(Texture2D[] textures)
        {
            Texture2D[] copiedTextures = new Texture2D[textures.Length];
            for (int i = 0; i < textures.Length; i++)
            {
                Texture2D copyTexture = new Texture2D(textures[i].width, textures[i].height);
                copyTexture.SetPixels(textures[i].GetPixels());
                copyTexture.Apply();

                copiedTextures[i] = copyTexture;
            }

            return copiedTextures;
        }

        public Texture2D[] SplitImage(Texture2D imageTexture)
        {

            Texture2D[] images = new Texture2D[64];

            for (int y = 0; y < 8; y++)
            {
                for (int x = 0; x < 8; x++)
                {
                    int index = y * 8 + x;
                    Texture2D subTexture = new Texture2D(64, 64, TextureFormat.RGBA32, false, true);

                    subTexture.SetPixels(imageTexture.GetPixels(x * 64, (8 - 1 - y) * 64, 64, 64));
                    subTexture.Apply();

                    subTexture.filterMode = FilterMode.Point;
                    images[index] = subTexture;
                }
            }
            return images;
        }


        public Texture2D GetSmallImageFromImage(Texture2D texture, int index)
        {
            int yStart = (imagesInRowInBigImage - 1 - Mathf.FloorToInt(index / imagesInRowInBigImage)) * smallImageDimensions;
            int xStart = (index % 8) * smallImageDimensions;
            Texture2D section = new Texture2D(smallImageDimensions, smallImageDimensions);

            Color[] pixels = texture.GetPixels(xStart, yStart, smallImageDimensions, smallImageDimensions);
            section.SetPixels(pixels);
            section.Apply();

            return section;
        }



        public byte[] Texture2DToByteArray(Texture2D texture)
        {
            Color32[] pixels = texture.GetPixels32();
            byte[] result = new byte[pixels.Length * 3];
            int index = 0;

            foreach (Color32 pixel in pixels)
            {
                // Reduce to 6-bit per channel
                byte red = (byte)(pixel.r >> 2);
                byte green = (byte)(pixel.g >> 2);
                byte blue = (byte)(pixel.b >> 2);

                result[index++] = red;
                result[index++] = green;
                result[index++] = blue;
            }

            return result;
        }

        public Texture2D ByteArrayToTexture2D(byte[] data, int width, int height)
        {
            Texture2D texture = new Texture2D(width, height, TextureFormat.RGB24, false);
            Color32[] pixels = new Color32[width * height];
            int index = 0;

            for (int i = 0; i < pixels.Length; i++)
            {
                byte red = (byte)((data[index++] << 2) | 0x02);
                byte green = (byte)((data[index++] << 2) | 0x02);
                byte blue = (byte)((data[index++] << 2) | 0x02);

                pixels[i] = new Color32(red, green, blue, 255);
            }

            texture.SetPixels32(pixels);
            texture.filterMode = FilterMode.Point;
            texture.Apply();

            return texture;
        }

        public static bool AreTexturesEqual(Texture2D newTex1, Texture2D oldTex2)
        {
            if (newTex1 == null || oldTex2 == null)
            {
                Debug.Log($"Reason: newTex1 == null {newTex1 == null} || oldTex2 == null {oldTex2 == null}");
                return true;
            }
            if (newTex1.width != 64 || newTex1.height != 64 || oldTex2.width != 64 || oldTex2.height != 64)
            {
                Debug.LogWarning("One or both textures are not 64x64.");
                return true;
            }

            if (!newTex1.isReadable || !oldTex2.isReadable)
            {
                Debug.LogError("One or both textures are not readable.");
                return true;
            }

            // Get pixel data from both textures
            Color[] pixels1 = newTex1.GetPixels();
            Color[] pixels2 = oldTex2.GetPixels();

            // Compare pixel data
            for (int i = 0; i < pixels1.Length; i++)
            {
                if (pixels1[i] != pixels2[i])
                {
                    return false;
                }
            }
            return true;
        }


    }

}