; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 5
		Day: 12
		Hour: 22
		Minute: 46
		Second: 56
		Millisecond: 925
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\09yn353t_C02 Sword.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\09yn353t_C02 Sword.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2388808655920, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2383163098176, "Geometry::Scene", "Mesh" {
		Vertices: *126 {
			a: 13.4836941957474,-26.4118224382401,-13.1656050682068,3.18679884076118,-28.5218805074692,4.56915460526943,-3.23991924524307,-27.8361469507217,-1.19513273239136,0.18131211400032,-30.6151479482651,1.16543769836426,0.597058236598969,-35.4874223470688,0.122296810150146,-4.2356476187706,-2.4770051240921,17.5978541374207,-0.374016538262367,-18.565371632576,5.29938936233521,-1.7354428768158,-12.1971517801285,-0.409579277038574,1.31469964981079,-14.6314710378647,2.71623730659485,6.05355575680733,-13.1318956613541,3.25890183448792,-0.658882036805153,20.2767550945282,-17.5978541374207,-3.66595238447189,10.3435188531876,0.214731693267822,1.88978537917137,5.10667264461517,-3.87295484542847,1.01818814873695,9.14992988109589,-1.06361508369446,4.83072996139526,11.9995921850204,0.467377901077271,3.48716229200363,-46.0149109363556,4.04626727104187,-2.27019265294075,-46.0149109363556,4.04626727104187,-4.39151525497437,26.010075211525,6.16758801043034,5.60848340392113,26.010075211525,6.16758801043034,-4.39151525497437,26.010075211525,-3.83241176605225,-2.27019265294075,-46.0149109363556,-1.71108841896057,-2.27019265294075,-51.7722725868225,4.04626727104187,-2.27019265294075,-51.7722725868225,-1.71108841896057,5.60848340392113,26.010075211525,-3.83241176605225,3.48716229200363,-46.0149109363556,-1.71108841896057,3.48716229200363,-51.7722725868225,-1.71108841896057,7.02389925718307,-59.188449382782,-5.24782501161098,-5.80692887306213,-59.188449382782,-5.24782501161098,-5.80692887306213,-59.188449382782,7.58300349116325,7.02389925718307,-59.188449382782,7.58300349116325,0.60848593711853,59.188449382782,1.16758942604065,3.48716229200363,-51.7722725868225,4.04626727104187,18.3105319738388,-16.7369306087494,0.945841521024704,-0.44093132019043,-25.2871572971344,0.420138239860535,-1.58527493476868,-18.7324702739716,5.96329793334007,-1.34041309356689,-20.4429447650909,1.27135291695595,-1.99503302574158,-17.3092097043991,-2.56937518715858,-18.310534954071,-16.9171333312988,3.75722050666809,0.405797362327576,-25.4672229290009,2.49429270625114,
1.01685523986816,-18.9125299453735,-3.13266515731812,1.2200653553009,-20.6230044364929,1.56126767396927,2.23754942417145,-17.4892663955688,5.3221732378006
		} 
		PolygonVertexIndex: *180 {
			a: 0,2,-2,2,3,-2,0,1,-5,0,4,-3,5,7,-7,5,6,-10,6,8,-10,5,9,-8,10,12,-12,10,11,-15,11,13,-15,10,14,-13,18,30,-18,19,30,-24,30,18,-24,17,30,-20,32,34,-34,32,33,-37,33,35,-37,32,36,-35,37,39,-39,37,38,-42,38,40,-42,37,41,-40,1,2,-5,2,1,-4,7,8,-7,8,7,-10,12,13,-12,13,12,-15,15,17,-17,17,15,-19,19,16,-18,16,19,-21,20,21,-17,21,20,-23,23,20,-20,20,23,-25,24,22,-21,22,24,-26,24,18,-16,18,24,-24,26,28,-28,28,26,-30,15,25,-25,25,15,-32,16,31,-16,31,16,-22,25,27,-23,27,25,-27,31,26,-26,26,31,-30,22,28,-22,28,22,-28,21,29,-32,29,21,-29,34,35,-34,35,34,-37,39,40,-39,40,39,-42
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *540 {
				a: 0.665118277072906,0.210168197751045,-0.716552197933197,-0.502501726150513,0.590178608894348,-0.631807863712311,0.744464874267578,0.457126438617706,0.48662856221199,-0.665118634700775,-0.210166275501251,0.716552376747131,-0.665118813514709,-0.210166245698929,0.716552257537842,-0.665119051933289,-0.210166230797768,0.716552078723907,0.665118277072906,0.210168197751045,-0.716552197933197,0.744464874267578,0.457126438617706,0.48662856221199,0.241981759667397,-0.894386291503906,-0.376188844442368,0.665118277072906,0.210168197751045,-0.716552197933197,0.241981759667397,-0.894386291503906,-0.376188844442368,-0.502501726150513,0.590178608894348,-0.631807863712311,-0.277516096830368,0.60772293806076,0.744081735610962,-0.659122049808502,0.619722008705139,-0.426031202077866,-0.394966959953308,-0.615893959999084,0.681671321392059,-0.277516096830368,0.60772293806076,0.744081735610962,-0.394966959953308,-0.615893959999084,0.681671321392059,0.852165818214417,0.438355535268784,0.285758525133133,0.277517318725586,-0.607723236083984,-0.744081079959869,0.277517169713974,-0.607723355293274,-0.744081020355225,0.277517050504684,-0.607723414897919,-0.744081020355225,-0.277516096830368,0.60772293806076,0.744081735610962,0.852165818214417,0.438355535268784,0.285758525133133,-0.659122049808502,0.619722008705139,-0.426031202077866,-0.0838537141680717,0.556340634822845,-0.826712548732758,0.14877738058567,-0.649574220180511,-0.745599508285522,-0.929194092750549,0.366523295640945,0.0475288070738316,-0.0838537141680717,0.556340634822845,-0.826712548732758,-0.929194092750549,0.366523295640945,0.0475288070738316,0.719404280185699,0.687848687171936,0.0965492725372314,0.0838536322116852,-0.556341171264648,0.826712191104889,0.0838534459471703,-0.556341171264648,0.826712191104889,0.0838532671332359,-0.556341290473938,0.826712191104889,-0.0838537141680717,0.556340634822845,-0.826712548732758,0.719404280185699,0.687848687171936,0.0965492725372314,0.14877738058567,-0.649574220180511,-0.745599508285522,-0,0.149017885327339,0.988834500312805,
-0,0.149017885327339,0.988834500312805,-0,0.149017885327339,0.988834500312805,-0,0.149017959833145,-0.988834500312805,-0,0.149017959833145,-0.988834500312805,-0,0.149017959833145,-0.988834500312805,0.988834500312805,0.149017855525017,0,0.988834500312805,0.149017855525017,0,0.988834500312805,0.149017855525017,0,-0.988834500312805,0.149017974734306,0,-0.988834500312805,0.149017974734306,0,-0.988834500312805,0.149017974734306,0,0.982547104358673,0.185300529003143,-0.016275554895401,0.190792605280876,0.376822888851166,0.906423032283783,0.412827491760254,-0.894972681999207,-0.169107392430305,0.982547104358673,0.185300529003143,-0.016275554895401,0.412827491760254,-0.894972681999207,-0.169107392430305,0.111288078129292,0.652975857257843,-0.749157786369324,-0.982547044754028,-0.185300976037979,0.016275679692626,-0.982547044754028,-0.185301005840302,0.016275692731142,-0.982547044754028,-0.185301035642624,0.0162757039070129,0.982547104358673,0.185300529003143,-0.016275554895401,0.111288078129292,0.652975857257843,-0.749157786369324,0.190792605280876,0.376822888851166,0.906423032283783,-0.976529955863953,0.185293048620224,0.109798319637775,-0.276272237300873,0.376821339130402,-0.884126365184784,-0.394834727048874,-0.894975662231445,0.207663342356682,-0.976529955863953,0.185293048620224,0.109798319637775,-0.394834727048874,-0.894975662231445,0.207663342356682,-0.0394230224192142,0.652975142002106,0.756352663040161,0.976529955863953,-0.185293316841125,-0.10979788005352,0.976529955863953,-0.185293704271317,-0.109798014163971,0.976529836654663,-0.185294091701508,-0.109798148274422,-0.976529955863953,0.185293048620224,0.109798319637775,-0.0394230224192142,0.652975142002106,0.756352663040161,-0.276272237300873,0.376821339130402,-0.884126365184784,-0.665119051933289,-0.210166230797768,0.716552078723907,-0.665118634700775,-0.210166275501251,0.716552376747131,-0.665118873119354,-0.210165828466415,0.716552317142487,-0.665118634700775,-0.210166275501251,0.716552376747131,-0.665119051933289,-0.210166230797768,0.716552078723907,-0.665118813514709,-0.210166245698929,0.716552257537842,
0.277517378330231,-0.607723474502563,-0.744080901145935,0.277517169713974,-0.607723355293274,-0.744081020355225,0.277517318725586,-0.607723236083984,-0.744081079959869,0.277517169713974,-0.607723355293274,-0.744081020355225,0.277517378330231,-0.607723474502563,-0.744080901145935,0.277517050504684,-0.607723414897919,-0.744081020355225,0.0838533937931061,-0.556340932846069,0.826712369918823,0.0838534459471703,-0.556341171264648,0.826712191104889,0.0838536322116852,-0.556341171264648,0.826712191104889,0.0838534459471703,-0.556341171264648,0.826712191104889,0.0838533937931061,-0.556340932846069,0.826712369918823,0.0838532671332359,-0.556341290473938,0.826712191104889,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0.999566555023193,-0.0294398255646229,0,-0.999891638755798,-0.0147215081378818,0,-0.999566555023193,-0.0294398255646229,0,-0.999891638755798,-0.0147215081378818,0,-0.999566555023193,-0.0294398255646229,0,-0.999891638755798,-0.0147215081378818,0,-0.999891638755798,-0.0147215081378818,0,-1,0,0,-0.999891638755798,-0.0147215081378818,0,-1,0,0,-0.999891638755798,-0.0147215081378818,0,-1,0,0,-0,-0.0294398367404938,-0.999566555023193,-0,-0.0147215137258172,-0.999891638755798,-0,-0.0294398348778486,-0.999566555023193,-0,-0.0147215137258172,-0.999891638755798,-0,-0.0294398367404938,-0.999566555023193,-0,-0.0147215137258172,-0.999891638755798,-0,-0.0147215137258172,-0.999891638755798,-0,0,-1,-0,-0.0147215137258172,-0.999891638755798,-0,0,-1,-0,-0.0147215137258172,-0.999891638755798,-0,0,-1,0.999566555023193,-0.0294398032128811,0,0.999566555023193,-0.0294398032128811,0,0.999566555023193,-0.0294398050755262,0,0.999566555023193,-0.0294398032128811,0,0.999566555023193,-0.0294398032128811,0,0.999566555023193,-0.0294398032128811,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0.430451810359955,-0.902613639831543,-0,0.430451810359955,-0.902613639831543,-0,0.430451780557632,-0.902613639831543,-0,0.430451810359955,-0.902613639831543,-0,0.430451810359955,-0.902613639831543,-0,0.430451810359955,-0.902613580226898,0.902613520622253,0.430451840162277,0,0.902613520622253,0.430451840162277,0,0.902613580226898,0.430451840162277,0,0.902613520622253,0.430451840162277,0,0.902613520622253,0.430451840162277,0,0.902613520622253,0.430451840162277,0,-0.902613639831543,0.43045175075531,0,-0.902613639831543,0.43045175075531,0,-0.902613639831543,0.43045175075531,0,-0.902613639831543,0.43045175075531,0,-0.902613639831543,0.43045175075531,0,-0.902613580226898,0.430451780557632,0,-0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.430451720952988,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.430451780557632,0.902613580226898,-0.982547044754028,-0.185301035642624,0.0162756368517876,-0.982547044754028,-0.185301005840302,0.016275692731142,-0.982547044754028,-0.185300976037979,0.016275679692626,-0.982547044754028,-0.185301005840302,0.016275692731142,-0.982547044754028,-0.185301035642624,0.0162756368517876,-0.982547044754028,-0.185301035642624,0.0162757039070129,0.976529955863953,-0.185293942689896,-0.109797343611717,0.976529955863953,-0.185293704271317,-0.109798014163971,0.976529955863953,-0.185293316841125,-0.10979788005352,0.976529955863953,-0.185293704271317,-0.109798014163971,0.976529955863953,-0.185293942689896,-0.109797343611717,0.976529836654663,-0.185294091701508,-0.109798148274422
			} 
			NormalsW: *180 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *540 {
				a: 0.0831507220864296,-0.974453508853912,-0.208629712462425,0.632085919380188,-0.247810289263725,-0.734205186367035,0.632085919380188,-0.247810170054436,-0.734205186367035,0.299038141965866,0.804310441017151,0.513479232788086,0.299038082361221,0.804310441017151,0.513479292392731,0.299037992954254,0.804310441017151,0.513479471206665,-0.68424779176712,0.555799961090088,-0.472113907337189,0.288896411657333,0.436539471149445,-0.852039933204651,0.28889536857605,0.436540007591248,-0.85204017162323,0.601097345352173,0.418652892112732,0.680743634700775,0.863719642162323,0.375205844640732,-0.33646547794342,0.863719284534454,0.375205516815186,-0.336466491222382,0.947771966457367,0.299915313720703,0.108531847596169,0.175637438893318,0.677690744400024,0.71406352519989,0.175637498497963,0.67769068479538,0.71406352519989,-0.610028743743896,0.486864805221558,-0.625162124633789,-0.521031975746155,0.761296510696411,0.385944783687592,-0.52103191614151,0.761296510696411,0.385944932699203,-0.742192447185516,-0.627404093742371,0.23561517894268,-0.742192506790161,-0.627403974533081,0.235615327954292,-0.742192566394806,-0.627403974533081,0.235615447163582,-0.337743669748306,-0.786779999732971,0.516629993915558,-0.399262577295303,0.191705793142319,0.896570324897766,-0.399262547492981,0.191705599427223,0.896570324897766,0.76250821352005,0.569933295249939,0.306198209524155,0.266003519296646,0.752488493919373,-0.602497518062592,0.266003102064133,0.752488374710083,-0.602497816085815,0.174319818615913,-0.808651566505432,-0.561867713928223,0.00295731029473245,0.135967150330544,-0.990708887577057,0.00295734591782093,0.135966971516609,-0.990709006786346,-0.98111218214035,-0.191227108240128,-0.0291730221360922,-0.98111218214035,-0.191227003931999,-0.0291731730103493,-0.981112241744995,-0.191226899623871,-0.0291733387857676,-0.936827838420868,0.238718524575233,0.25566965341568,-0.493963092565536,0.604364931583405,-0.625094950199127,-0.493963241577148,0.60436487197876,-0.625094830989838,-0,-0.988834500312805,0.149017885327339,-0,-0.988834500312805,0.149017885327339,
-0,-0.988834500312805,0.149017885327339,-0,-0.988834500312805,-0.149017959833145,-0,-0.988834500312805,-0.149017959833145,-0,-0.988834500312805,-0.149017959833145,-0.149017855525017,0.988834500312805,0,-0.149017855525017,0.988834500312805,0,-0.149017855525017,0.988834500312805,0,0.149017974734306,0.988834500312805,-0,0.149017974734306,0.988834500312805,-0,0.149017974734306,0.988834500312805,-0,-0.130923539400101,0.626747071743011,-0.768145322799683,0.820265769958496,0.446028143167496,-0.35808253288269,0.820266008377075,0.446027785539627,-0.358082085847855,-0.0489723198115826,0.342095077037811,0.938388407230377,0.856915771961212,0.31872770190239,0.405102282762527,0.856915354728699,0.318727910518646,0.405103027820587,0.179452121257782,-0.921213746070862,0.345198899507523,0.179452151060104,-0.921213746070862,0.345198899507523,0.179452195763588,-0.921213746070862,0.345198899507523,0.179895833134651,-0.968841969966888,-0.170243054628372,0.959268629550934,-0.267541527748108,-0.0906924977898598,0.959268808364868,-0.267541259527206,-0.0906924456357956,0.203496336936951,0.626748144626617,0.752180874347687,-0.782428503036499,0.446022093296051,0.434591621160507,-0.782428801059723,0.446021914482117,0.434591382741928,-0.0406431034207344,0.342095404863358,-0.93878585100174,-0.89161080121994,0.318721354007721,-0.321631401777267,-0.891610622406006,0.318721532821655,-0.321631997823715,-0.211512088775635,-0.921215057373047,-0.32653546333313,-0.211512506008148,-0.921215057373047,-0.326535433530807,-0.2115129083395,-0.921214938163757,-0.326535373926163,-0.162853226065636,-0.968843400478363,0.186604797840118,-0.946265280246735,-0.267548382282257,0.181658387184143,-0.946265399456024,-0.267548233270645,0.181658387184143,0.299037396907806,0.804310917854309,0.513478994369507,0.314505040645599,0.791476666927338,0.524071753025055,0.30771791934967,0.797175467014313,0.519442915916443,0.314505040645599,0.791476666927338,0.524071753025055,0.299037396907806,0.804310917854309,0.513478994369507,0.307717680931091,0.797175407409668,0.519443213939667,
-0.742192387580872,-0.627404153347015,0.235615417361259,-0.742192387580872,-0.627404034137726,0.235615432262421,-0.742192387580872,-0.627404272556305,0.235615327954292,-0.742192387580872,-0.627404034137726,0.235615432262421,-0.742192387580872,-0.627404153347015,0.235615417361259,-0.742192447185516,-0.627403974533081,0.235615521669388,-0.981112241744995,-0.191226795315742,-0.0291730295866728,-0.981112241744995,-0.191226795315742,-0.0291730277240276,-0.981112241744995,-0.191226929426193,-0.0291728936135769,-0.981112241744995,-0.191226795315742,-0.0291730277240276,-0.981112241744995,-0.191226795315742,-0.0291730295866728,-0.98111230134964,-0.191226691007614,-0.0291731916368008,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0.0294398255646229,0.999566555023193,-0,-0.0147215081378818,0.999891638755798,-0,-0.0294398255646229,0.999566555023193,-0,-0.0147215081378818,0.999891638755798,-0,-0.0294398255646229,0.999566555023193,-0,-0.0147215081378818,0.999891638755798,-0,-0.0147215081378818,0.999891638755798,-0,0,1,-0,-0.0147215081378818,0.999891638755798,-0,0,1,-0,-0.0147215081378818,0.999891638755798,-0,0,1,-0,0,0.999566555023193,-0.0294398367404938,0,0.999891638755798,-0.0147215137258172,0,0.999566555023193,-0.0294398348778486,0,0.999891638755798,-0.0147215137258172,0,0.999566555023193,-0.0294398367404938,0,0.999891638755798,-0.0147215137258172,0,0.999891638755798,-0.0147215137258172,0,1,-0,0,0.999891638755798,-0.0147215137258172,0,1,-0,0,0.999891638755798,-0.0147215137258172,0,1,-0,0.0294398032128811,0.999566555023193,-0,0.0294398032128811,0.999566555023193,-0,0.0294398050755262,0.999566555023193,-0,0.0294398032128811,0.999566555023193,-0,0.0294398032128811,0.999566555023193,-0,0.0294398032128811,0.999566555023193,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,
0,1,-0,0,0.902613639831543,0.430451810359955,0,0.902613639831543,0.430451810359955,0,0.902613639831543,0.430451780557632,0,0.902613639831543,0.430451810359955,0,0.902613639831543,0.430451810359955,0,0.902613580226898,0.430451810359955,-0.430451840162277,0.902613520622253,0,-0.430451840162277,0.902613520622253,0,-0.430451840162277,0.902613580226898,0,-0.430451840162277,0.902613520622253,0,-0.430451840162277,0.902613520622253,0,-0.430451840162277,0.902613520622253,0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.430451780557632,0.902613580226898,-0,0,0.902613639831543,-0.43045175075531,0,0.902613639831543,-0.43045175075531,0,0.902613639831543,-0.430451720952988,0,0.902613639831543,-0.43045175075531,0,0.902613639831543,-0.43045175075531,0,0.902613580226898,-0.430451780557632,0.179452195763588,-0.921213924884796,0.34519824385643,0.179452180862427,-0.921213924884796,0.345198214054108,0.179452151060104,-0.921213984489441,0.345198214054108,0.179452180862427,-0.921213924884796,0.345198214054108,0.179452195763588,-0.921213924884796,0.34519824385643,0.179452210664749,-0.921213924884796,0.34519824385643,-0.211512461304665,-0.921215355396271,-0.326534539461136,-0.211512461304665,-0.921215415000916,-0.326534539461136,-0.211512058973312,-0.921215415000916,-0.326534509658813,-0.211512461304665,-0.921215415000916,-0.326534539461136,-0.211512461304665,-0.921215355396271,-0.326534539461136,-0.211512878537178,-0.921215295791626,-0.326534479856491
			} 
			BinormalsW: *180 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *540 {
				a: 0.742094099521637,-0.0791815966367722,0.665602445602417,0.589880585670471,0.768296420574188,0.248518362641335,0.215033039450645,-0.854181170463562,0.473429173231125,0.684246599674225,-0.555801093578339,0.472114115953445,0.684246480464935,-0.555801153182983,0.472114264965057,0.684246301651001,-0.555801212787628,0.472114443778992,-0.299036294221878,-0.80431079864502,-0.513479828834534,0.601922690868378,-0.774899125099182,-0.192926108837128,-0.926274478435516,-0.0974988788366318,-0.364018738269806,-0.443057298660278,0.883492588996887,-0.152122154831886,-0.442078292369843,0.24350306391716,-0.86329197883606,-0.0384823121130466,0.714779496192932,0.698290288448334,0.157204210758209,-0.735339164733887,0.659214079380035,-0.731238424777985,-0.395828068256378,0.55552738904953,0.901749730110168,-0.401758432388306,0.159491270780563,0.742192566394806,0.627403795719147,-0.235615625977516,0.756654977798462,0.202737107872963,0.62158739566803,0.0483657829463482,0.477778315544128,-0.877148032188416,0.610028326511383,-0.486864060163498,0.625163078308105,0.610028326511383,-0.486864060163498,0.625163078308105,0.610028386116028,-0.48686408996582,0.625163078308105,-0.899396538734436,0.107935778796673,-0.423598676919937,-0.338234812021255,0.878119289875031,-0.338384002447128,-0.637297034263611,-0.761047720909119,-0.121074423193932,-0.641521513462067,0.60469925403595,0.472005307674408,-0.952421903610229,0.108694106340408,-0.284742295742035,0.256594330072403,0.54719465970993,0.796704113483429,0.981112241744995,0.191227078437805,0.0291727613657713,0.36958035826683,0.920420289039612,0.127423793077469,0.694585263729095,-0.713005840778351,-0.0957810208201408,-0.174319937825203,0.808651149272919,0.561868190765381,-0.174319937825203,0.808651149272919,0.561868190765381,-0.174319937825203,0.808651149272919,0.561868250370026,-0.339591026306152,-0.795926213264465,-0.501177966594696,0.488321751356125,-0.402004092931747,-0.774554491043091,-0.856659591197968,-0.461298763751984,0.230949908494949,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,
-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,0.132137075066566,-0.756869792938232,-0.640068709850311,0.539223849773407,-0.81182724237442,0.2239960283041,-0.395900309085846,-0.00911303609609604,-0.918248236179352,-0.179451659321785,0.921213746070862,-0.345199137926102,0.308656364679337,0.312147974967957,-0.898495852947235,-0.503300070762634,0.687047958374023,0.524074554443359,0.0489723086357117,-0.342094838619232,-0.938388466835022,0.0489723049104214,-0.342094838619232,-0.938388466835022,0.0489723086357117,-0.342094838619232,-0.938388466835022,0.0473145730793476,-0.164343923330307,0.985267698764801,0.259650856256485,0.708550691604614,0.656153440475464,-0.208330571651459,-0.886806786060333,0.412519246339798,-0.0705579966306686,-0.756870746612549,0.649744808673859,-0.558103263378143,-0.811831176280975,-0.171612322330475,0.481571108102798,-0.00911006890237331,0.876359701156616,0.211511999368668,0.921215116977692,0.326535552740097,-0.221665561199188,0.312146097421646,0.92381227016449,0.451083540916443,0.687051713466644,-0.569634616374969,0.0406426303088665,-0.342095255851746,0.938785910606384,0.0406426265835762,-0.342095255851746,0.938785910606384,0.0406426265835762,-0.342095255851746,0.938785910606384,-0.140953958034515,-0.164344176650047,-0.976280212402344,-0.320979326963425,0.708548784255981,-0.628435134887695,0.16809369623661,-0.886805295944214,-0.430489003658295,0.68424654006958,-0.555800497531891,0.47211492061615,0.677276730537415,-0.573929250240326,0.460327535867691,0.680387079715729,-0.565987288951874,0.465544641017914,0.677276730537415,-0.573929250240326,0.460327535867691,0.68424654006958,-0.555800497531891,0.47211492061615,0.680387258529663,-0.565987229347229,0.465544492006302,0.610028386116028,-0.486863762140274,0.62516325712204,0.610028445720673,-0.486863940954208,0.625163018703461,0.610028505325317,-0.486863940954208,0.625163078308105,0.610028445720673,-0.486863940954208,0.625163018703461,0.610028386116028,-0.486863762140274,0.62516325712204,0.610028445720673,-0.486863940954208,0.625163018703461,-0.174319714307785,0.808651387691498,0.561867952346802,
-0.174319684505463,0.808651208877563,0.561868250370026,-0.174319714307785,0.808651208877563,0.561868250370026,-0.174319684505463,0.808651208877563,0.561868250370026,-0.174319714307785,0.808651387691498,0.561867952346802,-0.174319684505463,0.808651208877563,0.561868250370026,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.0489722415804863,-0.34209418296814,-0.938388645648956,0.0489721857011318,-0.34209418296814,-0.938388645648956,0.0489721857011318,-0.34209418296814,-0.938388705253601,0.0489721857011318,-0.34209418296814,-0.938388645648956,0.0489722415804863,-0.34209418296814,-0.938388645648956,0.0489721894264221,-0.342094212770462,-0.938388645648956,0.0406421385705471,-0.342094242572784,0.938786327838898,0.040642824023962,-0.342094361782074,0.938786268234253,0.0406428314745426,-0.342094331979752,0.938786268234253,0.040642824023962,-0.342094361782074,0.938786268234253,0.0406421385705471,-0.342094242572784,0.938786327838898,0.0406428165733814,-0.342094361782074,0.938786268234253
			} 
			TangentsW: *180 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *288 {
				a: 0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.0675515681505203,-0.211390003561974,0.142551571130753,-0.168088719248772,0.117551542818546,-0.211389973759651,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.136137545108795,-0.176895618438721,0.136137515306473,-0.263498157262802,0.111137516796589,-0.2201968729496,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.0719718337059021,-0.221348151564598,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,-0.156327903270721,-0.177946090698242,-0.0813279151916504,-0.134644821286201,-0.106327913701534,-0.177946090698242,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,-0.0911435559391975,-0.136728048324585,-0.0911435708403587,-0.223330572247505,-0.116143591701984,-0.180029332637787,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,-0.0844317227602005,-0.230789482593536,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.0641350075602531,-0.150301948189735,0.139135032892227,-0.107000663876534,0.114135034382343,-0.150301963090897,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.133624255657196,-0.10865344107151,0.133624270558357,-0.195255979895592,0.108624272048473,-0.151954710483551,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.137811049818993,-0.199202001094818,0.0939525216817856,-0.450727015733719,0.0363789722323418,-0.450727015733719,0.0151657462120056,0.269835203886032,0.115165740251541,0.269835203886032,-0.0383241176605225,0.267572999000549,0.0616758801043034,0.267572999000549,0.0404626727104187,-0.452989220619202,-0.0171108841896057,-0.452989220619202,-0.0171108841896057,-0.452989220619202,0.0404626727104187,-0.452989220619202,0.0404626727104187,-0.509687781333923,-0.0171108841896057,-0.509687781333923,-0.115165740251541,0.269147723913193,-0.0151657462120056,0.269147723913193,-0.0363789722323418,-0.451414495706558,-0.0939525216817856,-0.451414495706558,-0.0939525216817856,-0.451414495706558,-0.0363789722323418,-0.451414495706558,-0.0363789722323418,
-0.509687781333923,-0.0939525216817856,-0.509687781333923,0.0171108841896057,-0.449152290821075,-0.0404626727104187,-0.449152290821075,-0.0616758801043034,0.271409928798676,0.0383241176605225,0.271409928798676,0.12931989133358,-0.0524782538414001,0.00101161003112793,-0.0524782538414001,0.00101161003112793,0.0758300423622131,0.12931989133358,0.0758300423622131,0.115165740251541,0.268135726451874,0.0151657462120056,0.268135726451874,-0.0651657432317734,0.0116758942604065,-0.0651657432317734,0.0116758942604065,-0.0151657462120056,0.268135696649551,-0.115165740251541,0.268135696649551,-0.0404626727104187,-0.452114194631577,0.0171108841896057,-0.452114194631577,0.0171108841896057,-0.509687781333923,-0.0404626727104187,-0.509687781333923,0.0363789722323418,-0.452114194631577,0.0939525216817856,-0.452114194631577,0.0939525216817856,-0.509687781333923,0.0363789722323418,-0.509687781333923,-0.0939525216817856,-0.467416524887085,-0.0363789722323418,-0.467416524887085,-0.00101161003112793,-0.549579918384552,-0.12931989133358,-0.549579918384552,-0.0404626727104187,-0.500493168830872,0.0171108841896057,-0.500493168830872,0.0524782538414001,-0.582656502723694,-0.0758300423622131,-0.582656502723694,-0.0171108841896057,-0.44439172744751,0.0404626727104187,-0.44439172744751,0.0758300423622131,-0.526555061340332,-0.0524782538414001,-0.526555061340332,0.0363789722323418,-0.477468341588974,0.0939525216817856,-0.477468341588974,0.12931989133358,-0.559631705284119,0.00101161003112793,-0.559631705284119,-0.0116758942604065,0.58436906337738,-0.0616758801043034,0.24883896112442,0.0383241176605225,0.24883896112442,0.0116758942604065,0.586182534694672,-0.0383241176605225,0.250652432441711,0.0616758801043034,0.250652432441711,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.031915158033371,-0.123817950487137,0.106915146112442,-0.0805166810750961,0.0819151625037193,-0.123817920684814,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.103037655353546,-0.0819642543792725,0.103037625551224,-0.168566793203354,0.0780376344919205,
-0.125265538692474,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.106229916214943,-0.171201169490814,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.031915158033371,-0.123817950487137,0.106915146112442,-0.0805166810750961,0.0819151625037193,-0.123817920684814,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.103037655353546,-0.0819642543792725,0.103037625551224,-0.168566793203354,0.0780376344919205,-0.125265538692474,0.086602546274662,0.206155300140381,0.173205092549324,0,0,0,0.106229916214943,-0.171201169490814
			} 
			UVIndex: *180 {
				a: 0,2,1,3,5,4,6,8,7,12,14,13,16,18,17,22,24,23,25,27,26,28,30,29,32,34,33,38,40,39,41,43,42,44,46,45,76,78,77,80,79,81,106,107,108,111,109,110,112,114,113,118,120,119,121,123,122,124,126,125,128,130,129,134,136,135,137,139,138,140,142,141,9,15,10,15,9,11,19,21,20,21,19,31,35,37,36,37,35,47,48,50,49,50,48,51,52,54,53,54,52,55,56,58,57,58,56,59,60,62,61,62,60,63,64,66,65,66,64,67,68,70,69,70,68,71,72,74,73,74,72,75,82,84,83,84,82,85,86,88,87,88,86,89,90,92,91,92,90,93,94,96,95,96,94,97,98,100,99,100,98,101,102,104,103,104,102,105,115,117,116,117,115,127,131,133,132,133,131,143
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2385729043024, "Model::C02_Sword", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2383616142768, "Material::Green_Corr", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2382812106160, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Corr green.jpg"
			P: "RelPath", "KString", "XRefUrl", "", "..\Sprites\Corr green.jpg"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Corr green.jpg"
		RelativeFilename: "..\Sprites\Corr green.jpg"
	}
	Texture: 2382812104720, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Corr green.jpg"
		RelativeFilename: "..\Sprites\Corr green.jpg"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::C02_Sword, Model::RootNode
	C: "OO",2385729043024,0
	
	;Material::Green_Corr, Model::C02_Sword
	C: "OO",2383616142768,2385729043024
	
	;Geometry::Scene, Model::C02_Sword
	C: "OO",2383163098176,2385729043024
	
	;Texture::DiffuseColor_Texture, Material::Green_Corr
	C: "OO",2382812104720,2383616142768
	
	;Texture::DiffuseColor_Texture, Material::Green_Corr
	C: "OP",2382812104720,2383616142768, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",2382812106160,2382812104720
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
