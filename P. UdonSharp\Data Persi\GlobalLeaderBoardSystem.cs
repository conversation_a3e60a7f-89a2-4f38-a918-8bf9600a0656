
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GlobalLeaderBoardSystem : UdonSharpBehaviour
{
    [Header("Global Leaderboard Settings")]
    public TextMeshProUGUI leaderboardDisplay;
    public string scoreKey = "PlayerScore"; // PlayerData key for individual scores
    public string leaderboardTitle = "Global Top 10 Leaderboard";

    [Header("Global Leaderboard Storage")]
    private const string GLOBAL_LEADERBOARD_KEY = "GlobalLeaderboard";
    private const int MAX_LEADERBOARD_ENTRIES = 10;

    [Header("Update Settings")]
    public float updateInterval = 30f; // Update every 30 seconds
    public bool enableDebugLogs = true;

    private VRCPlayerApi localPlayer;
    private bool isUpdating = false;

    // Leaderboard entry structure (stored as concatenated string)
    // Format: "PlayerName:Score|PlayerName2:Score2|..."

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        if (localPlayer != null)
        {
            UpdateGlobalLeaderboard();
            SendCustomEventDelayedSeconds(nameof(PeriodicUpdate), updateInterval);
        }
    }

    public void PeriodicUpdate()
    {
        if (localPlayer != null)
        {
            UpdateGlobalLeaderboard();
            SendCustomEventDelayedSeconds(nameof(PeriodicUpdate), updateInterval);
        }
    }

    public void UpdateGlobalLeaderboard()
    {
        if (isUpdating || localPlayer == null) return;
        isUpdating = true;

        // Get current player's score
        int currentPlayerScore = PlayerData.GetInt(localPlayer, scoreKey);
        string currentPlayerName = localPlayer.displayName;

        // Get existing global leaderboard
        string globalLeaderboardData = PlayerData.GetString(localPlayer, GLOBAL_LEADERBOARD_KEY);

        // Parse existing leaderboard
        string[] leaderboardEntries = ParseLeaderboard(globalLeaderboardData);

        // Check if current player should be on leaderboard
        bool playerUpdated = UpdatePlayerInLeaderboard(ref leaderboardEntries, currentPlayerName, currentPlayerScore);

        // Sort leaderboard by score (descending)
        SortLeaderboard(ref leaderboardEntries);

        // Trim to top 10
        if (leaderboardEntries.Length > MAX_LEADERBOARD_ENTRIES)
        {
            string[] trimmedEntries = new string[MAX_LEADERBOARD_ENTRIES];
            for (int i = 0; i < MAX_LEADERBOARD_ENTRIES; i++)
            {
                trimmedEntries[i] = leaderboardEntries[i];
            }
            leaderboardEntries = trimmedEntries;
        }

        // Save updated leaderboard if player was updated
        if (playerUpdated)
        {
            string updatedLeaderboardData = SerializeLeaderboard(leaderboardEntries);
            PlayerData.SetString(localPlayer, GLOBAL_LEADERBOARD_KEY, updatedLeaderboardData);

            if (enableDebugLogs)
            {
                Debug.Log($"Global Leaderboard Updated: {currentPlayerName} with score {currentPlayerScore}");
            }
        }

        // Display leaderboard
        DisplayLeaderboard(leaderboardEntries);

        isUpdating = false;
    }

    private string[] ParseLeaderboard(string leaderboardData)
    {
        if (string.IsNullOrEmpty(leaderboardData))
        {
            return new string[0];
        }

        return leaderboardData.Split('|');
    }

    private bool UpdatePlayerInLeaderboard(ref string[] entries, string playerName, int playerScore)
    {
        // Check if player already exists in leaderboard
        for (int i = 0; i < entries.Length; i++)
        {
            if (!string.IsNullOrEmpty(entries[i]) && entries[i].Contains(":"))
            {
                string[] parts = entries[i].Split(':');
                if (parts.Length >= 2 && parts[0] == playerName)
                {
                    int existingScore = int.Parse(parts[1]);
                    if (playerScore > existingScore)
                    {
                        entries[i] = playerName + ":" + playerScore;
                        return true;
                    }
                    return false; // Player exists but score isn't higher
                }
            }
        }

        // Player doesn't exist, check if score qualifies for top 10
        if (entries.Length < MAX_LEADERBOARD_ENTRIES)
        {
            // Add new entry
            string[] newEntries = new string[entries.Length + 1];
            for (int i = 0; i < entries.Length; i++)
            {
                newEntries[i] = entries[i];
            }
            newEntries[entries.Length] = playerName + ":" + playerScore;
            entries = newEntries;
            return true;
        }
        else
        {
            // Check if score is higher than lowest score
            int lowestScore = int.MaxValue;
            int lowestIndex = -1;

            for (int i = 0; i < entries.Length; i++)
            {
                if (!string.IsNullOrEmpty(entries[i]) && entries[i].Contains(":"))
                {
                    string[] parts = entries[i].Split(':');
                    if (parts.Length >= 2)
                    {
                        int score = int.Parse(parts[1]);
                        if (score < lowestScore)
                        {
                            lowestScore = score;
                            lowestIndex = i;
                        }
                    }
                }
            }

            if (playerScore > lowestScore && lowestIndex >= 0)
            {
                entries[lowestIndex] = playerName + ":" + playerScore;
                return true;
            }
        }

        return false;
    }

    private void SortLeaderboard(ref string[] entries)
    {
        // Simple bubble sort by score (descending)
        for (int i = 0; i < entries.Length - 1; i++)
        {
            for (int j = 0; j < entries.Length - i - 1; j++)
            {
                if (!string.IsNullOrEmpty(entries[j]) && !string.IsNullOrEmpty(entries[j + 1]) &&
                    entries[j].Contains(":") && entries[j + 1].Contains(":"))
                {
                    string[] parts1 = entries[j].Split(':');
                    string[] parts2 = entries[j + 1].Split(':');

                    if (parts1.Length >= 2 && parts2.Length >= 2)
                    {
                        int score1 = int.Parse(parts1[1]);
                        int score2 = int.Parse(parts2[1]);

                        if (score1 < score2) // Swap for descending order
                        {
                            string temp = entries[j];
                            entries[j] = entries[j + 1];
                            entries[j + 1] = temp;
                        }
                    }
                }
            }
        }
    }

    private string SerializeLeaderboard(string[] entries)
    {
        string result = "";
        for (int i = 0; i < entries.Length; i++)
        {
            if (!string.IsNullOrEmpty(entries[i]))
            {
                if (result.Length > 0)
                {
                    result += "|";
                }
                result += entries[i];
            }
        }
        return result;
    }

    private void DisplayLeaderboard(string[] entries)
    {
        if (leaderboardDisplay == null) return;

        string displayText = leaderboardTitle + "\n\n";

        for (int i = 0; i < entries.Length && i < MAX_LEADERBOARD_ENTRIES; i++)
        {
            if (!string.IsNullOrEmpty(entries[i]) && entries[i].Contains(":"))
            {
                string[] parts = entries[i].Split(':');
                if (parts.Length >= 2)
                {
                    string playerName = parts[0];
                    string score = parts[1];
                    displayText += (i + 1) + ". " + playerName + " - " + score + "\n";
                }
            }
        }

        if (entries.Length == 0)
        {
            displayText += "No scores recorded yet!";
        }

        leaderboardDisplay.text = displayText;

        if (enableDebugLogs)
        {
            Debug.Log("Global Leaderboard Display Updated: " + entries.Length + " entries");
        }
    }

    // Public method to manually trigger leaderboard update
    public void ForceUpdateLeaderboard()
    {
        UpdateGlobalLeaderboard();
    }

    // Public method to reset the global leaderboard (for testing)
    public void ResetGlobalLeaderboard()
    {
        if (localPlayer != null)
        {
            PlayerData.SetString(localPlayer, GLOBAL_LEADERBOARD_KEY, "");
            if (leaderboardDisplay != null)
            {
                leaderboardDisplay.text = leaderboardTitle + "\n\nLeaderboard Reset!";
            }

            if (enableDebugLogs)
            {
                Debug.Log("Global Leaderboard Reset");
            }
        }
    }
}
