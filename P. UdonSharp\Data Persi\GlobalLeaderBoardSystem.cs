
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
public class GlobalLeaderBoardSystem : UdonSharpBehaviour
{
    public TextMeshPro leaderboardDisplay;
    public string[] scoreKeys = {"EmperCoins", "Floors"};
    public string[] leaderboardTitles = {"Global EmperCoins Top 10", "Global Floors Top 10"};
    public int currentLeaderboardIndex = 0;
    public float updateInterval = 30f;

    private string[] globalPlayerNames = new string[10];
    private int[] globalScores = new int[10];
    private string[] globalPlatforms = new string[10];
    private const string MASTER_LEADERBOARD_KEY = "MasterGlobalLeaderboard";

    private const int MAX_LEADERBOARD_ENTRIES = 10;
    private VRCPlayerApi localPlayer;
    private bool isUpdating = false;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        if (localPlayer != null)
        {
            LoadGlobalLeaderboard();
            SendCustomEventDelayedSeconds(nameof(UpdateGlobalLeaderboard), 10f);
            SendCustomEventDelayedSeconds(nameof(PeriodicUpdate), updateInterval);
        }
    }

    public override void OnPlayerLeft(VRCPlayerApi player)
    {
        if (player == localPlayer)
        {
            SaveGlobalLeaderboard();
        }
    }

    public void PeriodicUpdate()
    {
        if (localPlayer != null)
        {
            UpdateGlobalLeaderboard();
            SendCustomEventDelayedSeconds(nameof(PeriodicUpdate), updateInterval);
        }
    }

    public void UpdateGlobalLeaderboard()
    {
        if (isUpdating || localPlayer == null || currentLeaderboardIndex >= scoreKeys.Length) return;
        isUpdating = true;

        LoadGlobalLeaderboard();

        string currentScoreKey = scoreKeys[currentLeaderboardIndex];
        int currentPlayerScore = PlayerData.GetInt(localPlayer, currentScoreKey);
        string currentPlayerName = localPlayer.displayName;
        string platformIndicator = localPlayer.IsUserInVR() ? "[VR]" : "[Desktop]";

        bool wasUpdated = UpdatePlayerInGlobalLeaderboard(currentPlayerName, currentPlayerScore, platformIndicator);

        if (wasUpdated)
        {
            SortGlobalLeaderboard();
            SaveGlobalLeaderboard();
        }

        DisplayGlobalLeaderboard();
        isUpdating = false;
    }

    private void LoadGlobalLeaderboard()
    {
        string masterKey = MASTER_LEADERBOARD_KEY + "_" + scoreKeys[currentLeaderboardIndex];
        string savedData = PlayerData.GetString(localPlayer, masterKey);

        for (int i = 0; i < MAX_LEADERBOARD_ENTRIES; i++)
        {
            globalPlayerNames[i] = "";
            globalScores[i] = 0;
            globalPlatforms[i] = "";
        }

        if (!string.IsNullOrEmpty(savedData))
        {
            string[] entries = savedData.Split('|');
            for (int i = 0; i < entries.Length && i < MAX_LEADERBOARD_ENTRIES; i++)
            {
                if (!string.IsNullOrEmpty(entries[i]) && entries[i].Contains(":"))
                {
                    string[] parts = entries[i].Split(':');
                    if (parts.Length >= 3)
                    {
                        globalPlayerNames[i] = parts[0];
                        globalScores[i] = int.Parse(parts[1]);
                        globalPlatforms[i] = parts[2];
                    }
                }
            }
        }
    }

    private void SaveGlobalLeaderboard()
    {
        string masterKey = MASTER_LEADERBOARD_KEY + "_" + scoreKeys[currentLeaderboardIndex];
        string dataToSave = "";

        for (int i = 0; i < MAX_LEADERBOARD_ENTRIES; i++)
        {
            if (!string.IsNullOrEmpty(globalPlayerNames[i]))
            {
                if (dataToSave.Length > 0) dataToSave += "|";
                dataToSave += globalPlayerNames[i] + ":" + globalScores[i] + ":" + globalPlatforms[i];
            }
        }

        PlayerData.SetString(masterKey, dataToSave);
    }

    private bool UpdatePlayerInGlobalLeaderboard(string playerName, int playerScore, string platform)
    {
        int existingIndex = -1;
        for (int i = 0; i < MAX_LEADERBOARD_ENTRIES; i++)
        {
            if (globalPlayerNames[i] == playerName)
            {
                existingIndex = i;
                break;
            }
        }

        if (existingIndex >= 0)
        {
            if (playerScore > globalScores[existingIndex])
            {
                globalScores[existingIndex] = playerScore;
                globalPlatforms[existingIndex] = platform;
                return true;
            }
            return false;
        }
        else
        {
            int emptySlot = -1;
            for (int i = 0; i < MAX_LEADERBOARD_ENTRIES; i++)
            {
                if (string.IsNullOrEmpty(globalPlayerNames[i]))
                {
                    emptySlot = i;
                    break;
                }
            }

            if (emptySlot >= 0)
            {
                globalPlayerNames[emptySlot] = playerName;
                globalScores[emptySlot] = playerScore;
                globalPlatforms[emptySlot] = platform;
                return true;
            }
            else
            {
                int lowestIndex = 0;
                int lowestScore = globalScores[0];
                for (int i = 1; i < MAX_LEADERBOARD_ENTRIES; i++)
                {
                    if (globalScores[i] < lowestScore)
                    {
                        lowestScore = globalScores[i];
                        lowestIndex = i;
                    }
                }

                if (playerScore > lowestScore)
                {
                    globalPlayerNames[lowestIndex] = playerName;
                    globalScores[lowestIndex] = playerScore;
                    globalPlatforms[lowestIndex] = platform;
                    return true;
                }
            }
        }
        return false;
    }

    private void SortGlobalLeaderboard()
    {
        for (int i = 0; i < MAX_LEADERBOARD_ENTRIES - 1; i++)
        {
            for (int j = 0; j < MAX_LEADERBOARD_ENTRIES - i - 1; j++)
            {
                if (!string.IsNullOrEmpty(globalPlayerNames[j]) && !string.IsNullOrEmpty(globalPlayerNames[j + 1]))
                {
                    if (globalScores[j] < globalScores[j + 1])
                    {
                        string tempName = globalPlayerNames[j];
                        int tempScore = globalScores[j];
                        string tempPlatform = globalPlatforms[j];

                        globalPlayerNames[j] = globalPlayerNames[j + 1];
                        globalScores[j] = globalScores[j + 1];
                        globalPlatforms[j] = globalPlatforms[j + 1];

                        globalPlayerNames[j + 1] = tempName;
                        globalScores[j + 1] = tempScore;
                        globalPlatforms[j + 1] = tempPlatform;
                    }
                }
            }
        }
    }

    private void DisplayGlobalLeaderboard()
    {
        if (leaderboardDisplay == null || currentLeaderboardIndex >= leaderboardTitles.Length) return;

        string displayText = leaderboardTitles[currentLeaderboardIndex] + "\n\n";

        for (int i = 0; i < MAX_LEADERBOARD_ENTRIES; i++)
        {
            if (!string.IsNullOrEmpty(globalPlayerNames[i]))
            {
                displayText += (i + 1) + ". " + globalPlayerNames[i] + " [" + globalScores[i] + "] " + globalPlatforms[i] + "\n";
            }
        }

        if (string.IsNullOrEmpty(globalPlayerNames[0]))
        {
            displayText += "No scores recorded yet!";
        }

        leaderboardDisplay.text = displayText;
    }

    public void ForceUpdateLeaderboard()
    {
        UpdateGlobalLeaderboard();
    }

    public void ResetGlobalLeaderboard()
    {
        if (localPlayer != null && currentLeaderboardIndex < scoreKeys.Length)
        {
            for (int i = 0; i < MAX_LEADERBOARD_ENTRIES; i++)
            {
                globalPlayerNames[i] = "";
                globalScores[i] = 0;
                globalPlatforms[i] = "";
            }

            string masterKey = MASTER_LEADERBOARD_KEY + "_" + scoreKeys[currentLeaderboardIndex];
            PlayerData.SetString(masterKey, "");

            if (leaderboardDisplay != null && currentLeaderboardIndex < leaderboardTitles.Length)
            {
                leaderboardDisplay.text = leaderboardTitles[currentLeaderboardIndex] + "\n\nLeaderboard Reset!";
            }
        }
    }

    public void SwitchToNextLeaderboard()
    {
        SaveGlobalLeaderboard();
        currentLeaderboardIndex = (currentLeaderboardIndex + 1) % scoreKeys.Length;
        UpdateGlobalLeaderboard();
    }

    public void SwitchToPreviousLeaderboard()
    {
        SaveGlobalLeaderboard();
        currentLeaderboardIndex = (currentLeaderboardIndex - 1 + scoreKeys.Length) % scoreKeys.Length;
        UpdateGlobalLeaderboard();
    }

    public void SetLeaderboardIndex(int index)
    {
        if (index >= 0 && index < scoreKeys.Length)
        {
            SaveGlobalLeaderboard();
            currentLeaderboardIndex = index;
            UpdateGlobalLeaderboard();
        }
    }
}
