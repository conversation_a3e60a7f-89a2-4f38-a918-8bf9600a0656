
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GlobalLeaderBoardSystem : UdonSharpBehaviour
{
    public TextMeshPro leaderboardDisplay;
    public string[] scoreKeys = {"EmperCoins", "Floors"};
    public string[] leaderboardTitles = {"Global EmperCoins Top 10", "Global Floors Top 10"};
    public int currentLeaderboardIndex = 0;
    public float updateInterval = 30f;
    public bool enableDebugLogs = true;

    private const int MAX_LEADERBOARD_ENTRIES = 10;
    private VRCPlayerApi localPlayer;
    private bool isUpdating = false;

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
        if (localPlayer != null)
        {
            UpdateGlobalLeaderboard();
            SendCustomEventDelayedSeconds(nameof(PeriodicUpdate), updateInterval);
        }
    }

    public void PeriodicUpdate()
    {
        if (localPlayer != null)
        {
            UpdateGlobalLeaderboard();
            SendCustomEventDelayedSeconds(nameof(PeriodicUpdate), updateInterval);
        }
    }

    public void UpdateGlobalLeaderboard()
    {
        if (isUpdating || localPlayer == null || currentLeaderboardIndex >= scoreKeys.Length) return;
        isUpdating = true;

        string currentScoreKey = scoreKeys[currentLeaderboardIndex];
        string globalLeaderboardKey = "GlobalLeaderboard_" + currentScoreKey;

        int currentPlayerScore = PlayerData.GetInt(localPlayer, currentScoreKey);
        string currentPlayerName = localPlayer.displayName;
        string globalLeaderboardData = PlayerData.GetString(localPlayer, globalLeaderboardKey);
        string[] leaderboardEntries = ParseLeaderboard(globalLeaderboardData);
        bool playerUpdated = UpdatePlayerInLeaderboard(ref leaderboardEntries, currentPlayerName, currentPlayerScore);

        SortLeaderboard(ref leaderboardEntries);

        if (leaderboardEntries.Length > MAX_LEADERBOARD_ENTRIES)
        {
            string[] trimmedEntries = new string[MAX_LEADERBOARD_ENTRIES];
            for (int i = 0; i < MAX_LEADERBOARD_ENTRIES; i++){trimmedEntries[i] = leaderboardEntries[i];}
            leaderboardEntries = trimmedEntries;
        }

        if (playerUpdated)
        {
            string updatedLeaderboardData = SerializeLeaderboard(leaderboardEntries);
            PlayerData.SetString(globalLeaderboardKey, updatedLeaderboardData);

            if (enableDebugLogs){Debug.Log($"Global Leaderboard Updated: {currentScoreKey} - {currentPlayerName} with score {currentPlayerScore}");}
        }

        DisplayLeaderboard(leaderboardEntries);
        isUpdating = false;
    }

    private string[] ParseLeaderboard(string leaderboardData)
    {
        if (string.IsNullOrEmpty(leaderboardData)){return new string[0];}

        return leaderboardData.Split('|');
    }

    private bool UpdatePlayerInLeaderboard(ref string[] entries, string playerName, int playerScore)
    {
        for (int i = 0; i < entries.Length; i++)
        {
            if (!string.IsNullOrEmpty(entries[i]) && entries[i].Contains(":"))
            {
                string[] parts = entries[i].Split(':');
                if (parts.Length >= 2 && parts[0] == playerName)
                {
                    int existingScore = int.Parse(parts[1]);
                    if (playerScore > existingScore)
                    {
                        entries[i] = playerName + ":" + playerScore;
                        return true;
                    }
                    return false;
                }
            }
        }

        if (entries.Length < MAX_LEADERBOARD_ENTRIES)
        {
            string[] newEntries = new string[entries.Length + 1];
            for (int i = 0; i < entries.Length; i++){newEntries[i] = entries[i];}
            newEntries[entries.Length] = playerName + ":" + playerScore;
            entries = newEntries;
            return true;
        }
        else
        {
            int lowestScore = int.MaxValue;
            int lowestIndex = -1;

            for (int i = 0; i < entries.Length; i++)
            {
                if (!string.IsNullOrEmpty(entries[i]) && entries[i].Contains(":"))
                {
                    string[] parts = entries[i].Split(':');
                    if (parts.Length >= 2)
                    {
                        int score = int.Parse(parts[1]);
                        if (score < lowestScore)
                        {
                            lowestScore = score;
                            lowestIndex = i;
                        }
                    }
                }
            }

            if (playerScore > lowestScore && lowestIndex >= 0)
            {
                entries[lowestIndex] = playerName + ":" + playerScore;
                return true;
            }
        }

        return false;
    }

    private void SortLeaderboard(ref string[] entries)
    {
        for (int i = 0; i < entries.Length - 1; i++)
        {
            for (int j = 0; j < entries.Length - i - 1; j++)
            {
                if (!string.IsNullOrEmpty(entries[j]) && !string.IsNullOrEmpty(entries[j + 1]) && entries[j].Contains(":") && entries[j + 1].Contains(":"))
                {
                    string[] parts1 = entries[j].Split(':');
                    string[] parts2 = entries[j + 1].Split(':');

                    if (parts1.Length >= 2 && parts2.Length >= 2)
                    {
                        int score1 = int.Parse(parts1[1]);
                        int score2 = int.Parse(parts2[1]);

                        if (score1 < score2)
                        {
                            string temp = entries[j];
                            entries[j] = entries[j + 1];
                            entries[j + 1] = temp;
                        }
                    }
                }
            }
        }
    }

    private string SerializeLeaderboard(string[] entries)
    {
        string result = "";
        for (int i = 0; i < entries.Length; i++)
        {
            if (!string.IsNullOrEmpty(entries[i]))
            {
                if (result.Length > 0){result += "|";}
                result += entries[i];
            }
        }
        return result;
    }

    private void DisplayLeaderboard(string[] entries)
    {
        if (leaderboardDisplay == null || currentLeaderboardIndex >= leaderboardTitles.Length) return;

        string displayText = leaderboardTitles[currentLeaderboardIndex] + "\n\n";

        for (int i = 0; i < entries.Length && i < MAX_LEADERBOARD_ENTRIES; i++)
        {
            if (!string.IsNullOrEmpty(entries[i]) && entries[i].Contains(":"))
            {
                string[] parts = entries[i].Split(':');
                if (parts.Length >= 2)
                {
                    string playerName = parts[0];
                    string score = parts[1];
                    displayText += (i + 1) + ". " + playerName + " - " + score + "\n";
                }
            }
        }

        if (entries.Length == 0){displayText += "No scores recorded yet!";}

        leaderboardDisplay.text = displayText;

        if (enableDebugLogs){Debug.Log("Global Leaderboard Display Updated: " + entries.Length + " entries");}
    }

    public void ForceUpdateLeaderboard(){UpdateGlobalLeaderboard();}

    public void ResetGlobalLeaderboard()
    {
        if (localPlayer != null && currentLeaderboardIndex < scoreKeys.Length)
        {
            string currentScoreKey = scoreKeys[currentLeaderboardIndex];
            string globalLeaderboardKey = "GlobalLeaderboard_" + currentScoreKey;
            PlayerData.SetString(globalLeaderboardKey, "");
            if (leaderboardDisplay != null && currentLeaderboardIndex < leaderboardTitles.Length)
            {
                leaderboardDisplay.text = leaderboardTitles[currentLeaderboardIndex] + "\n\nLeaderboard Reset!";
            }

            if (enableDebugLogs){Debug.Log("Global Leaderboard Reset: " + currentScoreKey);}
        }
    }

    //public void SwitchToNextLeaderboard()
    //{
    //    currentLeaderboardIndex = (currentLeaderboardIndex + 1) % scoreKeys.Length;
    //    UpdateGlobalLeaderboard();
    //}

    //public void SwitchToPreviousLeaderboard()
    //{
    //    currentLeaderboardIndex = (currentLeaderboardIndex - 1 + scoreKeys.Length) % scoreKeys.Length;
    //    UpdateGlobalLeaderboard();
    //}

    //public void SetLeaderboardIndex(int index)
    //{
    //    if (index >= 0 && index < scoreKeys.Length)
    //    {
    //        currentLeaderboardIndex = index;
    //        UpdateGlobalLeaderboard();
    //    }
    //}
}
