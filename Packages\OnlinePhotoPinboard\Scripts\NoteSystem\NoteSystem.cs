﻿
using System;
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;


namespace OnlinePhotoPinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class NoteSystem : UdonSharpBehaviour
    {
        [Header("References")]
        public GameObject notePrefab;
        public Transform notesContainer;
        public BoxCollider pinboardCanvasBoxCollider;
        public NotePickup notePickup;
        public NoteDownloader noteDownloader;
        public PhotoDownloader photoDownloader;
        public PinboardSettings pinboardSettings;

        public Shader blurShader;
        public Shader defaultShader;
        public Color unapprovedColor;

        public float noteDistance = 0.01f;

        private GameObject[] imageGameobjects = new GameObject[64];

        [HideInInspector] public int noteNumber;


        private void Start()
        {
        }

        public void InstatiateMaterialPropertyBlockTo(Renderer renderer, Color color)
        {
            MaterialPropertyBlock propertyBlock = new MaterialPropertyBlock();
            renderer.GetPropertyBlock(propertyBlock);
            propertyBlock.SetColor("_Color", color);
            renderer.SetPropertyBlock(propertyBlock);
        }

        public GameObject GetNoteGameobjectFromIndex(int index)
        {
            if (imageGameobjects[index] == null)
            {
                noteNumber++;
                GameObject note = Instantiate(notePrefab, notesContainer);
                note.name = $"note {index}";
                imageGameobjects[index] = note;
                note.SetActive(false);
                return note;
                
            }
            else
            {
                return imageGameobjects[index];
            }
        }

        public void SetNoteImage(Texture2D image, int noteIndex)
        {
            //Debug.Log($"Settings note image for ${noteIndex}");
            GameObject note = GetNoteGameobjectFromIndex(noteIndex);
            Renderer noteBackgroundRenderer = note.transform.GetChild(0).GetComponent<Renderer>();

            Transform noteImagePart = noteBackgroundRenderer.transform.GetChild(0);
            Renderer renderer = noteImagePart.GetComponent<Renderer>();
            InstatiateMaterialPropertyBlockTo(renderer, Color.white);

            renderer.material.mainTexture = image;

        }

        public void BlurImage(int index, bool value)
        {
            GameObject note = GetNoteGameobjectFromIndex(index);
            Renderer noteBackgroundRenderer = note.transform.GetChild(0).GetComponent<Renderer>();

            Transform noteImagePart = noteBackgroundRenderer.transform.GetChild(0);
            Renderer renderer = noteImagePart.GetComponent<Renderer>();
            InstatiateMaterialPropertyBlockTo(renderer, Color.white);

            if(value)
            {
                renderer.material.shader = blurShader;
            }
            else
            {
                renderer.material.shader = defaultShader;
            }
        }



        public void CreateNote(int index, int startIndex, Vector3 localPos, float angle, float hue, string status)
        {
            GameObject note = GetNoteGameobjectFromIndex(index);
            //Debug.Log($"Getting {note.name} from index {index}");
            note.SetActive(true);

            note.transform.localPosition = new Vector3(localPos.x, localPos.y, 0);
            note.transform.position = pinboardCanvasBoxCollider.ClosestPoint(note.transform.position);

            float zOffset = (((index - startIndex) % 64 + 64) % 64) * -noteDistance;
            note.transform.localPosition += new Vector3(0, 0, zOffset);

            Quaternion localRotation = Quaternion.Euler(0, 0, angle);
            note.transform.localRotation = localRotation;


            Renderer noteBackgroundRenderer = note.transform.GetChild(0).GetComponent<Renderer>();

            // InstatiateMaterialPropertyBlockTo(noteBackgroundRenderer, GetColorFromHue(hue));


            noteBackgroundRenderer.material.color = GetColorFromHue(hue);


            if (status == "deleted")
            {
                note.SetActive(false);
            }

            if (status == "not-approved")
            {
                if (pinboardSettings.safeMode)
                {
                    noteBackgroundRenderer.material.color = unapprovedColor;
                    if (pinboardSettings.LocalPlayerIsInAdmin())
                    {
                        BlurImage(index, false);
                    }
                    else
                    {
                        BlurImage(index, true);
                    }
                }
            }
            if(status == "approved")
            {
                BlurImage(index, false);
            }


        }

        private Color GetColorFromHue(float hue)
        {
            Color.RGBToHSV(notePickup.defaultNoteColor, out float h, out float s, out float v);
            return Color.HSVToRGB(hue, s, v);
        }

        
    }
}