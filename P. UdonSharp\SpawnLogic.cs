﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using System;
using VRC.SDK3.Components;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class SpawnLogic : UdonSharpBehaviour
{
    public Transform teleportDestination,tutorialDestination;
    public GameObject Room,VideoPlayer;
    public GameObject[] SpawnMenu;
    public SpawnMenuSystem SpawnMenuSystem;
    public VRCPlayerApi localPlayer;
    public int CanClose = 0;
    public int HasSpawned = 0;

    public float PlayerHeight = 1.45f;

    public void Start()
    {
        localPlayer = Networking.LocalPlayer;
        if(Room != null){Room.SetActive(true);}
        VideoPlayer.SetActive(false);
        localPlayer.TeleportTo(teleportDestination.position, teleportDestination.rotation);
        SendCustomEventDelayedSeconds(nameof(SpawnPlayer), 120f);
        SendCustomEventDelayedSeconds(nameof(GetridofSpawnMenu), 1f);
    }

    public void SpawnPlayer()
    {
        if(CanClose < 2 && HasSpawned == 0){
            HasSpawned = 1;
            localPlayer.TeleportTo(new Vector3(0,0,0), new Quaternion(0,0,0,0)); 
            Room.SetActive(false); 
            VideoPlayer.SetActive(true);
            localPlayer.SetAvatarEyeHeightByMeters(PlayerHeight);
        }
    }
    public void SpawnTutorial(){
        HasSpawned = 1;
        localPlayer.TeleportTo(tutorialDestination.position, tutorialDestination.rotation);
        Room.SetActive(false);
        localPlayer.SetAvatarEyeHeightByMeters(PlayerHeight);
    }

    public void GetridofSpawnMenu(){for(int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(false);}}

    public void Spawned(){
        HasSpawned = 1;
        for(int i = 0; i < SpawnMenu.Length; i++){SpawnMenu[i].SetActive(true);}
        SpawnMenuSystem.CloseMenu();
        SpawnMenuSystem.MinimizedMode = true;
        SpawnMenuSystem.MenuContainer.SetActive(false);
        SpawnMenuSystem.spawnpoint.gameObject.SetActive(false);
        SpawnMenuSystem.MinimizedMenu.SetActive(true);
        SpawnMenuSystem.PickupCollider.enabled = false;

        //Set Height to 1.45f
        localPlayer.SetAvatarEyeHeightByMeters(PlayerHeight);
    }
    
    public override void OnPlayerTriggerEnter(VRCPlayerApi player){
        localPlayer = Networking.LocalPlayer;
        if (player != localPlayer) return;

        CanClose++;
        if(CanClose >= 2){
            Room.SetActive(false);
            VideoPlayer.SetActive(true);
            HasSpawned = 1;
            localPlayer.SetAvatarEyeHeightByMeters(PlayerHeight);
        }
    }
}
