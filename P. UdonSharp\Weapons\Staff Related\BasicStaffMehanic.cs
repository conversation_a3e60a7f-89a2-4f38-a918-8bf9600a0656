using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class BasicStaffMehanic : UdonSharpBehaviour
{
    public int Stafftype,CooldownDelay,CooldownFinishesAt; //0 is Staff, 1 is Weapon, 2 Harpoon Gun, 3 Sentry
    public GameObject[] Minions; // Array of minions instead of a single minion
    public int MinionCount = 1; // How many minions to spawn (default: 1 for backward compatibility)
    private int CurrentMinionIndex = 0; // Track which minion was last spawned
    private GameObject[] ActiveMinions; // Track active minions
    public AudioSource AudioSource;
    public ParticleSystem Particle;
    public float HarpoonGunSpeed;
    public Transform HarpoonPosition;
    public HpoonOutput HarpoonGunOutput;

    void Start()
    {
        CooldownDelay = CooldownFinishesAt;
        // Initialize the active minions array
        ActiveMinions = new GameObject[MinionCount];

        // Limit MinionCount to the number of available minions
        if (MinionCount > Minions.Length)
        {
            MinionCount = Minions.Length;
        }
    }

    public void Cooldown(){if(CooldownDelay < CooldownFinishesAt){CooldownDelay++; SendCustomEventDelayedSeconds(nameof(Cooldown), 0.1f);}}

    public override void OnPickupUseDown()
    {
        if(Stafftype == 0){
            // Spawn multiple minions for staff type 0
            for (int i = 0; i < MinionCount; i++)
            {
                // Calculate spawn position with slight offset for each minion
                Vector3 spawnPosition = gameObject.transform.position;
                if (i > 0)
                {
                    // Add a small offset for each additional minion
                    spawnPosition += new Vector3(
                        Random.Range(-0.3f, 0.3f),
                        Random.Range(0.1f, 0.3f),
                        Random.Range(-0.3f, 0.3f)
                    );
                }

                // Get the minion from the array using modulo to cycle through available minions
                int minionIndex = i % Minions.Length;
                GameObject minion = Minions[minionIndex];

                minion.SetActive(true);
                minion.transform.position = spawnPosition;
                minion.transform.rotation = gameObject.transform.rotation;
                minion.transform.parent = null;

                // Store reference to the active minion
                ActiveMinions[i] = minion;
            }
        }
        if(AudioSource != null && Stafftype == 1 && CooldownDelay >= CooldownFinishesAt){
            // Spawn multiple minions for staff type 1
            for (int i = 0; i < MinionCount; i++)
            {
                // Calculate spawn position with slight offset for each minion
                Vector3 spawnPosition = gameObject.transform.position;
                if (i > 0)
                {
                    // Add a small offset for each additional minion
                    spawnPosition += new Vector3(
                        Random.Range(-0.3f, 0.3f),
                        Random.Range(0.1f, 0.3f),
                        Random.Range(-0.3f, 0.3f)
                    );
                }

                // Get the minion from the array using modulo to cycle through available minions
                int minionIndex = i % Minions.Length;
                GameObject minion = Minions[minionIndex];

                minion.SetActive(true);
                minion.transform.position = spawnPosition;
                minion.transform.rotation = gameObject.transform.rotation;
                minion.transform.parent = null;

                // Store reference to the active minion
                ActiveMinions[i] = minion;
            }

            AudioSource.PlayOneShot(AudioSource.clip);
            if(Particle != null) {Particle.Play();}
            CooldownDelay = 0;
            SendCustomEventDelayedSeconds(nameof(Cooldown), 0.1f);
        }
        if(AudioSource != null && Stafftype == 2 && CooldownDelay >= CooldownFinishesAt){
            // For harpoon gun, we'll still use just the first minion
            GameObject minion = Minions[0];

            minion.transform.position = HarpoonPosition.position;
            minion.transform.rotation = HarpoonPosition.rotation;
            minion.transform.parent = null;
            minion.GetComponent<Rigidbody>().isKinematic = false;
            minion.GetComponent<Rigidbody>().velocity = Vector3.zero;
            minion.GetComponent<Rigidbody>().angularVelocity = Vector3.zero;
            minion.GetComponent<Rigidbody>().velocity = -minion.transform.forward * HarpoonGunSpeed;
            AudioSource.PlayOneShot(AudioSource.clip);
            if(Particle != null) {Particle.Play();}
            CooldownDelay = 0;
            HarpoonGunOutput.IsActive = 1;
            SendCustomEventDelayedSeconds(nameof(Cooldown), 0.1f);

            // Store reference to the active minion
            ActiveMinions[0] = minion;
        }
    }

    public override void OnPickupUseUp()
    {
        if(Stafftype == 0 || Stafftype == 1){
            // Deactivate all active minions
            for (int i = 0; i < MinionCount; i++)
            {
                if (ActiveMinions[i] != null)
                {
                    ActiveMinions[i].SetActive(false);
                    ActiveMinions[i].transform.parent = gameObject.transform;
                }
            }
        }
        else if (Stafftype == 2){
            // For harpoon gun, we'll still use just the first minion
            if (ActiveMinions[0] != null)
            {
                GameObject minion = ActiveMinions[0];
                minion.transform.parent = gameObject.transform;
                minion.GetComponent<Rigidbody>().isKinematic = true;
                minion.GetComponent<Rigidbody>().velocity = Vector3.zero;
                minion.transform.position = HarpoonPosition.position;
                minion.transform.rotation = HarpoonPosition.rotation;
                HarpoonGunOutput.IsActive = 0;
            }
        }
    }
}
