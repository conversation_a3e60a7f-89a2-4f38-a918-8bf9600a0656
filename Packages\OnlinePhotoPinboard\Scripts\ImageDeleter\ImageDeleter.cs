﻿
using UdonSharp;
using UnityEngine;
using VRC.SDK3.StringLoading;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Udon.Common.Interfaces;

namespace OnlinePhotoPinboard
{
    public class ImageDeleter : UdonSharpBehaviour
    {
        [HideInInspector] public VRCUrl[] imageDeleteUrls;
        public NoteDownloader noteDownloader;
        public PhotoSerializer photoSerializer;


        public void DeleteImageAtIndex(int index)
        {
            if (index < 0 || index >= imageDeleteUrls.Length)
            {
                Debug.LogError($"[ImageDeleter] could not delete image at index {index}. DeleteUrls lenght = {imageDeleteUrls.Length}");
                return;
            }

            //Debug.Log($"Loading url {imageDeleteUrls[index]} from index {index}");
            VRCStringDownloader.LoadUrl(imageDeleteUrls[index], (IUdonEventReceiver)this);

            noteDownloader.DeleteImageAtIndexSync(index);


        }
        public override void OnStringLoadSuccess(IVRCStringDownload result)
        {
            string resultJson = result.Result;
            //Debug.Log("Image deleted successfully");
        }

        public override void OnStringLoadError(IVRCStringDownload result)
        {
            Debug.LogError($"Error loading string: {result.ErrorCode} - {result.Error}");
        }
    }
}