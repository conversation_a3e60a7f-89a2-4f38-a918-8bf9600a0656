; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2024
		Month: 8
		Day: 29
		Hour: 10
		Minute: 54
		Second: 40
		Millisecond: 344
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:\Users\<USER>\Desktop\New folder\Kon Rusk Test\Assets\exported models\spirit armor\jmuh1x6g_Sceptre.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:\Users\<USER>\Desktop\New folder\Kon Rusk Test\Assets\exported models\spirit armor\jmuh1x6g_Sceptre.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2316907712128, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2311955038944, "Geometry::Scene", "Mesh" {
		Vertices: *102 {
			a: 2.50000040978193,28.0875414609909,2.50000040978193,-2.5000000372529,28.0875414609909,2.50000040978193,5.41681014001369,34.0655118227005,5.41684627532959,-5.41681088507175,34.0655118227005,5.41684627532959,2.50000040978193,28.0875414609909,-2.5000000372529,5.41681014001369,34.0655118227005,-5.41684627532959,-2.5000000372529,28.0875414609909,-2.5000000372529,-5.41681088507175,34.0655118227005,-5.41684627532959,1.90337281674147,-48.2613682746887,1.90338920801878,-1.90338119864464,-48.2613682746887,1.90338920801878,-1.90337914973497,-7.0286326110363,1.9033906981349,1.90337877720594,-7.0286326110363,1.9033906981349,-1.70450229197741,3.4122534096241,1.70450191944838,-1.70450229197741,22.1213191747665,1.70450191944838,1.70450191944838,22.1213191747665,1.70450191944838,1.70450191944838,3.4122534096241,1.70450191944838,-1.90338119864464,-48.2613682746887,-1.90340094268322,-1.90337914973497,-7.0286326110363,-1.90339107066393,-1.70450229197741,3.4122534096241,-1.70450229197741,-1.70450229197741,22.1213191747665,-1.70450229197741,1.90337281674147,-48.2613682746887,-1.90340094268322,1.90337877720594,-7.0286326110363,-1.90339107066393,1.70450191944838,3.4122534096241,-1.70450229197741,1.70450191944838,22.1213191747665,-1.70450229197741,2.49999742954969,-2.55396887660027,-2.50000972300768,2.49999742954969,-2.55396887660027,2.50000935047865,-2.49999780207872,-2.55396887660027,2.50000935047865,-2.49999780207872,-2.55396887660027,-2.50000972300768,-2.22887415191053e-06,-52.7360379695892,-9.20119020975108e-06,5.41681014001369,45.9226429462433,5.41684627532959,-5.41681088507175,45.9226429462433,5.41684627532959,-2.33310704089718e-07,52.7360141277313,-1.43575817901365e-07,5.41681014001369,45.9226429462433,-5.41684627532959,-5.41681088507175,45.9226429462433,-5.41684627532959
		} 
		PolygonVertexIndex: *180 {
			a: 28,8,-10,28,9,-17,28,16,-21,28,20,-9,29,31,-31,32,31,-30,30,31,-34,33,31,-33,0,3,-2,3,0,-3,4,2,-1,2,4,-6,1,7,-7,7,1,-4,6,5,-5,5,6,-8,8,10,-10,10,8,-12,15,13,-13,13,15,-15,9,17,-17,17,9,-11,12,19,-19,19,12,-14,20,17,-22,17,20,-17,22,19,-24,19,22,-19,8,21,-12,21,8,-21,15,23,-15,23,15,-23,1,14,-1,14,1,-14,0,23,-5,23,0,-15,4,19,-7,19,4,-24,24,15,-26,15,24,-23,25,12,-27,12,25,-16,6,13,-2,13,6,-20,26,18,-28,18,26,-13,27,22,-25,22,27,-19,27,10,-27,10,27,-18,25,21,-25,21,25,-12,26,11,-26,11,26,-11,24,17,-28,17,24,-22,33,29,-31,29,33,-33,7,2,-6,2,7,-4
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *540 {
				a: -0,-0.39143043756485,0.920207738876343,-0,-0.39143043756485,0.920207738876343,-0,-0.39143043756485,0.920207738876343,-0.920209169387817,-0.39142706990242,0,-0.920209169387817,-0.39142706990242,0,-0.920209169387817,-0.39142706990242,0,-0,-0.391429275274277,-0.920208215713501,-0,-0.391429275274277,-0.920208215713501,-0,-0.391429275274277,-0.920208215713501,0.920209407806396,-0.391426354646683,0,0.920209407806396,-0.391426354646683,0,0.920209407806396,-0.391426354646683,0,-0,0.622320830821991,0.782762289047241,-0,0.622320830821991,0.782762289047241,-0,0.622320830821991,0.782762289047241,0.782764256000519,0.62231832742691,0,0.782764256000519,0.62231832742691,0,0.782764256000519,0.62231832742691,0,-0.782764256000519,0.62231832742691,0,-0.782764256000519,0.62231832742691,0,-0.782764256000519,0.62231832742691,0,-0,0.622320830821991,-0.782762289047241,-0,0.622320830821991,-0.782762289047241,-0,0.622320830821991,-0.782762289047241,-0,-0.43851625919342,0.898723244667053,-0,-0.43851625919342,0.898723244667053,-0,-0.43851625919342,0.898723244667053,-0,-0.43851625919342,0.898723244667053,-0,-0.43851625919342,0.898723244667053,-0,-0.43851625919342,0.898723244667053,0.898725390434265,-0.438511878252029,0,0.898725390434265,-0.438511908054352,0,0.898725390434265,-0.438511908054352,0,0.898725390434265,-0.438511908054352,0,0.898725390434265,-0.438511878252029,0,0.898725390434265,-0.438511908054352,0,-0.89872533082962,-0.438511997461319,0,-0.89872533082962,-0.438512027263641,0,-0.89872533082962,-0.438512027263641,0,-0.89872533082962,-0.438512027263641,0,-0.89872533082962,-0.438511997461319,0,-0.89872533082962,-0.438512027263641,0,-0,-0.438516288995743,-0.898723244667053,-0,-0.438516318798065,-0.898723244667053,-0,-0.438516318798065,-0.898723244667053,-0,-0.438516318798065,-0.898723244667053,-0,-0.438516288995743,-0.898723244667053,-0,-0.438516318798065,-0.898723244667053,-0,-3.61391521153109e-08,1,-0,-3.61391521153109e-08,1,-0,-3.61391521153109e-08,1,-0,-3.61391521153109e-08,1,-0,-3.61391521153109e-08,1,-0,-3.61391521153109e-08,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,4.96913337144633e-08,0,-1,4.96913337144633e-08,0,-1,4.96913337144633e-08,0,-1,4.96913337144633e-08,0,-1,4.96913337144633e-08,0,-1,4.96913337144633e-08,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,2.39421893866165e-07,-1,-0,2.39421893866165e-07,-1,-0,2.39421893866165e-07,-1,-0,2.39421893866165e-07,-1,-0,2.39421893866165e-07,-1,-0,2.39421893866165e-07,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,-1.44556608461244e-07,0,1,-1.44556608461244e-07,0,1,-1.44556608461244e-07,0,1,-1.44556608461244e-07,0,1,-1.44556608461244e-07,0,1,-1.44556608461244e-07,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,-0.132164090871811,0.991227865219116,-0,-0.13216407597065,0.991227865219116,-0,-0.132164090871811,0.991227865219116,-0,-0.13216407597065,0.991227865219116,-0,-0.132164090871811,0.991227865219116,-0,-0.132164090871811,0.991227865219116,0.991227865219116,-0.132164090871811,0,0.991227865219116,-0.13216407597065,0,0.991227865219116,-0.132164090871811,0,0.991227865219116,-0.13216407597065,0,0.991227865219116,-0.132164090871811,0,0.991227865219116,-0.132164090871811,0,-0,-0.13216395676136,-0.991227924823761,-0,-0.13216395676136,-0.991227865219116,-0,-0.13216395676136,-0.991227865219116,-0,-0.13216395676136,-0.991227865219116,-0,-0.13216395676136,-0.991227924823761,-0,-0.13216395676136,-0.991227865219116,0.991227924823761,0.13216358423233,0,0.991227865219116,0.13216358423233,0,0.991227865219116,0.13216358423233,0,0.991227865219116,0.13216358423233,0,0.991227924823761,0.13216358423233,0,0.991227865219116,0.13216358423233,0,-0,0.132165536284447,0.991227686405182,-0,0.132165551185608,0.991227686405182,-0,0.132165551185608,0.991227686405182,-0,0.132165551185608,0.991227686405182,-0,0.132165536284447,0.991227686405182,-0,0.132165551185608,0.991227686405182,-0.991227924823761,-0.13216395676136,0,-0.991227865219116,-0.13216395676136,0,-0.991227865219116,-0.13216395676136,0,-0.991227865219116,-0.13216395676136,0,-0.991227924823761,-0.13216395676136,0,-0.991227865219116,-0.13216395676136,0,-0.991227924823761,0.13216358423233,0,
-0.991227865219116,0.13216358423233,0,-0.991227865219116,0.13216358423233,0,-0.991227865219116,0.13216358423233,0,-0.991227924823761,0.13216358423233,0,-0.991227865219116,0.13216358423233,0,-0,0.132165536284447,-0.991227686405182,-0,0.132165551185608,-0.991227686405182,-0,0.132165551185608,-0.991227686405182,-0,0.132165551185608,-0.991227686405182,-0,0.132165536284447,-0.991227686405182,-0,0.132165551185608,-0.991227686405182,-0.991227984428406,-0.132163032889366,0,-0.991227984428406,-0.132163032889366,0,-0.991227984428406,-0.132163032889366,0,-0.991227984428406,-0.132163032889366,0,-0.991227984428406,-0.132163032889366,0,-0.991227984428406,-0.132163032889366,0,0.991227984428406,-0.132163032889366,0,0.991227984428406,-0.132163032889366,0,0.991227984428406,-0.132163032889366,0,0.991227984428406,-0.132163032889366,0,0.991227984428406,-0.132163032889366,0,0.991227984428406,-0.132163032889366,0,-0,-0.132163032889366,0.991228044033051,-0,-0.132163032889366,0.991228044033051,-0,-0.132163032889366,0.991228044033051,-0,-0.132163032889366,0.991228044033051,-0,-0.132163032889366,0.991228044033051,-0,-0.132163032889366,0.991228044033051,-0,-0.132163032889366,-0.991228044033051,-0,-0.132163032889366,-0.991228044033051,-0,-0.132163032889366,-0.991228044033051,-0,-0.132163032889366,-0.991228044033051,-0,-0.132163032889366,-0.991228044033051,-0,-0.132163032889366,-0.991228044033051,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0
			} 
			NormalsW: *180 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *540 {
				a: -0,0.920207738876343,0.39143043756485,-0,0.920207738876343,0.39143043756485,-0,0.920207738876343,0.39143043756485,-0.39142706990242,0.920209169387817,-0,-0.39142706990242,0.920209169387817,-0,-0.39142706990242,0.920209169387817,-0,0,0.920208215713501,-0.391429275274277,0,0.920208215713501,-0.391429275274277,0,0.920208215713501,-0.391429275274277,0.391426354646683,0.920209407806396,-0,0.391426354646683,0.920209407806396,-0,0.391426354646683,0.920209407806396,-0,0,0.782762289047241,-0.622320830821991,0,0.782762289047241,-0.622320830821991,0,0.782762289047241,-0.622320830821991,-0.62231832742691,0.782764256000519,0,-0.62231832742691,0.782764256000519,0,-0.62231832742691,0.782764256000519,0,0.62231832742691,0.782764256000519,-0,0.62231832742691,0.782764256000519,-0,0.62231832742691,0.782764256000519,-0,0,0.782762289047241,0.622320830821991,0,0.782762289047241,0.622320830821991,0,0.782762289047241,0.622320830821991,-0,0.898723244667053,0.43851625919342,-0,0.898723244667053,0.43851625919342,-0,0.898723244667053,0.43851625919342,-0,0.898723244667053,0.43851625919342,-0,0.898723244667053,0.43851625919342,-0,0.898723244667053,0.43851625919342,0.438511878252029,0.898725390434265,-0,0.438511908054352,0.898725390434265,-0,0.438511908054352,0.898725390434265,-0,0.438511908054352,0.898725390434265,-0,0.438511878252029,0.898725390434265,-0,0.438511908054352,0.898725390434265,-0,-0.438511997461319,0.89872533082962,-0,-0.438512027263641,0.89872533082962,-0,-0.438512027263641,0.89872533082962,-0,-0.438512027263641,0.89872533082962,-0,-0.438511997461319,0.89872533082962,-0,-0.438512027263641,0.89872533082962,-0,0,0.898723244667053,-0.438516288995743,0,0.898723244667053,-0.438516318798065,0,0.898723244667053,-0.438516318798065,0,0.898723244667053,-0.438516318798065,0,0.898723244667053,-0.438516288995743,0,0.898723244667053,-0.438516318798065,-0,1,3.61391521153109e-08,-0,1,3.61391521153109e-08,-0,1,3.61391521153109e-08,-0,1,3.61391521153109e-08,-0,1,3.61391521153109e-08,-0,1,3.61391521153109e-08,0,1,-0,0,1,-0,0,1,-0,
0,1,-0,0,1,-0,0,1,-0,4.96913337144633e-08,1,-0,4.96913337144633e-08,1,-0,4.96913337144633e-08,1,-0,4.96913337144633e-08,1,-0,4.96913337144633e-08,1,-0,4.96913337144633e-08,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,2.39421893866165e-07,0,1,2.39421893866165e-07,0,1,2.39421893866165e-07,0,1,2.39421893866165e-07,0,1,2.39421893866165e-07,0,1,2.39421893866165e-07,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1.44556608461244e-07,1,-0,1.44556608461244e-07,1,-0,1.44556608461244e-07,1,-0,1.44556608461244e-07,1,-0,1.44556608461244e-07,1,-0,1.44556608461244e-07,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,0.991227865219116,0.132164090871811,-0,0.991227865219116,0.13216407597065,-0,0.991227865219116,0.132164090871811,-0,0.991227865219116,0.13216407597065,-0,0.991227865219116,0.132164090871811,-0,0.991227865219116,0.132164090871811,0.132164090871811,0.991227865219116,-0,0.13216407597065,0.991227865219116,-0,0.132164090871811,0.991227865219116,-0,0.13216407597065,0.991227865219116,-0,0.132164090871811,0.991227865219116,-0,0.132164090871811,0.991227865219116,-0,0,0.991227924823761,-0.13216395676136,0,0.991227865219116,-0.13216395676136,0,0.991227865219116,-0.13216395676136,0,0.991227865219116,-0.13216395676136,0,0.991227924823761,-0.13216395676136,0,0.991227865219116,-0.13216395676136,-0.13216358423233,0.991227924823761,0,-0.13216358423233,0.991227865219116,0,-0.13216358423233,0.991227865219116,0,-0.13216358423233,0.991227865219116,0,-0.13216358423233,0.991227924823761,0,-0.13216358423233,0.991227865219116,0,0,0.991227686405182,-0.132165536284447,0,0.991227686405182,-0.132165551185608,0,0.991227686405182,-0.132165551185608,0,0.991227686405182,-0.132165551185608,0,0.991227686405182,-0.132165536284447,0,0.991227686405182,-0.132165551185608,-0.13216395676136,0.991227924823761,-0,-0.13216395676136,0.991227865219116,-0,-0.13216395676136,0.991227865219116,-0,-0.13216395676136,0.991227865219116,-0,-0.13216395676136,0.991227924823761,-0,-0.13216395676136,0.991227865219116,-0,0.13216358423233,0.991227924823761,-0,0.13216358423233,0.991227865219116,-0,
0.13216358423233,0.991227865219116,-0,0.13216358423233,0.991227865219116,-0,0.13216358423233,0.991227924823761,-0,0.13216358423233,0.991227865219116,-0,0,0.991227686405182,0.132165536284447,0,0.991227686405182,0.132165551185608,0,0.991227686405182,0.132165551185608,0,0.991227686405182,0.132165551185608,0,0.991227686405182,0.132165536284447,0,0.991227686405182,0.132165551185608,-0.132163032889366,0.991227984428406,-0,-0.132163032889366,0.991227984428406,-0,-0.132163032889366,0.991227984428406,-0,-0.132163032889366,0.991227984428406,-0,-0.132163032889366,0.991227984428406,-0,-0.132163032889366,0.991227984428406,-0,0.132163032889366,0.991227984428406,-0,0.132163032889366,0.991227984428406,-0,0.132163032889366,0.991227984428406,-0,0.132163032889366,0.991227984428406,-0,0.132163032889366,0.991227984428406,-0,0.132163032889366,0.991227984428406,-0,-0,0.991228044033051,0.132163032889366,-0,0.991228044033051,0.132163032889366,-0,0.991228044033051,0.132163032889366,-0,0.991228044033051,0.132163032889366,-0,0.991228044033051,0.132163032889366,-0,0.991228044033051,0.132163032889366,0,0.991228044033051,-0.132163032889366,0,0.991228044033051,-0.132163032889366,0,0.991228044033051,-0.132163032889366,0,0.991228044033051,-0.132163032889366,0,0.991228044033051,-0.132163032889366,0,0.991228044033051,-0.132163032889366,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1
			} 
			BinormalsW: *180 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *540 {
				a: 1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			TangentsW: *180 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *256 {
				a: 0.0250000040978193,0.263392180204391,-0.025000000372529,0.263392180204391,0.0541681014001369,0.329908430576324,-0.0541681088507175,0.329908430576324,0.025000000372529,0.26339265704155,-0.0250000040978193,0.26339265704155,0.0541684627532959,0.329908758401871,-0.0541684627532959,0.329908758401871,0.0250000040978193,0.26339265704155,-0.025000000372529,0.26339265704155,0.0541684627532959,0.329908758401871,-0.0541684627532959,0.329908758401871,0.025000000372529,0.263392180204391,-0.0250000040978193,0.263392180204391,0.0541681088507175,0.329908430576324,-0.0541681014001369,0.329908430576324,0.0190337281674147,-0.482613682746887,-0.0190338119864464,-0.482613682746887,-0.0190337914973497,-0.070286326110363,0.0190337877720594,-0.070286326110363,-0.0170450229197741,0.034122534096241,-0.0170450229197741,0.221213191747665,0.0170450191944838,0.221213191747665,0.0170450191944838,0.034122534096241,0.0190338920801878,-0.482613682746887,-0.0190340094268322,-0.482613682746887,-0.0190339107066393,-0.070286326110363,0.019033906981349,-0.070286326110363,-0.0170450229197741,0.034122534096241,-0.0170450229197741,0.221213191747665,0.0170450191944838,0.221213191747665,0.0170450191944838,0.034122534096241,0.0190338119864464,-0.482613682746887,-0.0190337281674147,-0.482613682746887,-0.0190337877720594,-0.0702863335609436,0.0190337914973497,-0.0702863335609436,-0.0170450191944838,0.034122534096241,-0.0170450191944838,0.221213191747665,0.0170450229197741,0.221213191747665,0.0170450229197741,0.034122534096241,0.0190340094268322,-0.482613682746887,-0.0190338920801878,-0.482613682746887,-0.019033906981349,-0.070286326110363,0.0190339107066393,-0.070286326110363,-0.0170450191944838,0.034122534096241,-0.0170450191944838,0.221213191747665,0.0170450229197741,0.221213191747665,0.0170450229197741,0.034122534096241,-0.025000000372529,0.281715631484985,0.0250000040978193,0.281715631484985,-0.0170450229197741,0.22152541577816,0.0170450191944838,0.22152541577816,-0.0250000040978193,0.281715631484985,0.025000000372529,0.281715631484985,
-0.0170450191944838,0.22152541577816,0.0170450229197741,0.22152541577816,-0.0250000040978193,0.281715631484985,0.025000000372529,0.281715631484985,-0.0170450191944838,0.22152541577816,0.0170450229197741,0.22152541577816,0.0250000972300768,-0.0286197382956743,-0.0250000935047865,-0.0286197382956743,0.0170450229197741,0.0315704792737961,-0.0170450191944838,0.0315704792737961,0.0249999742954969,-0.0286197979003191,-0.0249999780207872,-0.0286197979003191,0.0170450191944838,0.0315704345703125,-0.0170450229197741,0.0315704345703125,-0.025000000372529,0.281715631484985,0.0250000040978193,0.281715631484985,-0.0170450229197741,0.22152541577816,0.0170450191944838,0.22152541577816,0.0250000935047865,-0.0286197401583195,-0.0250000972300768,-0.0286197401583195,0.0170450191944838,0.0315704755485058,-0.0170450229197741,0.0315704755485058,0.0249999780207872,-0.0286197979003191,-0.0249999742954969,-0.0286197979003191,0.0170450229197741,0.0315704345703125,-0.0170450191944838,0.0315704345703125,-2.22887415191053e-08,-0.485281139612198,0.0190337281674147,-0.436654388904572,-0.0190338119864464,-0.436654388904572,-0.0250000972300768,-0.0220115818083286,0.0250000935047865,-0.0220115818083286,-0.0190339107066393,-0.0671542063355446,0.019033906981349,-0.0671542063355446,-0.0250000935047865,-0.0220115818083286,0.0250000972300768,-0.0220115818083286,-0.019033906981349,-0.0671542137861252,0.0190339107066393,-0.0671542137861252,-0.0249999780207872,-0.0220115669071674,0.0249999742954969,-0.0220115669071674,-0.0190337914973497,-0.0671541914343834,0.0190337877720594,-0.0671541914343834,-9.20119020975108e-08,-0.485281854867935,0.0190338920801878,-0.436655193567276,-0.0190340094268322,-0.436655193567276,2.22887415191053e-08,-0.485281318426132,0.0190338119864464,-0.436654597520828,-0.0190337281674147,-0.436654597520828,9.20119020975108e-08,-0.485282003879547,0.0190340094268322,-0.436655342578888,-0.0190338920801878,-0.436655342578888,-0.0249999742954969,-0.0220115650445223,0.0249999780207872,-0.0220115650445223,-0.0190337877720594,-0.0671541914343834,
0.0190337914973497,-0.0671541914343834,0.0541681014001369,0.325754970312119,-0.0541681088507175,0.325754970312119,-2.33310704089718e-09,0.412797629833221,0.0541684627532959,0.325756222009659,-0.0541684627532959,0.325756222009659,1.43575817901365e-09,0.412798672914505,0.0541684627532959,0.325756222009659,-0.0541684627532959,0.325756222009659,-1.43575817901365e-09,0.412798672914505,0.0541681088507175,0.325754970312119,-0.0541681014001369,0.325754970312119,2.33310704089718e-09,0.412797629833221,0.0541681014001369,0.0541684627532959,-0.0541681088507175,0.0541684627532959,-0.0541681088507175,-0.0541684627532959,0.0541681014001369,-0.0541684627532959,0.0541681088507175,0.0541684627532959,-0.0541681014001369,0.0541684627532959,-0.0541681014001369,-0.0541684627532959,0.0541681088507175,-0.0541684627532959
			} 
			UVIndex: *180 {
				a: 80,81,82,95,96,97,98,99,100,101,102,103,108,110,109,111,113,112,114,116,115,117,119,118,0,3,1,3,0,2,4,7,5,7,4,6,8,11,9,11,8,10,12,15,13,15,12,14,16,18,17,18,16,19,23,21,20,21,23,22,24,26,25,26,24,27,31,29,28,29,31,30,33,35,34,35,33,32,36,38,37,38,36,39,41,43,42,43,41,40,44,46,45,46,44,47,48,51,49,51,48,50,52,55,53,55,52,54,56,59,57,59,56,58,60,63,61,63,60,62,64,67,65,67,64,66,68,71,69,71,68,70,72,75,73,75,72,74,76,79,77,79,76,78,83,86,84,86,83,85,87,90,88,90,87,89,91,94,92,94,91,93,104,107,105,107,104,106,122,120,121,120,122,123,127,125,126,125,127,124
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2314139890448, "Model::Sceptre", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Rotation", "Lcl Rotation", "", "A",81.5675506591797,179.467468261719,170.499908447266
			P: "Lcl Scaling", "Lcl Scaling", "", "A",0.749999940395355,0.750000238418579,0.75000011920929
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2312539140960, "Material::BlackHole_og", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0,0,0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0,0,0
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Sceptre, Model::RootNode
	C: "OO",2314139890448,0
	
	;Material::BlackHole_og, Model::Sceptre
	C: "OO",2312539140960,2314139890448
	
	;Geometry::Scene, Model::Sceptre
	C: "OO",2311955038944,2314139890448
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
