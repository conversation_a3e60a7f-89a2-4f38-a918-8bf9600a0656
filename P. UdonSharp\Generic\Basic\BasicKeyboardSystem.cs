﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
public class BasicKeyboardSystem : UdonSharpBehaviour
{
    public TextMeshProUGUI Text;
    public TextMeshProUGUI[] LetterText;
    [UdonSynced] public string text;
    public bool CapsLock;
    public bool IsSyncable = true;
    public bool LimitReached;
    [UdonSynced] public int LimitValue;
    public int LimitMax;

    //Letters
    public void A(){if(!LimitReached){if(CapsLock == false){Text.text += "a";}else{Text.text += "A";} IncreaseLimit();}}
    public void B(){if(!LimitReached){if(CapsLock == false){Text.text += "b";}else{Text.text += "B";} IncreaseLimit();}}
    public void C(){if(!LimitReached){if(CapsLock == false){Text.text += "c";}else{Text.text += "C";} IncreaseLimit();}}
    public void D(){if(!LimitReached){if(CapsLock == false){Text.text += "d";}else{Text.text += "D";} IncreaseLimit();}}
    public void E(){if(!LimitReached){if(CapsLock == false){Text.text += "e";}else{Text.text += "E";} IncreaseLimit();}}
    public void F(){if(!LimitReached){if(CapsLock == false){Text.text += "f";}else{Text.text += "F";} IncreaseLimit();}}
    public void G(){if(!LimitReached){if(CapsLock == false){Text.text += "g";}else{Text.text += "G";} IncreaseLimit();}}
    public void H(){if(!LimitReached){if(CapsLock == false){Text.text += "h";}else{Text.text += "H";} IncreaseLimit();}}
    public void I(){if(!LimitReached){if(CapsLock == false){Text.text += "i";}else{Text.text += "I";} IncreaseLimit();}}
    public void J(){if(!LimitReached){if(CapsLock == false){Text.text += "j";}else{Text.text += "J";} IncreaseLimit();}}
    public void K(){if(!LimitReached){if(CapsLock == false){Text.text += "k";}else{Text.text += "K";} IncreaseLimit();}}
    public void L(){if(!LimitReached){if(CapsLock == false){Text.text += "l";}else{Text.text += "L";} IncreaseLimit();}}
    public void M(){if(!LimitReached){if(CapsLock == false){Text.text += "m";}else{Text.text += "M";} IncreaseLimit();}}
    public void N(){if(!LimitReached){if(CapsLock == false){Text.text += "n";}else{Text.text += "N";} IncreaseLimit();}}
    public void O(){if(!LimitReached){if(CapsLock == false){Text.text += "o";}else{Text.text += "O";} IncreaseLimit();}}
    public void P(){if(!LimitReached){if(CapsLock == false){Text.text += "p";}else{Text.text += "P";} IncreaseLimit();}}
    public void Q(){if(!LimitReached){if(CapsLock == false){Text.text += "q";}else{Text.text += "Q";} IncreaseLimit();}}
    public void R(){if(!LimitReached){if(CapsLock == false){Text.text += "r";}else{Text.text += "R";} IncreaseLimit();}}
    public void S(){if(!LimitReached){if(CapsLock == false){Text.text += "s";}else{Text.text += "S";} IncreaseLimit();}}
    public void T(){if(!LimitReached){if(CapsLock == false){Text.text += "t";}else{Text.text += "T";} IncreaseLimit();}}
    public void U(){if(!LimitReached){if(CapsLock == false){Text.text += "u";}else{Text.text += "U";} IncreaseLimit();}}
    public void V(){if(!LimitReached){if(CapsLock == false){Text.text += "v";}else{Text.text += "V";} IncreaseLimit();}}
    public void W(){if(!LimitReached){if(CapsLock == false){Text.text += "w";}else{Text.text += "W";} IncreaseLimit();}}
    public void X(){if(!LimitReached){if(CapsLock == false){Text.text += "x";}else{Text.text += "X";} IncreaseLimit();}}
    public void Y(){if(!LimitReached){if(CapsLock == false){Text.text += "y";}else{Text.text += "Y";} IncreaseLimit();}}
    public void Z(){if(!LimitReached){if(CapsLock == false){Text.text += "z";}else{Text.text += "Z";} IncreaseLimit();}}

    //Numbers
    public void One(){if(!LimitReached){Text.text += "1"; IncreaseLimit();}}
    public void Two(){if(!LimitReached){Text.text += "2"; IncreaseLimit();}}
    public void Three(){if(!LimitReached){Text.text += "3"; IncreaseLimit();}}
    public void Four(){if(!LimitReached){Text.text += "4"; IncreaseLimit();}}
    public void Five(){if(!LimitReached){Text.text += "5"; IncreaseLimit();}}
    public void Six(){if(!LimitReached){Text.text += "6"; IncreaseLimit();}}
    public void Seven(){if(!LimitReached){Text.text += "7"; IncreaseLimit();}}
    public void Eight(){if(!LimitReached){Text.text += "8"; IncreaseLimit();}}
    public void Nine(){if(!LimitReached){Text.text += "9"; IncreaseLimit();}}
    public void Zero(){if(!LimitReached){Text.text += "0"; IncreaseLimit();}}

    //Symbols
    public void Dot(){if(!LimitReached){Text.text += "."; IncreaseLimit();}}
    public void Comma(){if(!LimitReached){Text.text += ","; IncreaseLimit();}}
    public void ExclamationMark(){if(!LimitReached){Text.text += "!"; IncreaseLimit();}}
    public void QuestionMark(){if(!LimitReached){Text.text += "?"; IncreaseLimit();}}
    public void Colon(){if(!LimitReached){Text.text += ":"; IncreaseLimit();}}
    public void Semicolon(){if(!LimitReached){Text.text += ";"; IncreaseLimit();}}
    public void Asterisk(){if(!LimitReached){Text.text += "*"; IncreaseLimit();}}
    public void Equals(){if(!LimitReached){Text.text += "="; IncreaseLimit();}}
    public void Plus(){if(!LimitReached){Text.text += "+"; IncreaseLimit();}}
    public void Ampersand(){if(!LimitReached){Text.text += "&"; IncreaseLimit();}}
    public void Minus(){if(!LimitReached){Text.text += "-"; IncreaseLimit();}}
    public void Slash(){if(!LimitReached){Text.text += "/"; IncreaseLimit();}}
    public void Pipe(){if(!LimitReached){Text.text += "|"; IncreaseLimit();}}
    public void Quote(){if(!LimitReached){Text.text += "\""; IncreaseLimit();}}
    public void Backslash(){if(!LimitReached){Text.text += "\\"; IncreaseLimit();}}
    public void LeftBracket(){if(!LimitReached){Text.text += "["; IncreaseLimit();}}
    public void RightBracket(){if(!LimitReached){Text.text += "]"; IncreaseLimit();}}
    public void LeftParenthesis(){if(!LimitReached){Text.text += "("; IncreaseLimit();}}
    public void RightParenthesis(){if(!LimitReached){Text.text += ")"; IncreaseLimit();}}
    public void LeftCurlyBracket(){if(!LimitReached){Text.text += "{"; IncreaseLimit();}}
    public void RightCurlyBracket(){if(!LimitReached){Text.text += "}"; IncreaseLimit();}}
    public void LeftAngleBracket(){if(!LimitReached){Text.text += "<"; IncreaseLimit();}}
    public void RightAngleBracket(){if(!LimitReached){Text.text += ">"; IncreaseLimit();}}
    public void Caret(){if(!LimitReached){Text.text += "^"; IncreaseLimit();}}
    public void DollarSign(){if(!LimitReached){Text.text += "$"; IncreaseLimit();}}
    public void AtSign(){if(!LimitReached){Text.text += "@"; IncreaseLimit();}}
    public void PoundSign(){if(!LimitReached){Text.text += "#"; IncreaseLimit();}}
    public void PercentSign(){if(!LimitReached){Text.text += "%"; IncreaseLimit();}}
    public void Underscore(){if(!LimitReached){Text.text += "_"; IncreaseLimit();}}
    public void Apostrophe(){if(!LimitReached){Text.text += "'"; IncreaseLimit();}}

    //Other
    public void Space(){if(!LimitReached){Text.text += " "; IncreaseLimit();}}
    public void Backspace(){if(Text.text.Length > 0){Text.text = Text.text.Substring(0, Text.text.Length - 1); DecreaseLimit();}}
    public void Clear(){Text.text = ""; ClearLimit();}
    public void Enter(){if(!LimitReached){Text.text += "\n"; IncreaseLimit();}}
    public void CapsLockToggle(){
        LetterText[0].text = CapsLock ? "a" : "A";
        LetterText[1].text = CapsLock ? "b" : "B";
        LetterText[2].text = CapsLock ? "c" : "C";
        LetterText[3].text = CapsLock ? "d" : "D";
        LetterText[4].text = CapsLock ? "e" : "E";
        LetterText[5].text = CapsLock ? "f" : "F";
        LetterText[6].text = CapsLock ? "g" : "G";
        LetterText[7].text = CapsLock ? "h" : "H";
        LetterText[8].text = CapsLock ? "i" : "I";
        LetterText[9].text = CapsLock ? "j" : "J";
        LetterText[10].text = CapsLock ? "k" : "K";
        LetterText[11].text = CapsLock ? "l" : "L";
        LetterText[12].text = CapsLock ? "m" : "M";
        LetterText[13].text = CapsLock ? "n" : "N";
        LetterText[14].text = CapsLock ? "o" : "O";
        LetterText[15].text = CapsLock ? "p" : "P";
        LetterText[16].text = CapsLock ? "q" : "Q";
        LetterText[17].text = CapsLock ? "r" : "R";
        LetterText[18].text = CapsLock ? "s" : "S";
        LetterText[19].text = CapsLock ? "t" : "T";
        LetterText[20].text = CapsLock ? "u" : "U";
        LetterText[21].text = CapsLock ? "v" : "V";
        LetterText[22].text = CapsLock ? "w" : "W";
        LetterText[23].text = CapsLock ? "x" : "X";
        LetterText[24].text = CapsLock ? "y" : "Y";
        LetterText[25].text = CapsLock ? "z" : "Z";
        CapsLock = !CapsLock;
    }
    public void Confirm(){
        text = Text.text; 
        if(IsSyncable){
            if(!Networking.IsOwner(gameObject)){Networking.SetOwner(Networking.LocalPlayer, gameObject);}
            RequestSerialization();
        }
    }

    public void IncreaseLimit(){
        LimitValue++;
        if(LimitValue >= LimitMax){LimitReached = true;}
        if(LimitValue < LimitMax){LimitReached = false;}
    }
    public void DecreaseLimit(){
        LimitValue--;
        if(LimitValue < 0){LimitValue = 0;}
        if(LimitValue < LimitMax){LimitReached = false;}
    }
    public void ClearLimit(){LimitValue = 0;}

    public override void OnDeserialization(){Text.text = text;}
}
