; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 7
		Day: 23
		Hour: 2
		Minute: 42
		Second: 6
		Millisecond: 468
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\6oxsvqt4_Distorter Tower Model.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\6oxsvqt4_Distorter Tower Model.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2304452967696, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2297306773488, "Geometry::Scene", "Mesh" {
		Vertices: *30 {
			a: -14.4818961620331,-29.9784541130066,5.96046447753906e-06,-39.0623658895493,-29.9784481525421,-38.2206380367279,-0,29.9784541130066,8.94069671630859e-06,-0,-29.9784541130066,8.94069671630859e-06,-0,-29.9784541130066,-14.1698330640793,39.0623658895493,-29.9784481525421,-38.2206380367279,14.4818961620331,-29.9784541130066,5.96046447753906e-06,39.0623718500137,-29.9784481525421,38.2206380367279,-0,-29.9784541130066,14.169842004776,-39.0623658895493,-29.9784481525421,38.2206499576569
		} 
		PolygonVertexIndex: *48 {
			a: 0,2,-2,3,0,-2,1,2,-5,3,1,-5,4,2,-6,3,4,-6,5,2,-7,3,5,-7,6,2,-8,3,6,-8,7,2,-9,3,7,-9,8,2,-10,3,8,-10,9,2,-1,3,9,-1
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *144 {
				a: -0.972046971321106,0.234786659479141,0,3.75933737473133e-08,1,-1.1278012124194e-07,-0.527275502681732,0.673645496368408,-0.517863273620605,3.20927434475406e-14,-1,-1.55948839619668e-07,3.20927434475406e-14,-1,-1.55948839619668e-07,3.20927434475406e-14,-1,-1.55948839619668e-07,-0.527275502681732,0.673645496368408,-0.517863273620605,3.75933737473133e-08,1,-1.1278012124194e-07,-0,0.229997918009758,-0.973191142082214,-1.52588413016019e-07,-1,0,-1.52588413016019e-07,-1,0,-1.52588413016019e-07,-1,0,-0,0.229997918009758,-0.973191142082214,3.75933737473133e-08,1,-1.1278012124194e-07,0.527275502681732,0.673645496368408,-0.517863273620605,1.52588413016019e-07,-1,0,1.52588413016019e-07,-1,0,1.52588413016019e-07,-1,0,0.527275502681732,0.673645496368408,-0.517863273620605,3.75933737473133e-08,1,-1.1278012124194e-07,0.972046911716461,0.234786659479141,-1.05439745823332e-07,-3.20927434475406e-14,-1,-1.55948839619668e-07,-3.20927434475406e-14,-1,-1.55948839619668e-07,-3.20927434475406e-14,-1,-1.55948839619668e-07,0.972046911716461,0.234786659479141,-1.05439745823332e-07,3.75933737473133e-08,1,-1.1278012124194e-07,0.527275562286377,0.673645436763763,0.517863214015961,3.20927468356724e-14,-1,1.55948853830523e-07,3.20927468356724e-14,-1,1.55948853830523e-07,3.20927468356724e-14,-1,1.55948853830523e-07,0.527275562286377,0.673645436763763,0.517863214015961,3.75933737473133e-08,1,-1.1278012124194e-07,1.38971458341075e-07,0.229997783899307,0.973191142082214,1.52588398805165e-07,-1,0,1.52588398805165e-07,-1,0,1.52588398805165e-07,-1,0,1.38971458341075e-07,0.229997783899307,0.973191142082214,3.75933737473133e-08,1,-1.1278012124194e-07,-0.527275443077087,0.673645496368408,0.51786333322525,-1.52588413016019e-07,-1,0,-1.52588413016019e-07,-1,0,-1.52588413016019e-07,-1,0,-0.527275443077087,0.673645496368408,0.51786333322525,3.75933737473133e-08,1,-1.1278012124194e-07,-0.972046971321106,0.234786659479141,0,-3.20927332831452e-14,-1,1.55948796987104e-07,-3.20927332831452e-14,-1,1.55948796987104e-07,-3.20927332831452e-14,-1,1.55948796987104e-07
			} 
			NormalsW: *48 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *144 {
				a: 0.232155025005341,0.961151719093323,-0.149303451180458,0.84107780456543,-9.2623317016205e-08,-0.540914177894592,0.817387104034424,0.235671117901802,-0.525678157806396,5.00482600399333e-21,-1.55948839619668e-07,1,5.00482600399333e-21,-1.55948839619668e-07,1,5.00482600399333e-21,-1.55948839619668e-07,1,-0.509322345256805,0.237267628312111,0.827221155166626,-0.524293959140778,1.15746473738909e-07,0.85153740644455,-0.140211328864098,0.963577568531036,0.22772590816021,0,0,1,0,0,1,0,0,1,0.140211328864098,0.963577568531036,0.22772590816021,0.524293959140778,7.63265148862047e-08,0.85153740644455,0.509322345256805,0.237267628312111,0.827221155166626,-0,-0,1,-0,-0,1,-0,-0,1,-0.817387104034424,0.235671117901802,-0.525678157806396,-0.84107780456543,-2.93854149902018e-08,-0.540914177894592,-0.232155039906502,0.961151599884033,-0.149303466081619,-5.00482600399333e-21,-1.55948839619668e-07,1,-5.00482600399333e-21,-1.55948839619668e-07,1,-5.00482600399333e-21,-1.55948839619668e-07,1,-0.232155010104179,0.961151659488678,0.149303510785103,-0.84107768535614,9.26233312270597e-08,0.540914356708527,-0.81738692522049,0.235670983791351,0.525678336620331,-5.00482721568368e-21,1.55948853830523e-07,1,-5.00482721568368e-21,1.55948853830523e-07,1,-5.00482721568368e-21,1.55948853830523e-07,1,0.509322106838226,0.237267777323723,-0.827221274375916,0.524293720722198,-1.15746473738909e-07,-0.851537525653839,0.140211179852486,0.963577568531036,-0.22772578895092,-0,-0,1,-0,-0,1,-0,-0,1,-0.140211254358292,0.963577568531036,-0.227725759148598,-0.524293959140778,-7.63265077807773e-08,-0.851537346839905,-0.50932240486145,0.237267702817917,-0.827221155166626,0,0,1,0,0,1,0,0,1,0.817387163639069,0.235670983791351,0.525678217411041,0.84107780456543,2.93854149902018e-08,0.540914177894592,0.232155025005341,0.961151719093323,0.149303451180458,5.00482358061263e-21,1.55948796987104e-07,1,5.00482358061263e-21,1.55948796987104e-07,1,5.00482358061263e-21,1.55948796987104e-07,1
			} 
			BinormalsW: *48 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *144 {
				a: 0.0350544080138206,0.145129963755608,0.988791406154633,0.540914177894592,7.45220773978872e-08,0.84107780456543,0.232075244188309,0.700471997261047,0.674892663955688,1,3.20927434475406e-14,0,1,3.20927434475406e-14,0,1,3.20927434475406e-14,0,-0.680125892162323,-0.699932813644409,-0.217997223138809,-0.85153740644455,-2.71177800215128e-08,-0.524293959140778,-0.990121603012085,-0.136452421545982,-0.0322482697665691,1,-1.52588413016019e-07,0,1,-1.52588413016019e-07,0,1,-1.52588413016019e-07,0,-0.990121603012085,0.136452421545982,0.0322482697665691,-0.85153740644455,9.11421054183847e-08,0.524293959140778,-0.680125892162323,0.699932813644409,0.217997223138809,1,1.52588413016019e-07,0,1,1.52588413016019e-07,0,1,1.52588413016019e-07,0,0.232075244188309,-0.700471997261047,-0.674892663955688,0.540914177894592,-1.15191639338263e-07,-0.84107780456543,0.0350543409585953,-0.145130008459091,-0.988791406154633,1,-3.20927434475406e-14,0,1,-3.20927434475406e-14,0,1,-3.20927434475406e-14,0,-0.0350544825196266,0.145130023360252,-0.988791406154633,-0.540914356708527,-7.45220489761778e-08,-0.84107768535614,-0.232075482606888,0.700471937656403,-0.674892544746399,1,3.20927468356724e-14,0,1,3.20927468356724e-14,0,1,3.20927468356724e-14,0,0.680126070976257,-0.699932754039764,0.217996999621391,0.851537525653839,2.71177427180191e-08,0.524293720722198,0.990121603012085,-0.136452302336693,0.0322481952607632,1,1.52588398805165e-07,0,1,1.52588398805165e-07,0,1,1.52588398805165e-07,0,0.990121603012085,0.136452317237854,-0.0322484038770199,0.851537346839905,-9.11420983129574e-08,-0.524293959140778,0.680125951766968,0.699932813644409,-0.217997223138809,1,-1.52588413016019e-07,0,1,-1.52588413016019e-07,0,1,-1.52588413016019e-07,0,-0.232075348496437,-0.700472056865692,0.674892604351044,-0.540914177894592,1.15191646443691e-07,0.84107780456543,-0.0350544080138206,-0.145129963755608,0.988791406154633,1,-3.20927332831452e-14,-5.28375076530998e-28,1,-3.20927332831452e-14,-5.28375076530998e-28,1,-3.20927332831452e-14,-5.28375076530998e-28
			} 
			TangentsW: *48 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *96 {
				a: 0.555988669395447,0,0,0,0.277994334697723,0.899949431419373,-0.144818961620331,1.06355699358573e-07,-0.390623658895493,-0.382206320762634,-9.62090853490714e-15,1.36158021746269e-07,0.555988669395447,0,0,0,0.277994334697723,0.899949431419373,-0.390623599290848,-0.382206380367279,4.57436470924222e-08,-0.141698330640793,4.57436470924222e-08,8.94069671630859e-08,0.555988669395447,0,0,0,0.277994334697723,0.899949431419373,-4.57436470924222e-08,-0.141698330640793,0.390623599290848,-0.382206380367279,-4.57436470924222e-08,8.94069671630859e-08,0.555988669395447,0,0,0,0.277994334697723,0.899949431419373,0.390623658895493,-0.382206320762634,0.144818961620331,1.06355699358573e-07,9.62090853490714e-15,1.36158021746269e-07,0.555988669395447,0,0,0,0.277994334697723,0.899949431419373,0.144818961620331,1.28535893040294e-08,0.390623718500137,0.382206320762634,-9.62090938194009e-15,4.26559125799031e-08,0.555988669395447,0,0,0,0.277994334697723,0.899949431419373,0.390623658895493,0.382206380367279,-4.57436435397085e-08,0.14169842004776,-4.57436435397085e-08,8.94069671630859e-08,0.555988669395447,0,0,0,0.277994334697723,0.899949431419373,4.57436470924222e-08,0.14169842004776,-0.390623599290848,0.382206499576569,4.57436470924222e-08,8.94069671630859e-08,0.555988669395447,0,0,0,0.277994334697723,0.899949431419373,-0.390623658895493,0.382206439971924,-0.144818961620331,1.28536061794193e-08,9.62090514677535e-15,4.26559303434715e-08
			} 
			UVIndex: *48 {
				a: 0,2,1,5,3,4,6,8,7,11,9,10,12,14,13,17,15,16,18,20,19,23,21,22,24,26,25,29,27,28,30,32,31,35,33,34,36,38,37,41,39,40,42,44,43,47,45,46
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2297212520256, "Model::Distorter_Tower_Model", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",6,6,6
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2297073983168, "Material::Yellow_Metal", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,0.91372549533844,0
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,0.91372549533844,0
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Distorter_Tower_Model, Model::RootNode
	C: "OO",2297212520256,0
	
	;Material::Yellow_Metal, Model::Distorter_Tower_Model
	C: "OO",2297073983168,2297212520256
	
	;Geometry::Scene, Model::Distorter_Tower_Model
	C: "OO",2297306773488,2297212520256
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
