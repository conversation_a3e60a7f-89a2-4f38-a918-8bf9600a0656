
using System;
using UnityEngine.UI;
using UdonSharp;
using UnityEngine;
using VRC.SDK3.Data;
using VRC.SDK3.StringLoading;
using VRC.SDKBase;
using VRC.Udon.Common.Interfaces;
using Random = UnityEngine.Random;


namespace Oikki.JoinStats
{
    public enum TimePeriod
    {
        今日,
        一週間,
        一月,
        一年,
        五年,
    }
    public class DataDownloader : UdonSharpBehaviour
    {
        public string id;
        public TimePeriod currentTimePeriod = TimePeriod.今日;
        public DataVisualizer visualizer;
        string baseJoinURL = "https://join-stats.oikki-api.win";
        [HideInInspector] public VRCUrl joinURL;
        [HideInInspector] public VRCUrl reloadURL;
        string jsonData = "";
        public Text errorText;
        public Animator reloadAnimatior;

        public Button[] buttons;
        string[] enumNames = new string[] { "last1d", "last7d", "last1m", "last1y", "last5y" };

        void Start()
        {
            SendJoin();
        }

        private void OnValidate()
        {
#if !COMPILER_UDONSHARP && UNITY_EDITOR
            if (id == "")
            {
                if (!ThisIsInstatiated()) return;
                string newId = GenerateRandomString(16);
                id = newId;
            }
            reloadURL = new VRCUrl($"{baseJoinURL}/stats?id={id}");
            joinURL = new VRCUrl($"{baseJoinURL}/join?id={id}");

#endif
        }

#if !COMPILER_UDONSHARP && UNITY_EDITOR
        private bool ThisIsInstatiated()
        {
            var pinboardGeneratorScripts = FindObjectsOfType<DataDownloader>();
            bool instatiated = false;
            foreach (var script in pinboardGeneratorScripts)
            {
                if (script == this)
                {
                    instatiated = true;
                    break;
                }
            }
            return instatiated;
        }
#endif
        private string pinboardIdCharacters = "abcdefghijklmnopqrstuvwxyz0123456789";
        public string GenerateRandomString(int length)
        {
            string randomString = "";

            for (int i = 0; i < length; i++)
            {
                int index = Random.Range(0, pinboardIdCharacters.Length);
                randomString += pinboardIdCharacters[index];
            }

            return randomString;
        }


        public void SendJoin()
        {
            reloadAnimatior.SetBool("loading", true);
            VRCStringDownloader.LoadUrl(joinURL, (IUdonEventReceiver)this);
        }

        public void ReloadData()
        {
            reloadAnimatior.SetBool("loading", true);
            VRCStringDownloader.LoadUrl(reloadURL, (IUdonEventReceiver)this);
        }

        public override void OnStringLoadSuccess(IVRCStringDownload result)
        {
            errorText.text = "";
            reloadAnimatior.SetBool("loading", false);
            jsonData = result.Result;
            ProcessJsonData();
        }

        public void ProcessJsonData()
        {
            if (jsonData.Length == 0) return;
            DisableButton((int)currentTimePeriod);
            if (VRCJson.TryDeserializeFromJson(jsonData, out DataToken resultToken))
            {
                DataDictionary dataDic = resultToken.DataDictionary;

                DataList valuesList = GetDataList(dataDic, currentTimePeriod);
                VisualizeDataList(valuesList);

            }
        }

        public void VisualizeDataList(DataList dataList)
        {
            visualizer.Visualize(dataList, currentTimePeriod);
        }

        public DataList GetDataList(DataDictionary data, TimePeriod timePeriod)
        {
            string valueName = enumNames[(int)timePeriod];
            if (!data.TryGetValue(valueName, TokenType.DataList, out DataToken dataListToken))
            {
                Debug.LogWarning($"could not parse dataList with {valueName}");
                return new DataList();
            }
            return dataListToken.DataList;
        }

        public override void OnStringLoadError(IVRCStringDownload result)
        {
            reloadAnimatior.SetBool("loading", false);
            Debug.LogError($"Error loading string: {result.ErrorCode} - {result.Error}");
            if (result.ErrorCode == 401)
            {
                errorText.text = "セーフティ 設定で 「信頼されていない URLを許可」  を有効にしてください";
            }
            else
            {
                errorText.text = $"{result.ErrorCode} - {result.Error}";
            }
        }

        public void DisableButton(int index)
        {
            for (int i = 0; i < buttons.Length; i++)
            {
                if (i >= buttons.Length) return;
                buttons[i].interactable = (i != index);
            }
        }


        public void PastDayPressed()
        {
            currentTimePeriod = TimePeriod.今日;
            ProcessJsonData();
        }
        public void PastWeekPressed()
        {
            currentTimePeriod = TimePeriod.一週間;
            ProcessJsonData();
        }
        public void PastMonthPressed()
        {
            currentTimePeriod = TimePeriod.一月;
            ProcessJsonData();
        }
        public void PastYearPressed()
        {
            currentTimePeriod = TimePeriod.一年;
            ProcessJsonData();
        }
        public void Past5YearsPressed()
        {
            currentTimePeriod = TimePeriod.五年;
            ProcessJsonData();
        }
    }
}