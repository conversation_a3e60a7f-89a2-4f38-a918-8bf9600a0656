﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using UnityEngine.UI;

[UdonBehaviourSyncMode(BehaviourSyncMode.NoVariableSync)]
public class TogglePlayerSystem : UdonSharpBehaviour
{
    public SecurityCheckSystem SecurityCheckSystem;
    public PlayerWorldStartSystem playerWorldStartSystem;
    public MainPlayerCurrencySystem MainPlayerCurrencySystem;

    //Toggles
    public Toggle FriendlyFireToggle;
    public Toggle FriendlyFireToggleLocal, DeathPrisonToggleLocal, RandomizeSkyToggleLocal, HealingItemsToggleLocal;

    //GameObjects
    public GameObject DeathPrison, RandomizeSky, CanChangePvPObject, CanChangePvPObjectLock;

    //Healing GameObjects
    public GameObject[] Items,locks, GlobalLocks, PvPArenaOpen, PvPArenaClose;

    private VRCPlayerApi localPlayer;

    void Start(){localPlayer = Networking.LocalPlayer;}

    //Toggles
    public void FriendlyFireEveryoneToggle()
    {
        if(FriendlyFireToggleLocal.isOn){
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(EnableFriendlyFire));
        }
        else{
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(DisableFriendlyFire));
        }
    }
    public void DeathPrisonEveryoneToggle(){
        if(DeathPrisonToggleLocal.isOn){
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(EnableDeathPrison));
        }
        else{
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(DisableDeathPrison));
        }
    }
    public void RandomizeSkyEveryoneToggle(){
        if(RandomizeSkyToggleLocal.isOn){
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(EnableRandomizeSky));
        }
        else{
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(DisableRandomizeSky));
        }
    }
    public void HealingItemsEveryoneToggle(){
        if(HealingItemsToggleLocal.isOn){
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(EnableHealingItems));
        }
        else{
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(DisableHealingItems));
        }
    }

    public void HealedPlayerEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(HealedPlayer));}

    public void SetPlayerto1KOEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(SetPlayerto1KO));}

    public void ForceFlingEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(ForceFling));}

    public void ClearPrisonEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(ClearPrison));}

    public void Give100CoinsEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(Reward100));}

    public void Take100CoinsEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(Take100));}

    public void OpenPvPArenaEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(OpenPvPArena));}

    public void ClosePvPArenaEveryone(){SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, nameof(ClosePvPArena));}




    public void EnableFriendlyFire(){
        FriendlyFireToggle.isOn = false;
        playerWorldStartSystem.FriendlyFire = true;
        playerWorldStartSystem.FriendlyFireKnockbackToggle();
        CanChangePvPObject.SetActive(false);
        CanChangePvPObjectLock.SetActive(true);
        foreach (string targetName in SecurityCheckSystem.targetPlayerNames)
        {
            if (localPlayer.displayName == targetName)
            {
                CanChangePvPObject.SetActive(true);
                CanChangePvPObjectLock.SetActive(false);
                break;
            }
        }
    }

    public void DisableFriendlyFire(){
        FriendlyFireToggle.isOn = true;
        playerWorldStartSystem.FriendlyFire = false;
        playerWorldStartSystem.FriendlyFireKnockbackToggle();
        CanChangePvPObject.SetActive(true);
        CanChangePvPObjectLock.SetActive(false);
    }

    public void EnableDeathPrison(){DeathPrison.SetActive(true);}
    public void DisableDeathPrison(){DeathPrison.SetActive(false);}

    public void EnableRandomizeSky(){RandomizeSky.SetActive(true);}
    public void DisableRandomizeSky(){RandomizeSky.SetActive(false);}

    public void EnableHealingItems(){
        for(int i = 0; i < Items.Length; i++){
            Items[i].SetActive(true);
            if(locks[i] != null){
                locks[i].SetActive(false);
            }
            GlobalLocks[i].SetActive(false);
        }
    }
    public void DisableHealingItems(){
        for(int i = 0; i < Items.Length; i++){
            Items[i].SetActive(false);
            if(locks[i] != null){
                locks[i].SetActive(false);
            }
            GlobalLocks[i].SetActive(true);
        }
    }


    public void HealedPlayer(){
        playerWorldStartSystem.MAXHEALTH = 100;
        playerWorldStartSystem.CanChangeHealth = true;
        playerWorldStartSystem.HEALTH = playerWorldStartSystem.MAXHEALTH;
    }
    public void SetPlayerto1KO(){
        playerWorldStartSystem.MAXHEALTH = 1;
        playerWorldStartSystem.CanChangeHealth = false;
        playerWorldStartSystem.HEALTH = playerWorldStartSystem.MAXHEALTH;
    }

    public void ForceFling(){localPlayer.SetVelocity(Vector3.up * 15);}

    public void ClearPrison(){
        playerWorldStartSystem.PlayerInPrison = false;
        playerWorldStartSystem.PrisonCooldown = 0;
    }

    public void Reward100(){
        int RandomCoins = Random.Range(100, 200);
        MainPlayerCurrencySystem.AddPoints(RandomCoins);
    }

    public void Take100(){
        MainPlayerCurrencySystem.RemovePoints(100);
    }

    public void OpenPvPArena(){
        for(int i = 0; i < PvPArenaOpen.Length; i++){
            if(PvPArenaOpen[i] != null){PvPArenaOpen[i].SetActive(true);}
            if(PvPArenaClose[i] != null){PvPArenaClose[i].SetActive(false);}
        }
    }
    public void ClosePvPArena(){
        for(int i = 0; i < PvPArenaOpen.Length; i++){
            if(PvPArenaOpen[i] != null){PvPArenaOpen[i].SetActive(false);}
            if(PvPArenaClose[i] != null){PvPArenaClose[i].SetActive(true);}
        }
    }
}
