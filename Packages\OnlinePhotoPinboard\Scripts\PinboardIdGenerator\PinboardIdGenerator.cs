﻿
using UdonSharp;
using UnityEditor;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

namespace OnlinePhotoPinboard
{

    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class PinboardIdGenerator : UdonSharpBehaviour
    {
        public string pinboardId;
        public string hashKey;

        [Header("References")]
        public PhotoDownloader photoDownloader;
        public NoteDownloader noteDownloader;
        public ImageDeleter imageDeleter;
        public ImageApprover imageApprover;

        string noteDownloadBaseURL = "https://photo-bulletinboard.oikki-api.win/getNotesData";
        string noteUploadBaseURL = "https://photo-bulletinboard.oikki-api.win/addImage";
        string imageDownloadBaseURL = "https://photo-bulletinboard.oikki-api.win/getImage";
        string pinboardCreateBaseURL = "https://photo-bulletinboard.oikki-api.win/createPinboard";
        string imageDeleteBaseURL = "https://photo-bulletinboard.oikki-api.win/deleteImage";
        string imageApproveBaseURL = "https://photo-bulletinboard.oikki-api.win/approveImages";

        public string GetUserHash()
        {
            return UdonHashLib.MD5_UTF8(Networking.LocalPlayer.displayName + hashKey);
        }
        public string GetUploadBaseUrl()
        {
            return noteUploadBaseURL;
        }
        private void OnValidate()
        {
#if !COMPILER_UDONSHARP && UNITY_EDITOR
            //if (PrefabStageUtility.GetCurrentPrefabStage() != null) return;
            if (pinboardId == "")
            {
                if (!ThisIsInstatiated()) return;
                string newPinboardId = GenerateRandomString(14);
                string newHashKey = GenerateRandomString(32);
                pinboardId = newPinboardId;
                hashKey = newHashKey;
            }
            noteDownloader.noteDownloadURL = new VRCUrl($"{noteDownloadBaseURL}?pinboardId={pinboardId}");
            noteDownloader.pinboardCreateURL = new VRCUrl($"{pinboardCreateBaseURL}?pinboardId={pinboardId}&hashKey={hashKey}");
            imageApprover.imageApproveUrl = new VRCUrl($"{imageApproveBaseURL}?pinboardId={pinboardId}&hashKey={hashKey}");
            photoDownloader.getPhotoURL = new VRCUrl($"{imageDownloadBaseURL}?pinboardId={pinboardId}");

            VRCUrl[] imageDeleteUrls = new VRCUrl[64];
            for(int i=0; i<imageDeleteUrls.Length; i++)
            {
                imageDeleteUrls[i] = new VRCUrl($"{imageDeleteBaseURL}?pinboardId={pinboardId}&hashKey={hashKey}&index={i}");
            }

            imageDeleter.imageDeleteUrls = imageDeleteUrls;
#endif
        }

#if !COMPILER_UDONSHARP && UNITY_EDITOR
        private bool ThisIsInstatiated()
        {
            var pinboardGeneratorScripts = FindObjectsOfType<PinboardIdGenerator>();
            bool instatiated = false;
            foreach (var script in pinboardGeneratorScripts)
            {
                if (script == this)
                {
                    instatiated = true;
                    break;
                }
            }
            return instatiated;
        }
#endif

        private string pinboardIdCharacters = "abcdefghijklmnopqrstuvwxyz0123456789";
        public string GenerateRandomString(int length)
        {
            string randomString = "";

            for (int i = 0; i < length; i++)
            {
                int index = Random.Range(0, pinboardIdCharacters.Length);
                randomString += pinboardIdCharacters[index];
            }

            return randomString;
        }
    }
}