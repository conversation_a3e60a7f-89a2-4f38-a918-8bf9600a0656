; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 4
		Day: 10
		Hour: 4
		Minute: 56
		Second: 51
		Millisecond: 219
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\a9uvh139_Hammer Inventory Local.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\a9uvh139_Hammer Inventory Local.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2331569048224, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2331606762064, "Geometry::Scene", "Mesh" {
		Vertices: *159 {
			a: 1.99999809265137,-53.7008106708527,1.99997425079346,-1.99999809265137,-53.7008106708527,1.99997425079346,-1.99999809265137,-15.8961236476898,1.99999809265137,1.99999809265137,-15.8961236476898,1.99999809265137,-1.99999809265137,-53.7008106708527,-2.00002193450928,-1.99999809265137,-15.8960521221161,-1.99999809265137,1.99999809265137,-53.7008106708527,-2.00002193450928,1.99999809265137,-15.8960521221161,-1.99999809265137,9.65094566345215,-2.30042934417725,-3.62073183059692,22.6386070251465,0.213128328323364,-0.118136405944824,9.65094566345215,2.72670388221741,-3.62069606781006,9.65094566345215,2.72670388221741,3.3845067024231,9.65094566345215,-2.30047106742859,3.3845067024231,-6.54251575469971,-5.94799518585205,-6.1719536781311,-29.4280767440796,0.077444314956665,-2.38418579101562e-05,-6.54251575469971,-5.94809055328369,6.17197751998901,-6.54251575469971,6.10293745994568,6.17197751998901,-6.54251575469971,6.10293745994568,-6.17187023162842,-6.32755756378174,4.79297041893005,4.90431785583496,6.61497116088867,4.79297041893005,4.90431785583496,6.61497116088867,4.79304194450378,-4.90431785583496,-6.32755756378174,4.79304194450378,-4.90431785583496,1.99999809265137,-56.1098694801331,-2.00002193450928,-1.99999809265137,-56.1098694801331,-2.00002193450928,-1.99999809265137,-56.1098635196686,1.99997425079346,1.99999809265137,-56.1098635196686,1.99997425079346,-7.76841640472412,-54.9053311347961,-2.38418579101562e-05,7.76841640472412,-54.9053311347961,-2.38418579101562e-05,-0,-60.5556666851044,0,-11.0933542251587,-6.77645206451416,-7.12112188339233,11.0933542251587,-6.77645206451416,-7.12112188339233,-11.0933542251587,6.26471638679504,-7.12112188339233,11.0933542251587,6.26471638679504,-7.12112188339233,11.0933542251587,-6.77652359008789,7.12112188339233,11.0933542251587,6.26464486122131,7.12112188339233,-11.0933542251587,-6.77652359008789,7.12112188339233,-11.0933542251587,6.26464486122131,7.12112188339233,11.0933542251587,-6.77652359008789,3.84198427200317,-11.0933542251587,-6.77652359008789,3.84198427200317,
11.0933542251587,4.79297041893005,1.99999809265137,11.0933542251587,4.79304194450378,-1.99999809265137,-11.0933542251587,-6.77645206451416,-3.84198427200317,-11.0933542251587,4.79304194450378,-1.99999809265137,-11.0933542251587,4.79297041893005,1.99999809265137,11.0933542251587,-6.77645206451416,-3.84198427200317,11.0933542251587,6.26464486122131,1.99999809265137,11.0933542251587,6.26471638679504,-1.99999809265137,-11.0933542251587,6.26471638679504,-1.99999809265137,-11.0933542251587,6.26464486122131,1.99999809265137,6.61497116088867,6.26464486122131,4.90431785583496,-6.32755756378174,6.26464486122131,4.90431785583496,-6.32755756378174,6.26471638679504,-4.90431785583496,6.61497116088867,6.26471638679504,-4.90431785583496
		} 
		PolygonVertexIndex: *282 {
			a: 10,9,-9,11,9,-11,9,12,-9,9,11,-13,15,14,-14,16,14,-16,14,17,-14,14,16,-18,4,26,-2,23,26,-5,1,26,-25,24,26,-24,0,27,-7,25,27,-1,6,27,-23,22,27,-26,22,28,-24,25,28,-23,23,28,-25,24,28,-26,47,51,-32,32,31,-52,32,51,-53,52,46,-33,36,50,-49,49,34,-46,50,34,-50,50,36,-35,0,2,-2,2,0,-4,1,5,-5,5,1,-3,6,5,-8,5,6,-5,0,7,-4,7,0,-7,17,15,-14,15,17,-17,11,8,-13,8,11,-11,21,19,-21,19,21,-19,6,23,-5,23,6,-23,1,25,-1,25,1,-25,29,32,-31,32,29,-32,30,34,-34,34,30,-33,35,31,-30,31,35,-37,33,36,-36,36,33,-35,3,38,-3,38,3,-38,19,40,-21,40,19,-40,2,41,-6,41,2,-39,21,43,-19,43,21,-43,5,44,-8,44,5,-42,7,37,-4,37,7,-45,37,35,-39,35,37,-34,45,32,-47,32,45,-35,38,29,-42,29,38,-36,47,36,-49,36,47,-32,41,30,-45,30,41,-30,44,33,-38,33,44,-31,49,18,-51,18,49,-20,47,21,-52,21,47,-43,50,43,-49,43,50,-19,48,42,-48,42,48,-44,51,20,-53,20,51,-22,45,19,-50,19,45,-40,52,40,-47,40,52,-21,46,39,-46,39,46,-41
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *846 {
				a: 0.260382354259491,6.86856083120801e-06,-0.965505599975586,0.260382354259491,6.86856083120801e-06,-0.965505599975586,0.260382354259491,6.86856083120801e-06,-0.965505599975586,0.190009862184525,0.981782197952271,0,0.190009862184525,0.981782197952271,0,0.190009862184525,0.981782197952271,0,0.190010070800781,-0.981782138347626,-5.84750159760006e-06,0.190010070800781,-0.981782138347626,-5.84750159760006e-06,0.190010070800781,-0.981782138347626,-5.84750159760006e-06,0.260386914014816,0,0.965504348278046,0.260386914014816,0,0.965504348278046,0.260386914014816,0,0.965504348278046,-0.2546107172966,-0.96704363822937,-7.4712397690746e-06,-0.2546107172966,-0.96704363822937,-7.4712397690746e-06,-0.2546107172966,-0.96704363822937,-7.4712397690746e-06,-0.260386645793915,0,0.96550440788269,-0.260386645793915,0,0.96550440788269,-0.260386645793915,0,0.96550440788269,-0.260382205247879,6.68562915961957e-06,-0.965505599975586,-0.260382205247879,6.68562915961957e-06,-0.965505599975586,-0.260382205247879,6.68562915961957e-06,-0.965505599975586,-0.254610925912857,0.967043578624725,0,-0.254610925912857,0.967043578624725,0,-0.254610925912857,0.967043578624725,0,-0.204404190182686,0.978886604309082,0,-0.204404190182686,0.978886604309082,0,-0.204404190182686,0.978886604309082,0,-0.327584117650986,0,-0.944822013378143,-0.327584117650986,0,-0.944822013378143,-0.327584117650986,0,-0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.20440661907196,-0.978886127471924,1.45865533340839e-06,-0.20440661907196,-0.978886127471924,1.45865533340839e-06,-0.20440661907196,-0.978886127471924,1.45865533340839e-06,0.204404190182686,0.978886604309082,0,0.204404190182686,0.978886604309082,0,0.204404190182686,0.978886604309082,0,0.327584117650986,0,0.944822013378143,0.327584117650986,0,0.944822013378143,0.327584117650986,0,0.944822013378143,0.327584117650986,0,-0.944822013378143,0.327584117650986,0,-0.944822013378143,0.327584117650986,0,-0.944822013378143,
0.20440661907196,-0.978886127471924,1.45865533340839e-06,0.20440661907196,-0.978886127471924,1.45865533340839e-06,0.20440661907196,-0.978886127471924,1.45865533340839e-06,-0,-0.410264611244202,-0.911966562271118,-0,-0.410264611244202,-0.911966562271118,-0,-0.410264611244202,-0.911966562271118,0.911968469619751,-0.410260260105133,6.1133602002883e-07,0.911968469619751,-0.410260260105133,6.1133602002883e-07,0.911968469619751,-0.410260260105133,6.1133602002883e-07,-0.911968469619751,-0.410260260105133,6.1133602002883e-07,-0.911968469619751,-0.410260260105133,6.1133602002883e-07,-0.911968469619751,-0.410260260105133,6.1133602002883e-07,-0,-0.410255968570709,0.911970436573029,-0,-0.410255968570709,0.911970436573029,-0,-0.410255968570709,0.911970436573029,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-6.30658803402184e-07,1,-0,-6.30658803402184e-07,1,-0,-6.30658803402184e-07,1,-0,-6.30658803402184e-07,1,-0,-6.30658803402184e-07,1,-0,-6.30658803402184e-07,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,6.30657552846969e-07,-1,-0,6.30657552846969e-07,-1,-0,6.30657552846969e-07,-1,-0,6.30657552846969e-07,-1,-0,6.30657552846969e-07,-1,-0,6.30657552846969e-07,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,1,7.2921020546346e-06,-0,1,7.2921020546346e-06,-0,1,7.2921020546346e-06,-0,1,7.2921020546346e-06,-0,1,7.2921020546346e-06,-0,1,7.2921020546346e-06,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0.197982907295227,0.980205476284027,-0,-0.197982907295227,0.980205476284027,-0,-0.197982907295227,0.980205476284027,-0,-0.197982907295227,0.980205476284027,-0,-0.197982907295227,0.980205476284027,-0,-0.197982907295227,0.980205476284027,
4.72907231596764e-06,1,7.29210250938195e-06,-1.15964476208319e-05,1,1.78814116225112e-05,-3.43368765243213e-06,1,1.25867572933203e-05,-1.15964476208319e-05,1,1.78814116225112e-05,4.72907231596764e-06,1,7.29210250938195e-06,-3.43368765243213e-06,1,1.25867572933203e-05,-0.708126246929169,-0.706085860729218,-1.26258119053091e-05,-0.708124279975891,-0.706087827682495,-6.57255895930575e-06,-0.708125233650208,-0.706086874008179,-9.59918543230742e-06,-0.708124279975891,-0.706087827682495,-6.57255895930575e-06,-0.708126246929169,-0.706085860729218,-1.26258119053091e-05,-0.708125233650208,-0.706086874008179,-9.59918543230742e-06,4.44387342213304e-06,1,7.29210250938195e-06,-1.08970943983877e-05,1,1.78814098035218e-05,-3.22661048812733e-06,1,1.25867563838256e-05,-1.08970943983877e-05,1,1.78814098035218e-05,4.44387342213304e-06,1,7.29210250938195e-06,-3.22661048812733e-06,1,1.25867563838256e-05,-0,-0.197982907295227,-0.980205476284027,-0,-0.197982907295227,-0.980205476284027,-0,-0.197982907295227,-0.980205476284027,-0,-0.197982907295227,-0.980205476284027,-0,-0.197982907295227,-0.980205476284027,-0,-0.197982907295227,-0.980205476284027,0.708123683929443,-0.706088423728943,-1.26258573800442e-05,0.708125650882721,-0.70608651638031,-6.57254668112728e-06,0.70812463760376,-0.706087470054626,-9.59920180321205e-06,0.708125650882721,-0.70608651638031,-6.57254668112728e-06,0.708123683929443,-0.706088423728943,-1.26258573800442e-05,0.70812463760376,-0.706087470054626,-9.59920180321205e-06,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,0.520391404628754,0,0.853927850723267,0.520391404628754,0,0.853927850723267,0.520391404628754,0,0.853927850723267,0.520391404628754,0,0.853927850723267,0.520391404628754,0,0.853927850723267,0.520391404628754,0,0.853927850723267,0.520391404628754,0,-0.853927850723267,
0.520391404628754,0,-0.853927850723267,0.520391404628754,0,-0.853927850723267,0.520391404628754,0,-0.853927850723267,0.520391404628754,0,-0.853927850723267,0.520391404628754,0,-0.853927850723267,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0.544114947319031,0,-0.839010655879974,-0.544114947319031,0,-0.839010655879974,-0.544114947319031,0,-0.839010655879974,-0.544114947319031,0,-0.839010655879974,-0.544114947319031,0,-0.839010655879974,-0.544114947319031,0,-0.839010655879974,-0.544114947319031,0,0.839010655879974,-0.544114947319031,0,0.839010655879974,-0.544114947319031,0,0.839010655879974,-0.544114947319031,0,0.839010655879974,-0.544114947319031,0,0.839010655879974,-0.544114947319031,0,0.839010655879974,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			NormalsW: *282 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *846 {
				a: -1.78845220943913e-06,1,6.63163382341736e-06,-1.78845220943913e-06,1,6.63163382341736e-06,-1.78845220943913e-06,1,6.63163382341736e-06,0,-0,1,0,-0,1,0,-0,1,1.1110842024209e-06,-5.74097248318139e-06,1,1.1110842024209e-06,-5.74097248318139e-06,1,1.1110842024209e-06,-5.74097248318139e-06,1,-0,1,-0,-0,1,-0,-0,1,-0,-1.90225762253249e-06,-7.22501499694772e-06,1,-1.90225762253249e-06,-7.22501499694772e-06,1,-1.90225762253249e-06,-7.22501499694772e-06,1,0,1,-0,0,1,-0,0,1,-0,1.74081890236266e-06,1,6.45501268081716e-06,1.74081890236266e-06,1,6.45501268081716e-06,1.74081890236266e-06,1,6.45501268081716e-06,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,2.98158795430936e-07,1.42785734169593e-06,1,2.98158795430936e-07,1.42785734169593e-06,1,2.98158795430936e-07,1.42785734169593e-06,1,0,-0,1,0,-0,1,0,-0,1,-0,1,-0,-0,1,-0,-0,1,-0,0,1,0,0,1,0,0,1,0,-2.98158795430936e-07,1.42785734169593e-06,1,-2.98158795430936e-07,1.42785734169593e-06,1,-2.98158795430936e-07,1.42785734169593e-06,1,0,0.911966562271118,-0.410264611244202,0,0.911966562271118,-0.410264611244202,0,0.911966562271118,-0.410264611244202,0.410260260105133,0.911968469619751,1.6212941318372e-06,0.410260260105133,0.911968469619751,1.6212941318372e-06,0.410260260105133,0.911968469619751,1.6212941318372e-06,-0.410260260105133,0.911968469619751,1.6212941318372e-06,-0.410260260105133,0.911968469619751,1.6212941318372e-06,-0.410260260105133,0.911968469619751,1.6212941318372e-06,-0,0.911970436573029,0.410255968570709,-0,0.911970436573029,0.410255968570709,-0,0.911970436573029,0.410255968570709,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,1,6.30658803402184e-07,-0,1,6.30658803402184e-07,-0,1,6.30658803402184e-07,-0,1,6.30658803402184e-07,-0,1,6.30658803402184e-07,-0,1,6.30658803402184e-07,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,6.30657552846969e-07,0,1,6.30657552846969e-07,0,1,6.30657552846969e-07,
0,1,6.30657552846969e-07,0,1,6.30657552846969e-07,0,1,6.30657552846969e-07,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-7.2921020546346e-06,1,0,-7.2921020546346e-06,1,0,-7.2921020546346e-06,1,0,-7.2921020546346e-06,1,0,-7.2921020546346e-06,1,0,-7.2921020546346e-06,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0.980205476284027,0.197982907295227,-0,0.980205476284027,0.197982907295227,-0,0.980205476284027,0.197982907295227,-0,0.980205476284027,0.197982907295227,-0,0.980205476284027,0.197982907295227,-0,0.980205476284027,0.197982907295227,-3.44848767597217e-11,-7.29210250938195e-06,1,2.0736085071249e-10,-1.78814116225112e-05,1,4.32189943555539e-11,-1.25867572933203e-05,1,2.0736085071249e-10,-1.78814116225112e-05,1,-3.44848767597217e-11,-7.29210250938195e-06,1,4.32189943555539e-11,-1.25867572933203e-05,1,-0.706085860729218,0.708126246929169,-1.26697013911325e-05,-0.706087827682495,0.708124279975891,-1.26070917758625e-05,-0.706086874008179,0.708125233650208,-1.2638401130971e-05,-0.706087827682495,0.708124279975891,-1.26070917758625e-05,-0.706085860729218,0.708126246929169,-1.26697013911325e-05,-0.706086874008179,0.708125233650208,-1.2638401130971e-05,-3.24051792344182e-11,-7.29210250938195e-06,1,1.94855409585415e-10,-1.78814098035218e-05,1,4.06125584551109e-11,-1.25867563838256e-05,1,1.94855409585415e-10,-1.78814098035218e-05,1,-3.24051792344182e-11,-7.29210250938195e-06,1,4.06125584551109e-11,-1.25867563838256e-05,1,0,0.980205476284027,-0.197982907295227,0,0.980205476284027,-0.197982907295227,0,0.980205476284027,-0.197982907295227,0,0.980205476284027,-0.197982907295227,0,0.980205476284027,-0.197982907295227,0,0.980205476284027,-0.197982907295227,0.706088423728943,0.708123683929443,-1.24834814414498e-05,0.70608651638031,0.708125650882721,-1.24131556731299e-05,
0.706087470054626,0.70812463760376,-1.24483140098164e-05,0.70608651638031,0.708125650882721,-1.24131556731299e-05,0.706088423728943,0.708123683929443,-1.24834814414498e-05,0.706087470054626,0.70812463760376,-1.24483140098164e-05,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,-0,-0,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,0.999999940395355,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0
			} 
			BinormalsW: *282 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *846 {
				a: -0.965505599975586,-2.04699064231158e-13,-0.260382354259491,-0.965505599975586,-2.04699064231158e-13,-0.260382354259491,-0.965505599975586,-2.04699064231158e-13,-0.260382354259491,-0.981782197952271,0.190009862184525,0,-0.981782197952271,0.190009862184525,0,-0.981782197952271,0.190009862184525,0,0.981782138347626,0.190010070800781,0,0.981782138347626,0.190010070800781,0,0.981782138347626,0.190010070800781,0,0.965504348278046,0,-0.260386914014816,0.965504348278046,0,-0.260386914014816,0.965504348278046,0,-0.260386914014816,0.96704363822937,-0.2546107172966,-1.11330154893129e-13,0.96704363822937,-0.2546107172966,-1.11330154893129e-13,0.96704363822937,-0.2546107172966,-1.11330154893129e-13,0.965504467487335,0,0.260386645793915,0.965504467487335,0,0.260386645793915,0.965504467487335,0,0.260386645793915,-0.965505659580231,-1.99247275579559e-13,0.260382235050201,-0.965505659580231,-1.99247275579559e-13,0.260382235050201,-0.965505659580231,-1.99247275579559e-13,0.260382235050201,-0.967043578624725,-0.254610955715179,0,-0.967043578624725,-0.254610955715179,0,-0.967043578624725,-0.254610955715179,0,-0.978886604309082,-0.204404190182686,0,-0.978886604309082,-0.204404190182686,0,-0.978886604309082,-0.204404190182686,0,-0.944822013378143,0,0.327584117650986,-0.944822013378143,0,0.327584117650986,-0.944822013378143,0,0.327584117650986,0.944822013378143,0,0.327584117650986,0.944822013378143,0,0.327584117650986,0.944822013378143,0,0.327584117650986,0.978886067867279,-0.204406604170799,-2.1735658249384e-14,0.978886067867279,-0.204406604170799,-2.1735658249384e-14,0.978886067867279,-0.204406604170799,-2.1735658249384e-14,-0.978886604309082,0.204404190182686,0,-0.978886604309082,0.204404190182686,0,-0.978886604309082,0.204404190182686,0,0.944822013378143,0,-0.327584117650986,0.944822013378143,0,-0.327584117650986,0.944822013378143,0,-0.327584117650986,-0.944822013378143,0,-0.327584117650986,-0.944822013378143,0,-0.327584117650986,-0.944822013378143,0,-0.327584117650986,0.978886067867279,0.204406604170799,2.1735658249384e-14,
0.978886067867279,0.204406604170799,2.1735658249384e-14,0.978886067867279,0.204406604170799,2.1735658249384e-14,-1,0,0,-1,0,0,-1,0,0,1.22267181268398e-06,1.22776225452981e-06,-1,1.22267181268398e-06,1.22776225452981e-06,-1,1.22267181268398e-06,1.22776225452981e-06,-1,1.22267181268398e-06,-1.22776225452981e-06,1,1.22267181268398e-06,-1.22776225452981e-06,1,1.22267181268398e-06,-1.22776225452981e-06,1,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,4.72907231596764e-06,3.31606429853398e-18,-1,-1.15964476208319e-05,0,-1,-3.43368765243213e-06,2.86189726769414e-18,-1,-1.15964476208319e-05,0,-1,4.72907231596764e-06,3.31606429853398e-18,-1,-3.43368765243213e-06,2.86189726769414e-18,-1.78865648194915e-05,5.68409426193739e-08,1,-1.35559021146037e-05,4.28658404416637e-06,1,-1.57212343765423e-05,2.17171191252419e-06,1,-1.35559021146037e-05,4.28658404416637e-06,1,-1.78865648194915e-05,5.68409426193739e-08,1,-1.57212343765423e-05,2.17171191252419e-06,1,-1,4.44387342213304e-06,0,-1,-1.08970943983877e-05,0,-1,-3.22661048812733e-06,0,-1,-1.08970943983877e-05,0,-1,4.44387342213304e-06,0,-1,-3.22661048812733e-06,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1.7755110093276e-05,7.5122535747596e-08,-1,-1.3418949492916e-05,-4.14928717873408e-06,-1,-1.55870293383487e-05,-2.0370816855575e-06,-1,-1.3418949492916e-05,-4.14928717873408e-06,-1,
-1.7755110093276e-05,7.5122535747596e-08,-1,-1.55870293383487e-05,-2.0370816855575e-06,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.853927850723267,0,-0.520391404628754,0.853927850723267,0,-0.520391404628754,0.853927850723267,0,-0.520391404628754,0.853927850723267,0,-0.520391404628754,0.853927850723267,0,-0.520391404628754,0.853927850723267,0,-0.520391404628754,-0.853927850723267,0,-0.520391404628754,-0.853927850723267,0,-0.520391404628754,-0.853927850723267,0,-0.520391404628754,-0.853927850723267,0,-0.520391404628754,-0.853927850723267,0,-0.520391404628754,-0.853927850723267,0,-0.520391404628754,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.839010655879974,0,0.544114947319031,-0.839010655879974,0,0.544114947319031,-0.839010655879974,0,0.544114947319031,-0.839010655879974,0,0.544114947319031,-0.839010655879974,0,0.544114947319031,-0.839010655879974,0,0.544114947319031,0.839010655879974,0,0.544114947319031,0.839010655879974,0,0.544114947319031,0.839010655879974,0,0.544114947319031,0.839010655879974,0,0.544114947319031,0.839010655879974,0,0.544114947319031,0.839010655879974,0,0.544114947319031,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1
			} 
			TangentsW: *282 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *408 {
				a: 0.0199999809265137,-0.537008106708527,-0.0199999809265137,-0.537008106708527,-0.0199999809265137,-0.158961221575737,0.0199999809265137,-0.158961221575737,0.0199997425079346,-0.537008106708527,-0.0200002193450928,-0.537008106708527,-0.0199999809265137,-0.158960521221161,0.0199999809265137,-0.158961236476898,0.0199999809265137,-0.537008106708527,-0.0199999809265137,-0.537008106708527,-0.0199999809265137,-0.158960536122322,0.0199999809265137,-0.158960536122322,0.0200002193450928,-0.537008106708527,-0.0199997425079346,-0.537008106708527,-0.0199999809265137,-0.158961236476898,0.0199999809265137,-0.158960521221161,-0.0837526768445969,-0.0230047069489956,-0.218269407749176,0.00213087047450244,-0.083752766251564,0.0272666253149509,-0.0895702615380287,-0.0362069606781006,-0.221856847405434,-0.00118136405944824,-0.0895702615380287,0.033845067024231,0.0903801321983337,0.0338453054428101,0.222666770219803,-0.00118112470954657,0.0903802141547203,-0.0362070798873901,0.0843674838542938,0.0272670388221741,0.218884348869324,0.00213128328323364,0.0843674838542938,-0.0230047106742859,-0.0481247492134571,-0.0617189817130566,-0.284779518842697,3.15783921678303e-07,-0.0481245070695877,0.0617203302681446,-0.047097273170948,-0.0594809055328369,-0.284129440784454,0.00077444314956665,-0.047097273170948,0.0610293745994568,0.0470979027450085,0.0610288605093956,0.284129679203033,0.000773930863942951,0.0470976866781712,-0.0594804659485817,0.0477302335202694,0.0617197751998901,0.284385144710541,-2.38418579101562e-07,0.0477302335202694,-0.0617187023162842,-0.0617197751998901,-0.0594809055328369,0.061719536781311,-0.0594799518585205,0.0617187023162842,0.0610293745994568,-0.0617197751998901,0.0610293745994568,-0.0362069606781006,0.0272670388221741,-0.0362073183059692,-0.0230042934417725,0.033845067024231,-0.0230047106742859,0.033845067024231,0.0272670388221741,0.0632755756378174,0.0490428283810616,-0.0661497116088867,0.0490428283810616,-0.0661497116088867,-0.0490435287356377,0.0632755756378174,-0.0490435287356377,-0.0199999809265137,
-0.537008106708527,0.0199999809265137,-0.537008106708527,-0.0199999809265137,-0.561098694801331,0.0199999809265137,-0.561098694801331,-0.0199999809265137,-0.537008106708527,0.0199999809265137,-0.537008106708527,-0.0199999809265137,-0.561098635196686,0.0199999809265137,-0.561098635196686,0.129344418644905,-0.0200002193450928,0.129344418644905,0.0199997425079346,0.188272789120674,-2.38418579101562e-07,0.0123446676880121,-0.561098694801331,0.0123446676880121,-0.537008106708527,0.0733976289629936,-0.549053311347961,-0.0123448241502047,-0.537008106708527,-0.0123448241502047,-0.561098635196686,-0.0733977854251862,-0.549053311347961,0.0951145738363266,0.019998935982585,0.0951145812869072,-0.0200010258704424,0.0361861810088158,-1.04555056168465e-06,-0.129344418644905,0.0199997425079346,-0.129344418644905,-0.0200002193450928,-0.188272789120674,-2.38418579101562e-07,0.0123448241502047,-0.561098635196686,0.0123448241502047,-0.537008106708527,0.0733977854251862,-0.549053311347961,-0.0123446676880121,-0.537008106708527,-0.0123446676880121,-0.561098694801331,-0.0733976289629936,-0.549053311347961,-0.0951145812869072,-0.0200010258704424,-0.0951145738363266,0.019998935982585,-0.0361861810088158,-1.04555056168465e-06,-0.0199999809265137,-0.503497838973999,0.0199999809265137,-0.503497838973999,0,-0.552247405052185,-0.0199997294694185,-0.503499031066895,0.0200002323836088,-0.503499150276184,-0,-0.552248597145081,-0.0200002323836088,-0.503499150276184,0.0199997294694185,-0.503499031066895,0,-0.552248597145081,-0.0199999809265137,-0.50350034236908,0.0199999809265137,-0.50350034236908,0,-0.552249789237976,0.110933542251587,-0.0677645206451416,-0.110933542251587,-0.0677645206451416,0.110933542251587,0.0626471638679504,-0.110933542251587,0.0626471638679504,0.0712112188339233,-0.0677645206451416,-0.0712112188339233,-0.0677652359008789,0.0712112188339233,0.0626471638679504,-0.0712112188339233,0.0626464486122131,0.0712112188339233,-0.0677652359008789,-0.0712112188339233,-0.0677645206451416,0.0712112188339233,0.0626464486122131,-0.0712112188339233,
0.0626471638679504,0.110933542251587,-0.0677652359008789,-0.110933542251587,-0.0677652359008789,0.110933542251587,0.0626464486122131,-0.110933542251587,0.0626464486122131,0.0199999809265137,-0.151855021715164,-0.0199999809265137,-0.151855021715164,0.110933542251587,-0.058817382901907,-0.110933542251587,-0.058817382901907,-0.0661494880914688,0.0490428283810616,-0.0661494880914688,-0.0490435287356377,-0.110933318734169,0.0199996307492256,-0.110933318734169,-0.0200003311038017,0.0200003366917372,-0.0984431728720665,-0.0199996251612902,-0.0984421595931053,0.0384218208491802,0.0303417798131704,-0.0384178645908833,0.0303432531654835,0.0632757917046547,-0.0490435287356377,0.0632757917046547,0.0490428283810616,0.110933758318424,-0.0200003311038017,0.110933758318424,0.0199996307492256,0.0199999809265137,-0.151854321360588,-0.0199999809265137,-0.151854321360588,0.110933542251587,-0.0588166825473309,-0.110933542251587,-0.0588166825473309,0.0199996251612902,-0.0984417051076889,-0.0200003366917372,-0.0984427109360695,0.0384178645908833,0.0303437113761902,-0.0384218208491802,0.0303422380238771,0.110933542251587,0.0384198427200317,-0.110933542251587,0.0384198427200317,0.110933542251587,0.0712112188339233,-0.110933542251587,0.0712112188339233,-0.110933542251587,0.0199999809265137,-0.110933542251587,-0.0199999809265137,-0.110933542251587,0.0712112188339233,-0.110933542251587,-0.0712112188339233,0.110933542251587,0.0384198427200317,0.110933542251587,-0.0384198427200317,0.110933542251587,0.0712112188339233,0.110933542251587,-0.0712112188339233,0.110933542251587,-0.0199999809265137,0.110933542251587,0.0199999809265137,0.110933542251587,-0.0712112188339233,0.110933542251587,0.0712112188339233,-0.110933542251587,-0.0384198427200317,0.110933542251587,-0.0384198427200317,-0.110933542251587,-0.0712112188339233,0.110933542251587,-0.0712112188339233,-0.110933542251587,-0.0384198427200317,-0.110933542251587,0.0384198427200317,-0.110933542251587,-0.0712112188339233,-0.110933542251587,0.0712112188339233,-0.0661497116088867,0.0626464486122131,0.0632755756378174,
0.0626464486122131,-0.0661497116088867,0.0479297041893005,0.0632755756378174,0.0479297041893005,-0.0843214243650436,0.0626471638679504,-0.0285111274570227,0.0626471638679504,-0.0843214243650436,0.0479304194450378,-0.0285111274570227,0.0479304194450378,0.0285111274570227,0.0626464486122131,0.0843214243650436,0.0626464486122131,0.0285111274570227,0.0479297041893005,0.0843214243650436,0.0479297041893005,-0.0199999809265137,0.0626464486122131,0.0199999809265137,0.0626471638679504,-0.0199999809265137,0.0479297041893005,0.0199999809265137,0.0479304194450378,-0.0632755756378174,0.0626471638679504,0.0661497116088867,0.0626471638679504,-0.0632755756378174,0.0479304194450378,0.0661497116088867,0.0479304194450378,-0.08219213783741,0.0626464486122131,-0.0288151856511831,0.0626464486122131,-0.08219213783741,0.0479297041893005,-0.0288151856511831,0.0479297041893005,0.0288151856511831,0.0626471638679504,0.08219213783741,0.0626471638679504,0.0288151856511831,0.0479304194450378,0.08219213783741,0.0479304194450378,-0.0199999809265137,0.0626471638679504,0.0199999809265137,0.0626464486122131,-0.0199999809265137,0.0479304194450378,0.0199999809265137,0.0479297041893005,-0.110933542251587,-0.0712112188339233,0.110933542251587,-0.0712112188339233,0.110933542251587,-0.0199999809265137,0.0632755756378174,-0.0490431785583496,-0.0661497116088867,-0.0490431785583496,-0.110933542251587,-0.0199999809265137,0.110933542251587,0.0712112188339233,-0.110933542251587,0.0712112188339233,-0.110933542251587,0.0199999809265137,-0.0661497116088867,0.0490431785583496,0.0632755756378174,0.0490431785583496,0.110933542251587,0.0199999809265137
			} 
			UVIndex: *282 {
				a: 18,17,16,21,20,19,23,22,24,26,25,27,30,29,28,33,32,31,35,34,36,38,37,39,60,62,61,63,65,64,66,68,67,69,71,70,72,74,73,75,77,76,78,80,79,81,83,82,84,86,85,87,89,88,90,92,91,93,95,94,194,195,193,192,193,195,192,195,196,196,197,192,198,202,203,201,199,200,202,199,201,202,198,199,0,2,1,2,0,3,4,6,5,6,4,7,9,11,10,11,9,8,13,15,14,15,13,12,42,40,41,40,42,43,47,45,46,45,47,44,51,49,50,49,51,48,52,55,53,55,52,54,56,59,57,59,56,58,96,99,97,99,96,98,100,103,101,103,100,102,104,107,105,107,104,106,108,111,109,111,108,110,112,115,113,115,112,114,116,119,117,119,116,118,120,123,121,123,120,122,124,127,125,127,124,126,128,131,129,131,128,130,132,135,133,135,132,134,136,139,137,139,136,138,140,143,141,143,140,142,144,147,145,147,144,146,148,151,149,151,148,150,152,155,153,155,152,154,156,159,157,159,156,158,160,163,161,163,160,162,164,167,165,167,164,166,168,171,169,171,168,170,172,175,173,175,172,174,176,179,177,179,176,178,180,183,181,183,180,182,184,187,185,187,184,186,188,191,189,191,188,190
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2344109115312, "Model::Hammer_Inventory_Local", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2341805108656, "Material::BlackHole_small", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0,0,0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0,0,0
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Hammer_Inventory_Local, Model::RootNode
	C: "OO",2344109115312,0
	
	;Material::BlackHole_small, Model::Hammer_Inventory_Local
	C: "OO",2341805108656,2344109115312
	
	;Geometry::Scene, Model::Hammer_Inventory_Local
	C: "OO",2331606762064,2344109115312
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
