using UdonSharp;
using UnityEditor;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

namespace OnlinePinboard
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class PinboardIdGenerator : UdonSharpBehaviour
    {
        public string pinboardId;
        public string hashKey;

        [Header("References")]
        public NoteDownloader noteDownloader;
        private string noteDownloadBaseURL = "https://memo-bulletinboard.oikki-api.win/getNotesV3";
        private string noteUploadBaseURL = "https://memo-bulletinboard.oikki-api.win/addNoteV3";
        private string pinboardCreateBaseURL = "https://memo-bulletinboard.oikki-api.win/addPinboardV3";

        public string GetUserHash()
        {
            return UdonHashLib.MD5_UTF8(Networking.LocalPlayer.displayName + hashKey);
        }
        public string GetUploadBaseUrl()
        {
            return noteUploadBaseURL;
        }
        private void OnValidate()
        {
#if !COMPILER_UDONSHARP && UNITY_EDITOR
            //if (PrefabStageUtility.GetCurrentPrefabStage() != null) return;
            if (pinboardId == "")
            {
                if (!ThisIsInstatiated()) return;
                string newPinboardId = GenerateRandomString(14);
                string newHashKey = GenerateRandomString(32);
                pinboardId = newPinboardId;
                hashKey = newHashKey;
            }
            noteDownloader.noteDownloadURL = new VRCUrl($"{noteDownloadBaseURL}?pinboardId={pinboardId}");
            noteDownloader.pinboardCreateURL = new VRCUrl($"{pinboardCreateBaseURL}?pinboardId={pinboardId}&hashKey={hashKey}");
#endif
        }

#if !COMPILER_UDONSHARP && UNITY_EDITOR
        private bool ThisIsInstatiated()
        {
            var pinboardGeneratorScripts = FindObjectsOfType<PinboardIdGenerator>();
            bool instatiated = false;
            foreach (var script in pinboardGeneratorScripts)
            {
                if (script == this)
                {
                    instatiated = true;
                    break;
                }
            }
            return instatiated;
        }
#endif

        private string pinboardIdCharacters = "abcdefghijklmnopqrstuvwxyz0123456789";
        public string GenerateRandomString(int length)
        {
            string randomString = "";

            for (int i = 0; i < length; i++)
            {
                int index = Random.Range(0, pinboardIdCharacters.Length);
                randomString += pinboardIdCharacters[index];
            }

            return randomString;
        }
    }
}