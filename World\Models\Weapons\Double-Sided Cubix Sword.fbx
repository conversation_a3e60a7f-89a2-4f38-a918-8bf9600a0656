; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 4
		Day: 15
		Hour: 4
		Minute: 50
		Second: 43
		Millisecond: 430
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\gogq0efc_Double-Sided Cubix Sword.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\gogq0efc_Double-Sided Cubix Sword.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2568018718784, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2572955739920, "Geometry::Scene", "Mesh" {
		Vertices: *168 {
			a: 1.99999809265137,-5.06799817085266,1.99998617172241,-1.99999809265137,-5.06799817085266,1.99998617172241,-1.99999809265137,5.06799817085266,2.00001001358032,1.99999809265137,5.06799817085266,2.00001001358032,-1.99999809265137,-5.06799817085266,-2.00001001358032,-1.99999809265137,5.06799817085266,-1.99998617172241,1.99999809265137,-5.06799817085266,-2.00001001358032,1.99999809265137,5.06799817085266,-1.99998617172241,1.99999809265137,-5.06799817085266,-3.54965850710869,-1.99999809265137,-5.06799817085266,-3.54965850710869,1.99999809265137,-7.47705698013306,-3.54965850710869,-1.99999809265137,-7.47705698013306,-3.54965850710869,-1.99999809265137,-5.06799817085266,3.54961641132832,1.99999809265137,-5.06799817085266,3.54961641132832,-1.99999809265137,-7.47705101966858,3.54961641132832,1.99999809265137,-7.47705101966858,3.54961641132832,1.99999809265137,-24.1695642471313,1.99998617172241,-1.99999809265137,-24.1695642471313,1.99998617172241,-1.99999809265137,-24.1695880889893,-2.00001001358032,1.99999809265137,-24.1695880889893,-2.00001001358032,1.99999809265137,5.06799817085266,3.54965850710869,-1.99999809265137,5.06799817085266,3.54965850710869,1.99999809265137,7.47705698013306,3.54965850710869,-1.99999809265137,7.47705698013306,3.54965850710869,-1.99999809265137,5.06799817085266,-3.54961641132832,1.99999809265137,5.06799817085266,-3.54961641132832,-1.99999809265137,7.47705101966858,-3.54961641132832,1.99999809265137,7.47705101966858,-3.54961641132832,-1.99999809265137,24.169585108757,2.00001001358032,1.99999809265137,24.169585108757,2.00001001358032,1.99999809265137,24.1695657372475,-1.99998617172241,-1.99999809265137,24.1695657372475,-1.99998617172241,-1.99999809265137,7.47705698013306,2.00001001358032,-1.99999809265137,7.47705101966858,-1.99998617172241,1.99999809265137,7.47705101966858,-1.99998617172241,1.99999809265137,7.47705698013306,2.00001001358032,-1.99999809265137,-7.47705698013306,-2.00001001358032,-1.99999809265137,-7.47705101966858,1.99998617172241,1.99999809265137,-7.47705101966858,1.99998617172241,
1.99999809265137,-7.47705698013306,-2.00001001358032,5.11074513196945,7.47705101966858,-1.99998617172241,5.11074513196945,7.47705698013306,2.00001001358032,5.11074513196945,5.06799817085266,-1.99998617172241,5.11074513196945,5.06799817085266,2.00001001358032,-5.11074513196945,7.47705698013306,2.00001001358032,-5.11074513196945,7.47705101966858,-1.99998617172241,-5.11074513196945,5.06799817085266,2.00001001358032,-5.11074513196945,5.06799817085266,-1.99998617172241,-5.11074513196945,-5.06799817085266,1.99998617172241,-5.11074513196945,-5.06799817085266,-2.00001001358032,-5.11074513196945,-7.47705101966858,1.99998617172241,-5.11074513196945,-7.47705698013306,-2.00001001358032,5.11074513196945,-5.06799817085266,-2.00001001358032,5.11074513196945,-5.06799817085266,1.99998617172241,5.11074513196945,-7.47705698013306,-2.00001001358032,5.11074513196945,-7.47705101966858,1.99998617172241
		} 
		PolygonVertexIndex: *220 {
			a: 0,3,2,-2,1,2,5,-5,6,4,5,-8,0,6,7,-4,5,2,3,-8,8,10,11,-10,12,14,15,-14,18,19,16,-18,20,22,23,-22,24,26,27,-26,28,29,30,-32,32,28,31,-34,33,31,30,-35,34,30,29,-36,35,29,28,-33,36,18,17,-38,37,17,16,-39,38,16,19,-40,39,19,18,-37,41,43,42,-41,45,47,46,-45,49,51,50,-49,53,55,54,-53,35,41,40,-35,34,40,42,-8,7,42,43,-4,3,43,41,-36,0,53,52,-7,6,52,54,-40,39,54,55,-39,38,55,53,-1,4,49,48,-2,1,48,50,-38,37,50,51,-37,36,51,49,-5,33,45,44,-33,32,44,46,-3,2,46,47,-6,5,47,45,-34,6,8,9,-5,39,10,8,-7,4,9,11,-37,36,11,10,-40,5,24,25,-8,33,26,24,-6,7,25,27,-35,34,27,26,-34,3,20,21,-3,35,22,20,-4,2,21,23,-33,32,23,22,-36,1,12,13,-1,37,14,12,-2,0,13,15,-39,38,15,14,-38
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *660 {
				a: -0,-2.35219681599119e-06,1,-0,-2.35219681599119e-06,1,-0,-2.35219681599119e-06,1,-0,-2.35219681599119e-06,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,2.35219681599119e-06,-1,-0,2.35219681599119e-06,-1,-0,2.35219681599119e-06,-1,-0,2.35219681599119e-06,-1,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-1,5.96047038925462e-06,-0,-1,5.96047038925462e-06,-0,-1,5.96047038925462e-06,-0,-1,5.96047038925462e-06,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,1,-4.842881935474e-06,-0,1,-4.842881935474e-06,-0,1,-4.842881935474e-06,-0,1,-4.842881935474e-06,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,-1.49011759731366e-06,-0,1,-1.49011759731366e-06,-0,1,-1.49011759731366e-06,-0,1,-1.49011759731366e-06,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,1.49011759731366e-06,-0,-1,1.49011759731366e-06,-0,-1,1.49011759731366e-06,-0,-1,1.49011759731366e-06,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-1,1.49011759731366e-06,-0,-1,1.49011759731366e-06,-0,-1,1.49011759731366e-06,-0,-1,1.49011759731366e-06,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,1,-1.49011759731366e-06,-0,1,-1.49011759731366e-06,-0,1,-1.49011759731366e-06,-0,1,-1.49011759731366e-06,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0
			} 
			NormalsW: *220 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *660 {
				a: 0.340383917093277,0.940286576747894,2.21173900172289e-06,0,1,2.35219681599119e-06,0.340383917093277,0.940286576747894,2.21173900172289e-06,0.586436688899994,0.809994995594025,1.90526759524801e-06,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0.930188298225403,-0.367082804441452,-8.63451020904904e-07,0.930188298225403,0.367082804441452,8.63451020904904e-07,-0.930188298225403,-0.367082804441452,-8.63451020904904e-07,-0.930188298225403,-0.367082804441452,-8.63451020904904e-07,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0.0148691041395068,0.999889492988586,0,-0.0074351686052978,0.999972403049469,0,0,1,-0,-0.0074351686052978,0.999972403049469,0,0.2883420586586,0.957527458667755,0,0.148887753486633,0.988854110240936,0,-0,1,0,0.148887753486633,0.988854110240936,0,0,5.96047038925462e-06,1,0,5.96047038925462e-06,1,0,5.96047038925462e-06,1,0,5.96047038925462e-06,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,4.842881935474e-06,1,0,4.842881935474e-06,1,0,4.842881935474e-06,1,0,4.842881935474e-06,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,0.823491454124451,-0.567328691482544,-0,0.719122767448425,-0.694883108139038,-0,0.52585905790329,-0.850571751594543,-0,0.719122767448425,-0.694883108139038,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0.967917382717133,-0.251268595457077,-0,-0.932645380496979,-0.360794484615326,-0,-0.796613872051239,-0.604488551616669,-0,-0.932645380496979,-0.360794484615326,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1.56682199303759e-06,1.49011759731366e-06,1,-1.71371129908948e-06,1.49011759731366e-06,1,-1.86060060514137e-06,1.49011759731366e-06,1,-1.71371129908948e-06,1.49011759731366e-06,1,-0.932478487491608,-0.361225545406342,0,-0.851314187049866,-0.524656295776367,0,-0.55304354429245,-0.833152413368225,0,-0.851314187049866,-0.524656295776367,0,-1.95852706497135e-07,0,1,-4.89631929667667e-07,0,1,
-7.83411167049053e-07,0,1,-4.89631929667667e-07,0,1,-0.612290620803833,-0.790632843971252,-0,-0.612290620803833,-0.790632843971252,-0,-0.612290620803833,-0.790632843971252,-0,-0.612290620803833,-0.790632843971252,-0,0,-0,1,-9.79263745648495e-08,-0,1,-1.95852749129699e-07,-0,1,-9.79263745648495e-08,-0,1,-1,-0,0,-1,-0,0,-0,-1,0,-1,-0,0,0,1.49011759731366e-06,1,0,1.49011759731366e-06,1,0,1.49011759731366e-06,1,0,1.49011759731366e-06,1,-0.249949291348457,-0.96825897693634,-0,-0.249949291348457,-0.96825897693634,-0,-0.249949291348457,-0.96825897693634,-0,-0.249949291348457,-0.96825897693634,-0,1.66474819707219e-06,-0,1,1.81163807155826e-06,-0,1,1.95852771867067e-06,-0,1,1.81163807155826e-06,-0,1,0.968232750892639,0.250050723552704,0,0.840114593505859,0.542409002780914,0,0.612289667129517,-0.790633618831635,0,0.840114593505859,0.542409002780914,0,3.91705640367945e-07,1.49011759731366e-06,1,6.36521463093231e-07,1.49011759731366e-06,1,8.81337371083646e-07,1.49011759731366e-06,1,6.36521463093231e-07,1.49011759731366e-06,1,0.458753973245621,-0.888563334941864,-0,0.411691546440125,-0.911323249340057,-0,0.361090391874313,-0.93253082036972,-0,0.411691546440125,-0.911323249340057,-0,9.79263461431401e-08,1.49011759731366e-06,1,1.46889533425565e-07,1.49011759731366e-06,1,1.95852706497135e-07,1.49011759731366e-06,1,1.46889533425565e-07,1.49011759731366e-06,1,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,0,1,4.8963162413429e-08,0,1,9.79263177214307e-08,0,1,4.8963162413429e-08,0,1,0.249949291348457,-0.96825897693634,-0,0.128009632229805,-0.991772949695587,-0,-0,-1,0,0.128009632229805,-0.991772949695587,-0,-7.61565672746656e-07,-0,1,-3.80782836373328e-07,-0,1,0,-0,1,-3.80782836373328e-07,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-1.52312296108903e-07,0,1,-1.52312296108903e-07,0,1,-1.52312296108903e-07,0,1,-1.52312296108903e-07,0,1,-7.61556862016732e-08,0,1,-1.14233507986228e-07,0,1,-1.52311343981637e-07,0,1,-1.14233507986228e-07,0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1.52312125578646e-07,-0,1,-1.52312125578646e-07,-0,1,
-1.52312125578646e-07,-0,1,-1.52312125578646e-07,-0,1,-1.06618506379164e-06,0,1,-5.33092475052399e-07,0,1,0,0,1,-5.33092475052399e-07,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-1.52312324530612e-07,-0,1,-6.08252665357419e-14,-0,1,1.52312210843775e-07,-0,1,-6.08252665357419e-14,-0,1,-1.52311372403346e-07,-0,1,-7.61556790962459e-08,-0,1,0,-0,1,-7.61556790962459e-08,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,3.04624308000712e-07,0,1,2.28468195473397e-07,0,1,1.52312082946082e-07,0,1,2.28468195473397e-07,0,1
			} 
			BinormalsW: *220 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *660 {
				a: -0.940286576747894,0.340383917093277,8.00649957000132e-07,-1,0,0,-0.940286576747894,0.340383917093277,8.00649957000132e-07,-0.809994995594025,0.586436688899994,1.37941458433488e-06,0,1,0,0,1,0,0,1,0,0,1,0,-0.367082804441452,0.930188298225403,2.18798595597036e-06,-0.367082804441452,0.930188298225403,2.18798595597036e-06,-0.367082804441452,0.930188298225403,2.18798595597036e-06,-0.367082804441452,0.930188298225403,2.18798595597036e-06,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.999889492988586,-0.0148691041395068,0,-0.999972403049469,-0.0074351686052978,0,-1,0,0,-0.999972403049469,-0.0074351686052978,0,-0.957527458667755,0.2883420586586,0,-0.988854110240936,0.148887753486633,0,-1,0,0,-0.988854110240936,0.148887753486633,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.567328691482544,-0.823491454124451,0,-0.694883108139038,-0.719122767448425,0,-0.850571751594543,-0.52585905790329,0,-0.694883108139038,-0.719122767448425,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.251268595457077,0.967917382717133,0,-0.360794484615326,0.932645380496979,0,-0.604488551616669,0.796613872051239,0,-0.360794484615326,0.932645380496979,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,-2.33474893064634e-12,-1.56682199303759e-06,-1,-2.5536313515151e-12,-1.71371129908948e-06,-1,-2.77251377238386e-12,-1.86060060514137e-06,-1,-2.5536313515151e-12,-1.71371129908948e-06,-0.361225545406342,0.932478487491608,0,-0.524656295776367,0.851314187049866,0,-0.833152413368225,0.55304354429245,0,-0.524656295776367,0.851314187049866,0,-1,0,-1.95852706497135e-07,-1,0,-4.89631929667667e-07,-1,0,-7.83411167049053e-07,-1,0,-4.89631929667667e-07,-0.790632843971252,0.612290620803833,0,-0.790632843971252,0.612290620803833,0,-0.790632843971252,0.612290620803833,0,-0.790632843971252,0.612290620803833,0,
-1,0,0,-1,0,-9.79263745648495e-08,-1,0,-1.95852749129699e-07,-1,0,-9.79263745648495e-08,-0,1,0,-0,1,0,-1,0,0,-0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.96825897693634,0.249949291348457,0,-0.96825897693634,0.249949291348457,0,-0.96825897693634,0.249949291348457,0,-0.96825897693634,0.249949291348457,0,-1,0,1.66474819707219e-06,-1,0,1.81163807155826e-06,-1,0,1.95852771867067e-06,-1,0,1.81163807155826e-06,-0.250050723552704,0.968232750892639,0,-0.542409002780914,0.840114593505859,0,-0.790633618831635,-0.612289667129517,0,-0.542409002780914,0.840114593505859,0,-1,5.83687449502018e-13,3.91705640367945e-07,-1,9.48491827613973e-13,6.36521463093231e-07,-1,1.31329636835625e-12,8.81337371083646e-07,-1,9.48491827613973e-13,6.36521463093231e-07,-0.888563334941864,-0.458753973245621,0,-0.911323249340057,-0.411691546440125,0,-0.93253082036972,-0.361090391874313,0,-0.911323249340057,-0.411691546440125,0,-1,1.45921767507814e-13,9.79263461431401e-08,-1,2.18882678366776e-13,1.46889533425565e-07,-1,2.91843562120683e-13,1.95852706497135e-07,-1,2.18882678366776e-13,1.46889533425565e-07,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,4.8963162413429e-08,-1,0,9.79263177214307e-08,-1,0,4.8963162413429e-08,-0.96825897693634,-0.249949291348457,0,-0.991772949695587,-0.128009632229805,0,-1,0,0,-0.991772949695587,-0.128009632229805,0,-1,0,-7.61565672746656e-07,-1,0,-3.80782836373328e-07,-1,0,0,-1,0,-3.80782836373328e-07,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,-1.52312296108903e-07,-1,0,-1.52312296108903e-07,-1,0,-1.52312296108903e-07,-1,0,-1.52312296108903e-07,-1,0,-7.61556862016732e-08,-1,0,-1.14233507986228e-07,-1,0,-1.52311343981637e-07,-1,0,-1.14233507986228e-07,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,-1.52312125578646e-07,-1,0,-1.52312125578646e-07,-1,0,-1.52312125578646e-07,-1,0,-1.52312125578646e-07,-1,0,-1.06618506379164e-06,-1,0,-5.33092475052399e-07,-1,0,0,-1,0,-5.33092475052399e-07,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,-1.52312324530612e-07,-1,0,-6.08252665357419e-14,-1,0,1.52312210843775e-07,
-1,0,-6.08252665357419e-14,-1,0,-1.52311372403346e-07,-1,0,-7.61556790962459e-08,-1,0,0,-1,0,-7.61556790962459e-08,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,3.04624308000712e-07,-1,0,2.28468195473397e-07,-1,0,1.52312082946082e-07,-1,0,2.28468195473397e-07
			} 
			TangentsW: *220 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *440 {
				a: 0.30433401465416,0.695665717124939,0.695666015148163,0.695665597915649,0.695666015148163,0.695666015148163,0.30433401465416,0.695666015148163,0.695666015148163,0.695665717124939,0.695666015148163,0.304334342479706,0.695666015148163,0.304333984851837,0.695666015148163,0.695666134357452,0.695666015148163,0.304333865642548,0.30433401465416,0.304334282875061,0.30433401465416,0.304333984851837,0.695666015148163,0.304334282875061,0.30433401465416,0.304333865642548,0.30433401465416,0.695665717124939,0.30433401465416,0.695666134357452,0.30433401465416,0.304334282875061,0.695666015148163,0.695666372776031,0.30433401465416,0.695666372776031,0.30433401465416,0.304334223270416,0.695666015148163,0.304334223270416,0.30433401465416,0.152729064226151,0.695666015148163,0.152729123830795,0.30433401465416,0.152726650238037,0.695666015148163,0.152726650238037,0.695666015148163,0.847270846366882,0.30433401465416,0.847270905971527,0.695666015148163,0.847270727157593,0.30433401465416,0.847270727157593,0.30433401465416,0.695667028427124,0.695666015148163,0.695667028427124,0.695666015148163,0.304334938526154,0.30433401465416,0.304334938526154,0.30433401465416,0.847272098064423,0.695666015148163,0.847272098064423,0.30433401465416,0.847272574901581,0.695666015148163,0.847272574901581,0.695666015148163,0.152728021144867,0.30433401465416,0.152728021144867,0.695666015148163,0.152728617191315,0.30433401465416,0.152728617191315,0.695666015148163,0.695665657520294,0.30433401465416,0.695665657520294,0.30433401465416,0.304333627223969,0.695666015148163,0.304333627223969,0.695666015148163,0.695666015148163,0.695666015148163,0.304333955049515,0.695666015148163,0.695665121078491,0.695666015148163,0.30433315038681,0.695666015148163,0.304333716630936,0.30433401465416,0.304333955049515,0.695666015148163,0.304333031177521,0.30433401465416,0.30433315038681,0.30433401465416,0.304333716630936,0.30433401465416,0.695666015148163,0.30433401465416,0.304333031177521,0.30433401465416,0.695665121078491,0.30433401465416,0.695665717124939,0.695666015148163,
0.695665717124939,0.30433401465416,0.695665061473846,0.695666015148163,0.695665061473846,0.695666015148163,0.304333388805389,0.695666015148163,0.69566535949707,0.695666015148163,0.30433452129364,0.695666015148163,0.695666491985321,0.695666015148163,0.695666074752808,0.30433401465416,0.69566535949707,0.695666015148163,0.69566684961319,0.30433401465416,0.695666491985321,0.30433401465416,0.695666074752808,0.30433401465416,0.304333388805389,0.30433401465416,0.69566684961319,0.30433401465416,0.30433452129364,0.30433401465416,0.304334104061127,0.695666015148163,0.304334104061127,0.30433401465416,0.304334878921509,0.695666015148163,0.304334878921509,0,0.304334431886673,0,0.695665538311005,0,0.304333686828613,0,0.695666432380676,1,0.695666432380676,1,0.304334342479706,1,0.695666432380676,1,0.304334431886673,1,0.695665717124939,1,0.304333925247192,1,0.695666074752808,1,0.304333806037903,0,0.304333746433258,0,0.695665895938873,0,0.304334044456482,0,0.695665776729584,0.30433401465416,0.695666551589966,0.30433401465416,0.304334580898285,0,0.695666074752808,0,0.30433401465416,0.30433401465416,0.304334431886673,0.30433401465416,0.30433452129364,0,0.304334133863449,0,0.304334342479706,0.30433401465416,0.30433452129364,0.30433401465416,0.695666551589966,0,0.304334461688995,0,0.695666313171387,0.30433401465416,0.695666551589966,0.30433401465416,0.695666432380676,0,0.695666432380676,0,0.695666372776031,0.30433401465416,0.695665895938873,0.30433401465416,0.304333984851837,0,0.695665895938873,0,0.304333925247192,0.30433401465416,0.304333686828613,0.30433401465416,0.304333686828613,0,0.304333657026291,0,0.304333686828613,0.30433401465416,0.304333686828613,0.30433401465416,0.695665776729584,0,0.304333686828613,0,0.695665776729584,0.30433401465416,0.695665776729584,0.30433401465416,0.695665597915649,0,0.695665717124939,0,0.695665538311005,0.695666015148163,0.304334133863449,0.695666015148163,0.695666134357452,1,0.304333627223969,1,0.695665538311005,0.695666015148163,0.695665776729584,0.695666015148163,0.695665717124939,1,0.69566547870636,
1,0.695665597915649,0.695666015148163,0.695665717124939,0.695666015148163,0.304333925247192,1,0.695665597915649,1,0.304333657026291,0.695666015148163,0.304333806037903,0.695666015148163,0.304333627223969,1,0.304333686828613,1,0.304333567619324,0.695666015148163,0.304333955049515,0.695666015148163,0.695666015148163,1,0.304333925247192,1,0.695665955543518,0.695666015148163,0.695666134357452,0.695666015148163,0.695666372776031,1,0.695666134357452,1,0.695666372776031,0.695666015148163,0.695666432380676,0.695666015148163,0.304334282875061,1,0.695666432380676,1,0.304334253072739,0.695666015148163,0.304334431886673,0.695666015148163,0.304334342479706,1,0.304334402084351,1,0.304334342479706,0.30433401465416,0.304333567619324,0.695666015148163,0.304333865642548,0.30433401465416,0.152727574110031,0.695666015148163,0.152727574110031,0.30433401465416,0.304333627223969,0.30433401465416,0.304333508014679,0.30433401465416,0.152727156877518,0.30433401465416,0.152726829051971,0.695666015148163,0.304333627223969,0.695666015148163,0.304333657026291,0.695666015148163,0.152727007865906,0.695666015148163,0.152726918458939,0.695666015148163,0.304333686828613,0.30433401465416,0.304333627223969,0.695666015148163,0.152726858854294,0.30433401465416,0.152726799249649,0.695666015148163,0.304334133863449,0.30433401465416,0.304334104061127,0.695666015148163,0.152728170156479,0.30433401465416,0.152728110551834,0.695666015148163,0.304334223270416,0.695666015148163,0.304334402084351,0.695666015148163,0.152728676795959,0.695666015148163,0.152729064226151,0.30433401465416,0.304334342479706,0.30433401465416,0.304334312677383,0.30433401465416,0.152728766202927,0.30433401465416,0.152728974819183,0.30433401465416,0.304334282875061,0.695666015148163,0.304334342479706,0.30433401465416,0.152729064226151,0.695666015148163,0.152729123830795,0.30433401465416,0.695666074752808,0.695666015148163,0.695666491985321,0.30433401465416,0.847273051738739,0.695666015148163,0.847273051738739,0.30433401465416,0.695666193962097,0.30433401465416,0.695666372776031,0.30433401465416,
0.847273111343384,0.30433401465416,0.847273349761963,0.695666015148163,0.695666551589966,0.695666015148163,0.695666432380676,0.695666015148163,0.847273230552673,0.695666015148163,0.847273170948029,0.695666015148163,0.695666372776031,0.30433401465416,0.695666313171387,0.695666015148163,0.847273170948029,0.30433401465416,0.847273230552673,0.695666015148163,0.695665895938873,0.30433401465416,0.695665836334229,0.695666015148163,0.847271859645844,0.30433401465416,0.847271859645844,0.695666015148163,0.695665776729584,0.695666015148163,0.695665597915649,0.695666015148163,0.847271382808685,0.695666015148163,0.847270905971527,0.30433401465416,0.695665597915649,0.30433401465416,0.695665657520294,0.30433401465416,0.847271203994751,0.30433401465416,0.847271025180817,0.30433401465416,0.695665717124939,0.695666015148163,0.695665597915649,0.30433401465416,0.847270905971527,0.695666015148163,0.847270846366882
			} 
			UVIndex: *220 {
				a: 0,3,2,1,4,7,6,5,9,8,11,10,13,12,15,14,19,16,17,18,20,22,23,21,24,26,27,25,30,31,28,29,32,34,35,33,36,38,39,37,40,41,42,43,44,46,47,45,48,50,51,49,52,54,55,53,56,58,59,57,60,62,63,61,64,66,67,65,68,70,71,69,72,74,75,73,77,79,78,76,81,83,82,80,85,87,86,84,89,91,90,88,92,94,95,93,96,98,99,97,100,102,103,101,104,106,107,105,108,110,111,109,112,114,115,113,116,118,119,117,120,122,123,121,124,126,127,125,128,130,131,129,132,134,135,133,136,138,139,137,140,142,143,141,144,146,147,145,148,150,151,149,152,154,155,153,156,158,159,157,160,162,163,161,164,166,167,165,168,170,171,169,172,174,175,173,176,178,179,177,180,182,183,181,184,186,187,185,188,190,191,189,192,194,195,193,196,198,199,197,200,202,203,201,204,206,207,205,208,210,211,209,212,214,215,213,216,218,219,217
			} 
		}
		LayerElementUV: 1 {
			Version: 101
			Name: "UVSet2"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *440 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			} 
			UVIndex: *220 {
				a: 0,3,2,1,4,7,6,5,9,8,11,10,13,12,15,14,19,16,17,18,20,22,23,21,24,26,27,25,30,31,28,29,32,34,35,33,36,38,39,37,40,41,42,43,44,46,47,45,48,50,51,49,52,54,55,53,56,58,59,57,60,62,63,61,64,66,67,65,68,70,71,69,72,74,75,73,77,79,78,76,81,83,82,80,85,87,86,84,89,91,90,88,92,94,95,93,96,98,99,97,100,102,103,101,104,106,107,105,108,110,111,109,112,114,115,113,116,118,119,117,120,122,123,121,124,126,127,125,128,130,131,129,132,134,135,133,136,138,139,137,140,142,143,141,144,146,147,145,148,150,151,149,152,154,155,153,156,158,159,157,160,162,163,161,164,166,167,165,168,170,171,169,172,174,175,173,176,178,179,177,180,182,183,181,184,186,187,185,188,190,191,189,192,194,195,193,196,198,199,197,200,202,203,201,204,206,207,205,208,210,211,209,212,214,215,213,216,218,219,217
			} 
		}
		LayerElementUV: 2 {
			Version: 101
			Name: "UVSet3"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *440 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			} 
			UVIndex: *220 {
				a: 0,3,2,1,4,7,6,5,9,8,11,10,13,12,15,14,19,16,17,18,20,22,23,21,24,26,27,25,30,31,28,29,32,34,35,33,36,38,39,37,40,41,42,43,44,46,47,45,48,50,51,49,52,54,55,53,56,58,59,57,60,62,63,61,64,66,67,65,68,70,71,69,72,74,75,73,77,79,78,76,81,83,82,80,85,87,86,84,89,91,90,88,92,94,95,93,96,98,99,97,100,102,103,101,104,106,107,105,108,110,111,109,112,114,115,113,116,118,119,117,120,122,123,121,124,126,127,125,128,130,131,129,132,134,135,133,136,138,139,137,140,142,143,141,144,146,147,145,148,150,151,149,152,154,155,153,156,158,159,157,160,162,163,161,164,166,167,165,168,170,171,169,172,174,175,173,176,178,179,177,180,182,183,181,184,186,187,185,188,190,191,189,192,194,195,193,196,198,199,197,200,202,203,201,204,206,207,205,208,210,211,209,212,214,215,213,216,218,219,217
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		Layer: 1 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 1
			}
		}
		Layer: 2 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 2
			}
		}
	}
	Model: 2570884436624, "Model::Double_Sided_Cubix_Sword", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2572642868096, "Material::Cubix_00X", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0,0.0236773937940598,0.75471693277359
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0,0.0236773937940598,0.75471693277359
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2570704280560, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Environment\Cubix Design.png"
			P: "RelPath", "KString", "XRefUrl", "", "..\Sprites\Environment\Cubix Design.png"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Environment\Cubix Design.png"
		RelativeFilename: "..\Sprites\Environment\Cubix Design.png"
	}
	Texture: 2570898957456, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Environment\Cubix Design.png"
		RelativeFilename: "..\Sprites\Environment\Cubix Design.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Double_Sided_Cubix_Sword, Model::RootNode
	C: "OO",2570884436624,0
	
	;Material::Cubix_00X, Model::Double_Sided_Cubix_Sword
	C: "OO",2572642868096,2570884436624
	
	;Geometry::Scene, Model::Double_Sided_Cubix_Sword
	C: "OO",2572955739920,2570884436624
	
	;Texture::DiffuseColor_Texture, Material::Cubix_00X
	C: "OO",2570898957456,2572642868096
	
	;Texture::DiffuseColor_Texture, Material::Cubix_00X
	C: "OP",2570898957456,2572642868096, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",2570704280560,2570898957456
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
