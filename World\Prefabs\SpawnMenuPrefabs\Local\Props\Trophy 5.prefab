%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &146714154164167344
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2043496670128245044}
  - component: {fileID: 5273457978499125792}
  - component: {fileID: 3013175145938003827}
  - component: {fileID: 1875049680233279501}
  - component: {fileID: 4642007644482903578}
  - component: {fileID: 45998206174343390}
  m_Layer: 11
  m_Name: Tower Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2043496670128245044
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146714154164167344}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.36323854, z: 0}
  m_LocalScale: {x: 1.6411381, y: 1.6411381, z: 1.6411381}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 9065666877118612199}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5273457978499125792
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146714154164167344}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8233d90336aea43098adf6dbabd606a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MeshFormatVersion: 2
  m_Faces:
  - m_Indexes: 000000000100000002000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 6
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 1
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 050000000400000003000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 060000000700000008000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 1
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 0b0000000a00000009000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 0c0000000d0000000e000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 1
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 11000000100000000f000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 120000001300000014000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 1
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 170000001600000015000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 18000000190000001a000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 1
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 1d0000001c0000001b000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 1e0000001f00000020000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 1
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 230000002200000021000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 240000002500000026000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 1
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 290000002800000027000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 2a0000002b0000002c000000
    m_SmoothingGroup: 1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 1
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 2f0000002e0000002d000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: -1
  m_SharedVertices:
  - m_Vertices: 00000000030000002b0000002e000000
  - m_Vertices: 01000000040000000600000009000000
  - m_Vertices: 02000000080000000e000000140000001a00000020000000260000002c000000
  - m_Vertices: 050000000b00000011000000170000001d00000023000000290000002f000000
  - m_Vertices: 070000000a0000000c0000000f000000
  - m_Vertices: 0d000000100000001200000015000000
  - m_Vertices: 1300000016000000180000001b000000
  - m_Vertices: 190000001c0000001e00000021000000
  - m_Vertices: 1f000000220000002400000027000000
  - m_Vertices: 25000000280000002a0000002d000000
  m_SharedTextures: []
  m_Positions:
  - {x: 0.14481896, y: -0.29978454, z: 0.000000059604645}
  - {x: 0.39062366, y: -0.29978448, z: -0.38220638}
  - {x: 0, y: 0.29978454, z: 0.00000008940697}
  - {x: 0.14481896, y: -0.29978454, z: 0.000000059604645}
  - {x: 0.39062366, y: -0.29978448, z: -0.38220638}
  - {x: 0, y: -0.29978454, z: 0.00000008940697}
  - {x: 0.39062366, y: -0.29978448, z: -0.38220638}
  - {x: 0, y: -0.29978454, z: -0.14169833}
  - {x: 0, y: 0.29978454, z: 0.00000008940697}
  - {x: 0.39062366, y: -0.29978448, z: -0.38220638}
  - {x: 0, y: -0.29978454, z: -0.14169833}
  - {x: 0, y: -0.29978454, z: 0.00000008940697}
  - {x: 0, y: -0.29978454, z: -0.14169833}
  - {x: -0.39062366, y: -0.29978448, z: -0.38220638}
  - {x: 0, y: 0.29978454, z: 0.00000008940697}
  - {x: 0, y: -0.29978454, z: -0.14169833}
  - {x: -0.39062366, y: -0.29978448, z: -0.38220638}
  - {x: 0, y: -0.29978454, z: 0.00000008940697}
  - {x: -0.39062366, y: -0.29978448, z: -0.38220638}
  - {x: -0.14481896, y: -0.29978454, z: 0.000000059604645}
  - {x: 0, y: 0.29978454, z: 0.00000008940697}
  - {x: -0.39062366, y: -0.29978448, z: -0.38220638}
  - {x: -0.14481896, y: -0.29978454, z: 0.000000059604645}
  - {x: 0, y: -0.29978454, z: 0.00000008940697}
  - {x: -0.14481896, y: -0.29978454, z: 0.000000059604645}
  - {x: -0.39062372, y: -0.29978448, z: 0.38220638}
  - {x: 0, y: 0.29978454, z: 0.00000008940697}
  - {x: -0.14481896, y: -0.29978454, z: 0.000000059604645}
  - {x: -0.39062372, y: -0.29978448, z: 0.38220638}
  - {x: 0, y: -0.29978454, z: 0.00000008940697}
  - {x: -0.39062372, y: -0.29978448, z: 0.38220638}
  - {x: 0, y: -0.29978454, z: 0.14169842}
  - {x: 0, y: 0.29978454, z: 0.00000008940697}
  - {x: -0.39062372, y: -0.29978448, z: 0.38220638}
  - {x: 0, y: -0.29978454, z: 0.14169842}
  - {x: 0, y: -0.29978454, z: 0.00000008940697}
  - {x: 0, y: -0.29978454, z: 0.14169842}
  - {x: 0.39062366, y: -0.29978448, z: 0.3822065}
  - {x: 0, y: 0.29978454, z: 0.00000008940697}
  - {x: 0, y: -0.29978454, z: 0.14169842}
  - {x: 0.39062366, y: -0.29978448, z: 0.3822065}
  - {x: 0, y: -0.29978454, z: 0.00000008940697}
  - {x: 0.39062366, y: -0.29978448, z: 0.3822065}
  - {x: 0.14481896, y: -0.29978454, z: 0.000000059604645}
  - {x: 0, y: 0.29978454, z: 0.00000008940697}
  - {x: 0.39062366, y: -0.29978448, z: 0.3822065}
  - {x: 0.14481896, y: -0.29978454, z: 0.000000059604645}
  - {x: 0, y: -0.29978454, z: 0.00000008940697}
  m_Textures0:
  - {x: 0.55598867, y: 0}
  - {x: 0, y: 0}
  - {x: 0.27799433, y: 0.89994943}
  - {x: -0.14481896, y: 0.0000001063557}
  - {x: -0.39062366, y: -0.38220632}
  - {x: -9.6209085e-15, y: 0.00000013615802}
  - {x: 0.55598867, y: 0}
  - {x: 0, y: 0}
  - {x: 0.27799433, y: 0.89994943}
  - {x: -0.3906236, y: -0.38220638}
  - {x: 0.000000045743647, y: -0.14169833}
  - {x: 0.000000045743647, y: 0.00000008940697}
  - {x: 0.55598867, y: 0}
  - {x: 0, y: 0}
  - {x: 0.27799433, y: 0.89994943}
  - {x: -0.000000045743647, y: -0.14169833}
  - {x: 0.3906236, y: -0.38220638}
  - {x: -0.000000045743647, y: 0.00000008940697}
  - {x: 0.55598867, y: 0}
  - {x: 0, y: 0}
  - {x: 0.27799433, y: 0.89994943}
  - {x: 0.39062366, y: -0.38220632}
  - {x: 0.14481896, y: 0.0000001063557}
  - {x: 9.6209085e-15, y: 0.00000013615802}
  - {x: 0.55598867, y: 0}
  - {x: 0, y: 0}
  - {x: 0.27799433, y: 0.89994943}
  - {x: 0.14481896, y: 0.000000012853589}
  - {x: 0.39062372, y: 0.38220632}
  - {x: -9.620909e-15, y: 0.000000042655913}
  - {x: 0.55598867, y: 0}
  - {x: 0, y: 0}
  - {x: 0.27799433, y: 0.89994943}
  - {x: 0.39062366, y: 0.38220638}
  - {x: -0.000000045743644, y: 0.14169842}
  - {x: -0.000000045743644, y: 0.00000008940697}
  - {x: 0.55598867, y: 0}
  - {x: 0, y: 0}
  - {x: 0.27799433, y: 0.89994943}
  - {x: 0.000000045743647, y: 0.14169842}
  - {x: -0.3906236, y: 0.3822065}
  - {x: 0.000000045743647, y: 0.00000008940697}
  - {x: 0.55598867, y: 0}
  - {x: 0, y: 0}
  - {x: 0.27799433, y: 0.89994943}
  - {x: -0.39062366, y: 0.38220644}
  - {x: -0.14481896, y: 0.000000012853606}
  - {x: 9.620905e-15, y: 0.00000004265593}
  m_Textures2: []
  m_Textures3: []
  m_Tangents:
  - {x: -0.035054408, y: 0.14512996, z: 0.9887914, w: -1}
  - {x: -0.23207524, y: 0.700472, z: 0.67489266, w: -1}
  - {x: -0.5409142, y: 0.00000007452208, z: 0.8410778, w: -1}
  - {x: -1, y: 3.2092743e-14, z: 0, w: -1}
  - {x: -1, y: 3.2092743e-14, z: 0, w: -1}
  - {x: -1, y: 3.2092743e-14, z: 0, w: -1}
  - {x: 0.6801259, y: -0.6999328, z: -0.21799722, w: -1}
  - {x: 0.9901216, y: -0.13645242, z: -0.03224827, w: -1}
  - {x: 0.8515374, y: -0.00000002711778, z: -0.52429396, w: -1}
  - {x: -1, y: -0.00000015258841, z: 0, w: -1}
  - {x: -1, y: -0.00000015258841, z: 0, w: -1}
  - {x: -1, y: -0.00000015258841, z: 0, w: -1}
  - {x: 0.9901216, y: 0.13645242, z: 0.03224827, w: -1}
  - {x: 0.6801259, y: 0.6999328, z: 0.21799722, w: -1}
  - {x: 0.8515374, y: 0.000000091142105, z: 0.52429396, w: -1}
  - {x: -1, y: 0.00000015258841, z: 0, w: -1}
  - {x: -1, y: 0.00000015258841, z: 0, w: -1}
  - {x: -1, y: 0.00000015258841, z: 0, w: -1}
  - {x: -0.23207524, y: -0.700472, z: -0.67489266, w: -1}
  - {x: -0.03505434, y: -0.14513001, z: -0.9887914, w: -1}
  - {x: -0.5409142, y: -0.00000011519164, z: -0.8410778, w: -1}
  - {x: -1, y: -3.2092743e-14, z: 0, w: -1}
  - {x: -1, y: -3.2092743e-14, z: 0, w: -1}
  - {x: -1, y: -3.2092743e-14, z: 0, w: -1}
  - {x: 0.035054483, y: 0.14513002, z: -0.9887914, w: -1}
  - {x: 0.23207548, y: 0.70047194, z: -0.67489254, w: -1}
  - {x: 0.54091436, y: -0.00000007452205, z: -0.8410777, w: -1}
  - {x: -1, y: 3.2092747e-14, z: 0, w: -1}
  - {x: -1, y: 3.2092747e-14, z: 0, w: -1}
  - {x: -1, y: 3.2092747e-14, z: 0, w: -1}
  - {x: -0.6801261, y: -0.69993275, z: 0.217997, w: -1}
  - {x: -0.9901216, y: -0.1364523, z: 0.032248195, w: -1}
  - {x: -0.8515375, y: 0.000000027117743, z: 0.5242937, w: -1}
  - {x: -1, y: 0.0000001525884, z: 0, w: -1}
  - {x: -1, y: 0.0000001525884, z: 0, w: -1}
  - {x: -1, y: 0.0000001525884, z: 0, w: -1}
  - {x: -0.9901216, y: 0.13645232, z: -0.032248404, w: -1}
  - {x: -0.68012595, y: 0.6999328, z: -0.21799722, w: -1}
  - {x: -0.85153735, y: -0.0000000911421, z: -0.52429396, w: -1}
  - {x: -1, y: -0.00000015258841, z: 0, w: -1}
  - {x: -1, y: -0.00000015258841, z: 0, w: -1}
  - {x: -1, y: -0.00000015258841, z: 0, w: -1}
  - {x: 0.23207535, y: -0.70047206, z: 0.6748926, w: -1}
  - {x: 0.035054408, y: -0.14512996, z: 0.9887914, w: -1}
  - {x: 0.5409142, y: 0.000000115191646, z: 0.8410778, w: -1}
  - {x: -1, y: -3.2092733e-14, z: -5.283751e-28, w: -1}
  - {x: -1, y: -3.2092733e-14, z: -5.283751e-28, w: -1}
  - {x: -1, y: -3.2092733e-14, z: -5.283751e-28, w: -1}
  m_Colors: []
  m_UnwrapParameters:
    m_HardAngle: 88
    m_PackMargin: 30
    m_AngleError: 8
    m_AreaError: 15
  m_PreserveMeshAssetOnDestroy: 0
  assetGuid: 
  m_Mesh: {fileID: 0}
  m_VersionIndex: 2310
  m_IsSelectable: 1
  m_SelectedFaces: 
  m_SelectedEdges: []
  m_SelectedVertices: 
--- !u!114 &3013175145938003827
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146714154164167344}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ca002da428252441b92f28d83c8a65f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Shape:
    rid: 6924966527306563585
  m_Size: {x: 0.7424325, y: 0.5995691, z: -0.72643423}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_PivotLocation: 1
  m_PivotPosition: {x: 0, y: 0, z: 0}
  m_UnmodifiedMeshVersion: 1878
  m_ShapeBox:
    m_Center: {x: 0.37121624, y: 0.29978454, z: -0.36321712}
    m_Extent: {x: 0.72643423, y: 0.29978454, z: 0.72643423}
  references:
    version: 2
    RefIds:
    - rid: 6924966527306563585
      type: {class: Cone, ns: UnityEngine.ProBuilder.Shapes, asm: Unity.ProBuilder}
      data:
        m_NumberOfSides: 8
        m_Smooth: 1
--- !u!23 &1875049680233279501
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146714154164167344}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &4642007644482903578
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146714154164167344}
  m_Mesh: {fileID: 0}
--- !u!64 &45998206174343390
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146714154164167344}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 0}
--- !u!1 &978119271130584117
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4995335957955370961}
  - component: {fileID: 4715469252365893674}
  - component: {fileID: 8860591349044000954}
  m_Layer: 0
  m_Name: Shaelphoth Minion UFO Model (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4995335957955370961
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 978119271130584117}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12286119, y: 0.97614914, z: 0.17837141, w: 0.014887193}
  m_LocalPosition: {x: 0.985, y: 1.929, z: 0.208}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 3284009611533657198}
  m_LocalEulerAnglesHint: {x: -20.156, y: 175.547, z: 15.139}
--- !u!33 &4715469252365893674
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 978119271130584117}
  m_Mesh: {fileID: 2815373606932172023, guid: 4db720aaae3843f4d8641e39adfb8869, type: 3}
--- !u!23 &8860591349044000954
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 978119271130584117}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1664280353854774104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4708919668035226744}
  - component: {fileID: 7740637272994657464}
  - component: {fileID: 407275202390742691}
  m_Layer: 0
  m_Name: Shaelphoth Minion UFO Model (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4708919668035226744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1664280353854774104}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12286119, y: 0.97614914, z: 0.17837141, w: 0.014887193}
  m_LocalPosition: {x: 1.197, y: 1.284, z: -0.24399999}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 3284009611533657198}
  m_LocalEulerAnglesHint: {x: -20.156, y: 175.547, z: 15.139}
--- !u!33 &7740637272994657464
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1664280353854774104}
  m_Mesh: {fileID: 2815373606932172023, guid: 4db720aaae3843f4d8641e39adfb8869, type: 3}
--- !u!23 &407275202390742691
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1664280353854774104}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1909643987883374324
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4382263521070185680}
  - component: {fileID: 6650540857389212359}
  - component: {fileID: 4856881382046925730}
  m_Layer: 0
  m_Name: Shaelphoth Canon Base Model (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4382263521070185680
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1909643987883374324}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: 1, w: 0}
  m_LocalPosition: {x: 17.5, y: -19.516022, z: -4.262451}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 7969674488375699034}
  m_Father: {fileID: 4355395159183311423}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 180}
--- !u!23 &6650540857389212359
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1909643987883374324}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &4856881382046925730
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1909643987883374324}
  m_Mesh: {fileID: 1649961481857560365, guid: 8034a7f7cf5417f42abdffbffbd5315f, type: 3}
--- !u!1 &2130522973526567815
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7232895247495143560}
  - component: {fileID: 6922119184806560047}
  - component: {fileID: 6374782045615367832}
  m_Layer: 0
  m_Name: Shaelphoth Minion UFO Model (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7232895247495143560
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2130522973526567815}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12286119, y: 0.97614914, z: 0.17837141, w: 0.014887193}
  m_LocalPosition: {x: -1.4230001, y: 1.656, z: -0.07999998}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 3284009611533657198}
  m_LocalEulerAnglesHint: {x: -20.156, y: 175.547, z: 15.139}
--- !u!33 &6922119184806560047
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2130522973526567815}
  m_Mesh: {fileID: 2815373606932172023, guid: 4db720aaae3843f4d8641e39adfb8869, type: 3}
--- !u!23 &6374782045615367832
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2130522973526567815}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3028689515938660305
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6374810597452692862}
  - component: {fileID: 8753368767860840909}
  - component: {fileID: 4272568458125936570}
  m_Layer: 11
  m_Name: Trophy Stand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6374810597452692862
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3028689515938660305}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: -0.10800001, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3284009611533657198}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8753368767860840909
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3028689515938660305}
  m_Mesh: {fileID: -3433870947350836848, guid: 0cb7acfee1616044fa3d50c514e1d4da, type: 3}
--- !u!23 &4272568458125936570
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3028689515938660305}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3100241545166819739
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2838872345004412659}
  - component: {fileID: 5737160627448455696}
  - component: {fileID: 2925548024778980635}
  m_Layer: 0
  m_Name: Shaelphoth Canon Pipe Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2838872345004412659
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3100241545166819739}
  serializedVersion: 2
  m_LocalRotation: {x: 0.70710677, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0.916}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 1971350213114532333}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!23 &5737160627448455696
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3100241545166819739}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2925548024778980635
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3100241545166819739}
  m_Mesh: {fileID: -7215661979320751396, guid: 164fb9ae1ffee9b42a5860a9b0d9be9c, type: 3}
--- !u!1 &3189571052020782044
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1834773306910201039}
  - component: {fileID: 3476059175664868111}
  - component: {fileID: 1514743150011230877}
  m_Layer: 0
  m_Name: Shaelphoth Canon Base Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1834773306910201039
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3189571052020782044}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -11.312256, y: -9.264954, z: 11.102295}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 2455599487410235556}
  m_Father: {fileID: 4355395159183311423}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &3476059175664868111
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3189571052020782044}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1514743150011230877
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3189571052020782044}
  m_Mesh: {fileID: 1649961481857560365, guid: 8034a7f7cf5417f42abdffbffbd5315f, type: 3}
--- !u!1 &3975757478612743683
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 163056402486156661}
  - component: {fileID: 8549800092708777371}
  - component: {fileID: 3030391581138586839}
  m_Layer: 0
  m_Name: Shaelphoth Minion UFO Model (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &163056402486156661
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3975757478612743683}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12286119, y: 0.97614914, z: 0.17837141, w: 0.014887193}
  m_LocalPosition: {x: -0.821, y: 2.183, z: 0.16100001}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 3284009611533657198}
  m_LocalEulerAnglesHint: {x: -20.156, y: 175.547, z: 15.139}
--- !u!33 &8549800092708777371
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3975757478612743683}
  m_Mesh: {fileID: 2815373606932172023, guid: 4db720aaae3843f4d8641e39adfb8869, type: 3}
--- !u!23 &3030391581138586839
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3975757478612743683}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3996626953220208241
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1212412742884588435}
  - component: {fileID: 5568115536056327923}
  - component: {fileID: 4068578649152625735}
  m_Layer: 0
  m_Name: Shaelphoth Minion UFO Model (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1212412742884588435
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3996626953220208241}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12286119, y: 0.97614914, z: 0.17837141, w: 0.014887193}
  m_LocalPosition: {x: -1.3870001, y: 0.65800005, z: -0.444}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 3284009611533657198}
  m_LocalEulerAnglesHint: {x: -20.156, y: 175.547, z: 15.139}
--- !u!33 &5568115536056327923
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3996626953220208241}
  m_Mesh: {fileID: 2815373606932172023, guid: 4db720aaae3843f4d8641e39adfb8869, type: 3}
--- !u!23 &4068578649152625735
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3996626953220208241}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4318462321230435981
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2017624014301810680}
  - component: {fileID: 3418974509738091886}
  - component: {fileID: 2396867540834174859}
  m_Layer: 0
  m_Name: Shaelphoth Canon Pipe Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2017624014301810680
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318462321230435981}
  serializedVersion: 2
  m_LocalRotation: {x: 0.70710677, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0.916}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5885797219941966659}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!23 &3418974509738091886
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318462321230435981}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2396867540834174859
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318462321230435981}
  m_Mesh: {fileID: -7215661979320751396, guid: 164fb9ae1ffee9b42a5860a9b0d9be9c, type: 3}
--- !u!1 &4509098281146136638
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4606734995738890738}
  - component: {fileID: 1267256494753395413}
  - component: {fileID: 1549069070012750400}
  m_Layer: 11
  m_Name: Sentry Guard Boss Hat
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4606734995738890738
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4509098281146136638}
  serializedVersion: 2
  m_LocalRotation: {x: -0.071661405, y: -0, z: -0, w: 0.997429}
  m_LocalPosition: {x: 0, y: 0.802, z: -0.138}
  m_LocalScale: {x: 0.35010943, y: 0.35010943, z: 0.35010943}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 9065666877118612199}
  m_LocalEulerAnglesHint: {x: -8.219, y: 0, z: 0}
--- !u!23 &1267256494753395413
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4509098281146136638}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1549069070012750400
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4509098281146136638}
  m_Mesh: {fileID: -8626022017946226100, guid: 770cca227af0f4249b639b57825d0028, type: 3}
--- !u!1 &4683843630307822370
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4355395159183311423}
  - component: {fileID: 326510803230004273}
  - component: {fileID: 5681928853224809966}
  m_Layer: 0
  m_Name: Shaelphoth UFO Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4355395159183311423
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4683843630307822370}
  serializedVersion: 2
  m_LocalRotation: {x: 0.06345459, y: 0.9939013, z: 0.062039383, w: 0.06545843}
  m_LocalPosition: {x: 0.06300001, y: 1.7659999, z: -0.11899999}
  m_LocalScale: {x: 0.05, y: 0.05, z: 0.05}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1834773306910201039}
  - {fileID: 5885797219941966659}
  - {fileID: 1971350213114532333}
  - {fileID: 4382263521070185680}
  m_Father: {fileID: 3284009611533657198}
  m_LocalEulerAnglesHint: {x: -6.604, y: 172.015, z: 7.768}
--- !u!23 &326510803230004273
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4683843630307822370}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &5681928853224809966
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4683843630307822370}
  m_Mesh: {fileID: 8335794586419630984, guid: 2c6a565b9fa74ec4e87a9ac95e6faa88, type: 3}
--- !u!1 &5588342061384931426
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2455599487410235556}
  - component: {fileID: 1630979618063026156}
  - component: {fileID: 4669965386550558387}
  m_Layer: 0
  m_Name: Shaelphoth Canon Pipe Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2455599487410235556
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5588342061384931426}
  serializedVersion: 2
  m_LocalRotation: {x: 0.70710677, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0.916}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 1834773306910201039}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!23 &1630979618063026156
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5588342061384931426}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &4669965386550558387
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5588342061384931426}
  m_Mesh: {fileID: -7215661979320751396, guid: 164fb9ae1ffee9b42a5860a9b0d9be9c, type: 3}
--- !u!1 &5740480116517967294
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5885797219941966659}
  - component: {fileID: 4569683680740095918}
  - component: {fileID: 831462329380314588}
  m_Layer: 0
  m_Name: Shaelphoth Canon Base Model (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5885797219941966659
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5740480116517967294}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 11.312256, y: -9.264954, z: 11.102295}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 2017624014301810680}
  m_Father: {fileID: 4355395159183311423}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &4569683680740095918
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5740480116517967294}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &831462329380314588
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5740480116517967294}
  m_Mesh: {fileID: 1649961481857560365, guid: 8034a7f7cf5417f42abdffbffbd5315f, type: 3}
--- !u!1 &5975265954045250042
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7969674488375699034}
  - component: {fileID: 7401786642365349128}
  - component: {fileID: 42676054893078875}
  m_Layer: 0
  m_Name: Shaelphoth Canon Pipe Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7969674488375699034
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5975265954045250042}
  serializedVersion: 2
  m_LocalRotation: {x: 0.70710677, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0.916}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 4382263521070185680}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!23 &7401786642365349128
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5975265954045250042}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &42676054893078875
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5975265954045250042}
  m_Mesh: {fileID: -7215661979320751396, guid: 164fb9ae1ffee9b42a5860a9b0d9be9c, type: 3}
--- !u!1 &6115373879110263227
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4276504299030680973}
  - component: {fileID: 3259822873536829316}
  - component: {fileID: 2729452187206780033}
  m_Layer: 0
  m_Name: Shaelphoth Minion UFO Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4276504299030680973
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6115373879110263227}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12286119, y: 0.97614914, z: 0.17837141, w: 0.014887193}
  m_LocalPosition: {x: 0.947, y: 0.788, z: -0.985}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 3284009611533657198}
  m_LocalEulerAnglesHint: {x: -20.156, y: 175.547, z: 15.139}
--- !u!33 &3259822873536829316
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6115373879110263227}
  m_Mesh: {fileID: 2815373606932172023, guid: 4db720aaae3843f4d8641e39adfb8869, type: 3}
--- !u!23 &2729452187206780033
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6115373879110263227}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6298194059911875343
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9065666877118612199}
  - component: {fileID: 5499667810465532298}
  - component: {fileID: 3567686662151302640}
  m_Layer: 11
  m_Name: Tower of Distortion
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 122
  m_IsActive: 1
--- !u!4 &9065666877118612199
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6298194059911875343}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.79, y: 0.488, z: -0.48}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 4606734995738890738}
  - {fileID: 2043496670128245044}
  m_Father: {fileID: 3284009611533657198}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5499667810465532298
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6298194059911875343}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3567686662151302640
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6298194059911875343}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8305938029817635634
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1971350213114532333}
  - component: {fileID: 3130891557515437735}
  - component: {fileID: 5567430445388076610}
  m_Layer: 0
  m_Name: Shaelphoth Canon Base Model (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1971350213114532333
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8305938029817635634}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: 1, w: 0}
  m_LocalPosition: {x: -17.5, y: -19.516022, z: -4.262451}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 2838872345004412659}
  m_Father: {fileID: 4355395159183311423}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 180}
--- !u!23 &3130891557515437735
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8305938029817635634}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  - {fileID: 2100000, guid: b7285a3c1ba665845b3e63b13d305152, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &5567430445388076610
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8305938029817635634}
  m_Mesh: {fileID: 1649961481857560365, guid: 8034a7f7cf5417f42abdffbffbd5315f, type: 3}
--- !u!1 &8708047276893499714
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3284009611533657198}
  - component: {fileID: 3427286239059398524}
  - component: {fileID: 3617915943668413597}
  - component: {fileID: 8644774111844698732}
  m_Layer: 11
  m_Name: Trophy 5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3284009611533657198
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8708047276893499714}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 6374810597452692862}
  - {fileID: 4276504299030680973}
  - {fileID: 4708919668035226744}
  - {fileID: 4995335957955370961}
  - {fileID: 163056402486156661}
  - {fileID: 7232895247495143560}
  - {fileID: 1212412742884588435}
  - {fileID: 4355395159183311423}
  - {fileID: 9065666877118612199}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &3427286239059398524
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8708047276893499714}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 5
  m_AngularDrag: 5
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &3617915943668413597
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8708047276893499714}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1804438810, guid: 661092b4961be7145bfbe56e1e62337b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MomentumTransferMethod: 0
  DisallowTheft: 0
  ExactGun: {fileID: 0}
  ExactGrip: {fileID: 0}
  allowManipulationWhenEquipped: 0
  orientation: 0
  AutoHold: 0
  InteractionText: Shaelphoth Strikes! Trophy!
  UseText: 
  useEventBroadcastType: 0
  UseDownEventName: 
  UseUpEventName: 
  pickupDropEventBroadcastType: 0
  PickupEventName: 
  DropEventName: 
  ThrowVelocityBoostMinSpeed: 1
  ThrowVelocityBoostScale: 1
  currentlyHeldBy: {fileID: 0}
  pickupable: 1
  proximity: 2
--- !u!65 &8644774111844698732
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8708047276893499714}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 2.98, y: 0.05, z: 2.98}
  m_Center: {x: 0, y: 0, z: 0}
