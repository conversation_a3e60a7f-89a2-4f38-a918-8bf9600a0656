﻿
using System;
using System.Linq;
using UdonSharp;
using UnityEngine;
using VRC.SDK3.Data;
using VRC.SDKBase;
using VRC.Udon;

namespace OnlinePhotoPinboard
{

    [DefaultExecutionOrder(-1)]
    [UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
    public class PhotoSectionSerializer : UdonSharpBehaviour
    {
        public PhotoSerializer photoSerializer;
        public int photoSectionIndex;

        [UdonSynced] public byte[] imageSyncedByteData;



        void Start()
        {
            photoSectionIndex = transform.GetSiblingIndex();
        }

        public void SetSyncedImageByteData(byte[] newImageByteData)
        {
            //Debug.Log($"Setting imagebytedata: {newImageByteData == imageSyncedByteData}");
            if (AreByteArraysEqual(newImageByteData, imageSyncedByteData)) return;
            //Debug.Log($"Setting image byte data! for {photoSectionIndex}");
            SetProgramVariable(nameof(imageSyncedByteData), newImageByteData);
            RequestSerialization();

            UpdateImage(imageSyncedByteData);
        }

        public void UpdateImage(byte[] imageByteData)
        {
            bool noData = (imageByteData.Length <= 0);
            if (noData) return;
            //Debug.Log($"Updating {name} {photoSectionIndex}");
            photoSerializer.UpdateImageAtIndex(imageByteData, photoSectionIndex);
        }


        public override void OnDeserialization()
        {
            UpdateImage(imageSyncedByteData);
        }


        bool AreByteArraysEqual(byte[] array1, byte[] array2)
        {
            if (array1 == array2)
                return true;

            if (array1 == null || array2 == null || array1.Length != array2.Length)
                return false;

            for (int i = 0; i < array1.Length; i++)
            {
                if (array1[i] != array2[i])
                    return false;
            }
            return true;
        }
    }
}