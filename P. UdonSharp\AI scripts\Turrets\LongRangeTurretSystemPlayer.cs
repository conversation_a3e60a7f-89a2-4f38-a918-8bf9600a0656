using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class LongRangeTurretSystemPlayer : UdonSharpBehaviour
{
    //Sight
    public float SightRange = 10f;
    public LayerMask WhatIsEnemy;
    public GameObject closestEnemy;
    //Bullet
    public float bulletSpeed = 10;
    public GameObject bullet;
    public Transform bulletSpawnPoint;
    public int BulletDelay,CanFireAgain = 3;

    private VRCPlayerApi localPlayer; 

    public bool CanShoot; 

    void Start()
    {
        localPlayer = Networking.LocalPlayer;
    
        //Timer for update
        SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.2f);
    }

    void OnEnable(){CanShoot = true;}
    void OnDisable(){CanShoot = false;}


    public void UpdateCustom()
    {
        //Find enemieswithinrange
        Collider[] enemiesInRange = Physics.OverlapSphere(transform.position, SightRange, WhatIsEnemy);
        float closestDistance = Mathf.Infinity;

        foreach (Collider enemy in enemiesInRange)
        {
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestEnemy = enemy.gameObject;
            }
        }

        //Timer Stuff
        if (enemiesInRange.Length == 0)
        {
            bullet.SetActive(false);
            closestEnemy = null;
            bullet.transform.parent = gameObject.transform;
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 1f);
            return;
        }

        if (closestEnemy != null)
        {
            TargetAndFire();
            SendCustomEventDelayedSeconds(nameof(UpdateCustom), 0.05f);
        }
    }
    void TargetAndFire()
    {
        if(CanShoot == true){
            BulletDelay++;

            if(BulletDelay > CanFireAgain){
                //Bullet Shoot
                bullet.SetActive(false);
                bullet.SetActive(true);
                bullet.transform.parent = null;
                bullet.transform.position = bulletSpawnPoint.position;
                bullet.transform.rotation = bulletSpawnPoint.rotation;
                BulletDelay = 0;
            }
        }
        else{
            bullet.SetActive(false);
            bullet.transform.parent = transform;
        }
    }
}