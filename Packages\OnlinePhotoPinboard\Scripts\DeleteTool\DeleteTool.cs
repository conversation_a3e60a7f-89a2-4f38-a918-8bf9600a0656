﻿
using UdonSharp;
using UnityEngine;
using VRC.SDK3.Components;
using VRC.SDKBase;
using VRC.Udon;


namespace OnlinePhotoPinboard
{
    public class DeleteTool : UdonSharpBehaviour
    {
        public ImageDeleter imageDeleter;

        public Transform raycastStartPoint;
        public LayerMask raycastTargetLayers;

        public Transform imageContainer;
        public Animator highlightedImageAnimator;

        public LineRenderer lineRenderer;

        public float rayVisualLength = 0.3f;

        //private float deleteHoldTimeThreshold = 0.4f;
        //private float triggerDownTimer;
        private bool triggerDown;
        VRCPickup pickup;
        void Start()
        {
            pickup = GetComponent<VRCPickup>();
            lineRenderer.enabled = false;
        }

        private void Update()
        {

            if(!pickup.IsHeld)
            {
                return;
            }
            lineRenderer.SetPosition(0, raycastStartPoint.position);
            lineRenderer.SetPosition(1, raycastStartPoint.position + raycastStartPoint.forward * rayVisualLength);
            if (!triggerDown)
            {
                TryToPlayHighlightImageAnimation();
                return;
            }
            //triggerDownTimer -= Time.deltaTime;
            //if (triggerDownTimer >= 0) return;




            triggerDown = false;


        }

        public void TryToPlayHighlightImageAnimation()
        {
            if (GetRaycastedImage(out Transform imageTransform))
            {
                if (highlightedImageAnimator != null && imageTransform == highlightedImageAnimator.transform) return;

                PlayImageHighlightAnimation(false, highlightedImageAnimator);
                Animator animator = imageTransform.GetComponent<Animator>();
                highlightedImageAnimator = animator;
                PlayImageHighlightAnimation(true, animator);
            }
            else
            {
                PlayImageHighlightAnimation(false, highlightedImageAnimator);
                highlightedImageAnimator = null;
            }
        }


        public bool GetRaycastedImage(out Transform imageTransform)
        {
            imageTransform = null;
            Ray ray = new Ray(raycastStartPoint.position, raycastStartPoint.forward);
            if (Physics.Raycast(ray, out RaycastHit hit, 5, raycastTargetLayers))
            {
                if (hit.transform == null) return false;
                if (hit.transform.transform.parent != imageContainer) return false;
                imageTransform = hit.transform;

                return true;
            }
            return false;
        }


        public override void OnPickupUseDown()
        {
            if (GetRaycastedImage(out Transform imageTransform))
            {
                Animator animator = imageTransform.GetComponent<Animator>();
                PlayImageHighlightAnimation(true, animator);




                //triggerDownTimer = deleteHoldTimeThreshold;
                //Debug.Log($"deleting {imageTransform.name} using index {imageTransform.GetSiblingIndex()}");
                imageDeleter.DeleteImageAtIndex(imageTransform.GetSiblingIndex());

                triggerDown = true;
                //imageTransform.gameObject.SetActive(false);
            }
        }

        public override void OnPickupUseUp()
        {
            triggerDown = false;
        }

        public override void OnPickup()
        {
            lineRenderer.enabled = true;
        }

        public override void OnDrop()
        {
            PlayImageHighlightAnimation(false, highlightedImageAnimator);
            lineRenderer.enabled = false;
        }



        public void PlayImageHighlightAnimation(bool value, Animator animator)
        {
            if (animator == null)
            {
                //Debug.LogWarning("[Delete Tool] No selected image");
                return;
            }
            animator.SetBool("Selected", value);
        }

        private void OnDrawGizmos()
        {
            Ray ray = new Ray(raycastStartPoint.position, raycastStartPoint.forward);
            Gizmos.DrawRay(ray);
        }
    }
}
