; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 5
		Day: 12
		Hour: 22
		Minute: 33
		Second: 1
		Millisecond: 85
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\mcn08slr_C02 Axe.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\mcn08slr_C02 Axe.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2383606744720, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2388697396288, "Geometry::Scene", "Mesh" {
		Vertices: *153 {
			a: 9.39525216817856,-45.2114194631577,4.04626727104187,3.63789722323418,-45.2114194631577,4.04626727104187,11.5165740251541,26.8135696649551,6.16758801043034,1.51657462120056,26.8135696649551,6.16758801043034,3.63789722323418,-45.2114194631577,-1.71108841896057,1.51657462120056,26.8135696649551,-3.83241176605225,9.39525216817856,-45.2114194631577,-1.71108841896057,11.5165740251541,26.8135696649551,-3.83241176605225,11.5165740251541,36.8135720491409,6.16759061813354,1.51657462120056,36.8135720491409,6.16759061813354,11.5165740251541,36.8135720491409,-3.83241176605225,1.51657462120056,36.8135720491409,-3.83241176605225,12.931989133358,-58.3849549293518,-5.24782538414001,0.101161003112793,-58.3849549293518,-5.24782538414001,12.931989133358,-58.3849549293518,7.58300423622131,0.101161003112793,-58.3849549293518,7.58300423622131,24.0629494190216,26.8135696649551,-3.83241176605225,24.0629494190216,26.8135696649551,6.16759061813354,24.0629494190216,36.8135720491409,-3.83241176605225,24.0629494190216,36.8135720491409,6.16759061813354,-24.0629494190216,5.2421897649765,1.16758942604065,-24.0629494190216,58.3849549293518,1.16758942604065,9.39525216817856,-50.9687781333923,-1.71108841896057,3.63789722323418,-50.9687781333923,-1.71108841896057,9.39525216817856,-50.9687781333923,4.04626727104187,3.63789722323418,-50.9687781333923,4.04626727104187,2.66817063093185,-27.0326524972916,-1.19513273239136,9.09488871693611,-27.718386054039,4.56915497779846,19.3917840719223,-25.6083279848099,-13.1656050682068,6.08940236270428,-29.8116534948349,1.16543769836426,6.5051481127739,-34.6839278936386,0.122296810150146,4.17264699935913,-11.3936573266983,-0.409579277038574,5.53407333791256,-17.7618771791458,5.29938936233521,1.67244225740433,-1.67351067066193,17.5978541374207,7.22278952598572,-13.8279765844345,2.71623730659485,11.9616463780403,-12.3284012079239,3.25890183448792,-8.28205347061157,25.3355234861374,-0.729209184646606,-15.6802356243134,20.8617180585861,-0.227159261703491,-20.8303332328796,36.3467365503311,-12.8244161605835,
-12.8077372908592,24.6636688709259,1.28742456436157,-14.4609272480011,27.793762087822,4.81864213943481,-14.7669047117233,42.8827077150345,-2.60380506515503,-21.086859703064,38.5244160890579,1.40412449836731,-13.7660905718803,50.4411816596985,16.5499925613403,-16.2459820508957,39.0760272741318,0.280892848968506,-12.8841802477837,35.8209520578384,2.04236507415771,7.7978752553463,5.91016709804535,-3.87295484542847,2.24213749170303,11.1470133066177,0.214731693267822,5.2492082118988,21.0802495479584,-17.5978541374207,6.92627802491188,9.95342433452606,-1.06361508369446,10.7388198375702,12.8030866384506,0.467377901077271
		} 
		PolygonVertexIndex: *234 {
			a: 3,20,-6,11,21,-10,28,26,-28,26,29,-28,28,27,-31,27,29,-31,28,30,-27,30,29,-27,33,31,-33,31,34,-33,33,32,-36,32,34,-36,33,35,-32,35,34,-32,38,36,-38,36,39,-38,38,37,-41,37,39,-41,38,40,-37,40,39,-37,43,41,-43,41,44,-43,43,42,-46,42,44,-46,43,45,-42,45,44,-42,48,46,-48,46,49,-48,48,47,-51,47,49,-51,48,50,-47,50,49,-47,0,3,-2,3,0,-3,1,5,-5,5,1,-4,4,7,-7,7,4,-6,6,2,-1,2,6,-8,8,11,-10,11,8,-11,12,15,-14,15,12,-15,2,9,-4,9,2,-9,16,19,-18,19,16,-19,5,10,-8,10,5,-12,9,20,-4,20,9,-22,5,21,-12,21,5,-21,7,17,-3,17,7,-17,10,16,-8,16,10,-19,2,19,-9,19,2,-18,8,18,-11,18,8,-20,6,23,-5,23,6,-23,0,22,-7,22,0,-25,4,25,-2,25,4,-24,1,24,-1,24,1,-26,22,13,-24,13,22,-13,24,12,-23,12,24,-15,23,15,-26,15,23,-14,25,14,-25,14,25,-16
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *702 {
				a: 0.644672870635986,-0.764458477497101,0,0.644672870635986,-0.764458477497101,0,0.644672870635986,-0.764458477497101,0,0.644672989845276,0.764458537101746,0,0.644672989845276,0.764458537101746,0,0.644672989845276,0.764458537101746,0,0.665118277072906,0.210168197751045,-0.716552197933197,-0.502501726150513,0.590178608894348,-0.631807863712311,0.744464874267578,0.457126438617706,0.48662856221199,-0.665118813514709,-0.210166811943054,0.716552138328552,-0.665118813514709,-0.210166811943054,0.716552138328552,-0.665118813514709,-0.210166811943054,0.716552138328552,0.665118277072906,0.210168197751045,-0.716552197933197,0.744464874267578,0.457126438617706,0.48662856221199,0.241981759667397,-0.894386291503906,-0.376188844442368,-0.665119349956512,-0.210165739059448,0.716551959514618,-0.665119349956512,-0.210165739059448,0.716551959514618,-0.665119349956512,-0.210165739059448,0.716551959514618,0.665118277072906,0.210168197751045,-0.716552197933197,0.241981759667397,-0.894386291503906,-0.376188844442368,-0.502501726150513,0.590178608894348,-0.631807863712311,-0.665118455886841,-0.210165828466415,0.716552734375,-0.665118455886841,-0.210165828466415,0.716552734375,-0.665118455886841,-0.210165828466415,0.716552734375,-0.277516305446625,0.60772293806076,0.744081735610962,-0.659122049808502,0.619722008705139,-0.426031202077866,-0.394967019557953,-0.615893959999084,0.681671321392059,0.277517646551132,-0.607723236083984,-0.74408096075058,0.277517646551132,-0.607723236083984,-0.74408096075058,0.277517646551132,-0.607723236083984,-0.74408096075058,-0.277516305446625,0.60772293806076,0.744081735610962,-0.394967019557953,-0.615893959999084,0.681671321392059,0.852165758609772,0.438355535268784,0.285758525133133,0.277516961097717,-0.60772317647934,-0.744081199169159,0.277516961097717,-0.60772317647934,-0.744081199169159,0.277516961097717,-0.60772317647934,-0.744081199169159,-0.277516305446625,0.60772293806076,0.744081735610962,0.852165758609772,0.438355535268784,0.285758525133133,-0.659122049808502,0.619722008705139,-0.426031202077866,
0.277517080307007,-0.607723653316498,-0.744080781936646,0.277517080307007,-0.607723653316498,-0.744080781936646,0.277517080307007,-0.607723653316498,-0.744080781936646,-0.401129812002182,0.584153532981873,-0.70559161901474,0.780823111534119,0.272037029266357,-0.562415361404419,-0.654634118080139,-0.596008241176605,-0.465003490447998,0.401129752397537,-0.584153413772583,0.705591797828674,0.401129752397537,-0.584153413772583,0.705591797828674,0.401129752397537,-0.584153413772583,0.705591797828674,-0.401129812002182,0.584153532981873,-0.70559161901474,-0.654634118080139,-0.596008241176605,-0.465003490447998,-0.418053805828094,0.749005377292633,0.514025330543518,0.401129722595215,-0.584153413772583,0.705591797828674,0.401129722595215,-0.584153413772583,0.705591797828674,0.401129722595215,-0.584153413772583,0.705591797828674,-0.401129812002182,0.584153532981873,-0.70559161901474,-0.418053805828094,0.749005377292633,0.514025330543518,0.780823111534119,0.272037029266357,-0.562415361404419,0.401129722595215,-0.584153354167938,0.705591797828674,0.401129722595215,-0.584153354167938,0.705591797828674,0.401129722595215,-0.584153354167938,0.705591797828674,0.123995818197727,0.568257570266724,0.813454568386078,0.317055970430374,0.87642765045166,-0.362422615289688,-0.909194827079773,0.0307951495051384,0.415230542421341,-0.123994283378124,-0.568257451057434,-0.813454926013947,-0.123994283378124,-0.568257451057434,-0.813454926013947,-0.123994283378124,-0.568257451057434,-0.813454926013947,0.123995818197727,0.568257570266724,0.813454568386078,-0.909194827079773,0.0307951495051384,0.415230542421341,0.68235832452774,-0.493754655122757,0.539067149162292,-0.123994193971157,-0.568257808685303,-0.813454687595367,-0.123994193971157,-0.568257808685303,-0.813454687595367,-0.123994193971157,-0.568257808685303,-0.813454687595367,0.123995818197727,0.568257570266724,0.813454568386078,0.68235832452774,-0.493754655122757,0.539067149162292,0.317055970430374,0.87642765045166,-0.362422615289688,-0.123993925750256,-0.568257629871368,-0.813454866409302,
-0.123993925750256,-0.568257629871368,-0.813454866409302,-0.123993925750256,-0.568257629871368,-0.813454866409302,-0.0838536992669106,0.556340575218201,-0.826712608337402,0.14877738058567,-0.649574160575867,-0.745599508285522,-0.929194092750549,0.366523295640945,0.0475287735462189,0.0838537588715553,-0.556340873241425,0.826712369918823,0.0838537588715553,-0.556340873241425,0.826712369918823,0.0838537588715553,-0.556340873241425,0.826712369918823,-0.0838536992669106,0.556340575218201,-0.826712608337402,-0.929194092750549,0.366523295640945,0.0475287735462189,0.719404339790344,0.687848627567291,0.0965492427349091,0.0838534981012344,-0.556341469287872,0.826712012290955,0.0838534981012344,-0.556341469287872,0.826712012290955,0.0838534981012344,-0.556341469287872,0.826712012290955,-0.0838536992669106,0.556340575218201,-0.826712608337402,0.719404339790344,0.687848627567291,0.0965492427349091,0.14877738058567,-0.649574160575867,-0.745599508285522,0.0838530361652374,-0.556341052055359,0.826712369918823,0.0838530361652374,-0.556341052055359,0.826712369918823,0.0838530361652374,-0.556341052055359,0.826712369918823,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0.999566555023193,-0.0294398255646229,0,-0.999566555023193,-0.0294398255646229,0,-0.999566555023193,-0.0294398255646229,0,-0.999566555023193,-0.0294398255646229,0,-0.999566555023193,-0.0294398255646229,0,-0.999566555023193,-0.0294398255646229,0,-0,-0.0294398367404938,-0.999566555023193,-0,-0.0294398348778486,-0.999566555023193,-0,-0.0294398367404938,-0.999566555023193,-0,-0.0294398348778486,-0.999566555023193,-0,-0.0294398367404938,-0.999566555023193,-0,-0.0294398367404938,-0.999566555023193,0.999566555023193,-0.0294398162513971,0,0.999566555023193,-0.0294398162513971,0,0.999566555023193,-0.0294398162513971,0,0.999566555023193,-0.0294398162513971,0,0.999566555023193,-0.0294398162513971,0,
0.999566555023193,-0.0294398162513971,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-2.60770264048915e-07,1,-0,-2.60770264048915e-07,1,-0,-2.60770264048915e-07,1,-0,-2.60770264048915e-07,1,-0,-2.60770264048915e-07,1,-0,-2.60770264048915e-07,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0.191838562488556,-2.55926835279752e-07,0.981426537036896,-0.191838249564171,0,0.981426537036896,-0.191838413476944,-1.27963417639876e-07,0.981426537036896,-0.191838249564171,0,0.981426537036896,-0.191838562488556,-2.55926835279752e-07,0.981426537036896,-0.191838413476944,-1.27963417639876e-07,0.981426537036896,-0.191838338971138,0,-0.981426537036896,-0.19183836877346,0,-0.981426537036896,-0.191838353872299,0,-0.981426537036896,-0.19183836877346,0,-0.981426537036896,-0.191838338971138,0,-0.981426537036896,-0.191838353872299,0,-0.981426537036896,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-2.07845147315311e-07,-2.60770264048915e-07,1,-0,0,1,-1.03922573657655e-07,-1.30385132024458e-07,1,-0,0,1,-2.07845147315311e-07,-2.60770264048915e-07,1,-1.03922573657655e-07,-1.30385132024458e-07,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0.430451810359955,-0.902613580226898,-0,0.430451840162277,-0.902613520622253,-0,0.430451810359955,-0.902613520622253,-0,0.430451840162277,-0.902613520622253,-0,0.430451810359955,-0.902613580226898,-0,0.430451810359955,-0.902613520622253,0.902613580226898,0.430451840162277,0,0.902613580226898,0.430451810359955,0,0.902613580226898,0.430451810359955,0,0.902613580226898,0.430451810359955,0,0.902613580226898,0.430451840162277,0,0.902613580226898,0.430451810359955,0,-0.902613639831543,0.43045175075531,0,-0.902613580226898,0.430451720952988,0,-0.902613639831543,0.43045175075531,0,-0.902613580226898,0.430451720952988,0,
-0.902613639831543,0.43045175075531,0,-0.902613639831543,0.43045175075531,0,-0,0.430451810359955,0.902613580226898,-0,0.430451840162277,0.902613520622253,-0,0.430451810359955,0.902613520622253,-0,0.430451840162277,0.902613520622253,-0,0.430451810359955,0.902613580226898,-0,0.430451810359955,0.902613520622253
			} 
			NormalsW: *234 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *702 {
				a: -0,-0,0.999999940395355,-0,-0,0.999999940395355,-0,-0,0.999999940395355,0,-0,1.00000011920929,0,-0,1.00000011920929,0,-0,1.00000011920929,0.0831507220864296,-0.974453508853912,-0.208629712462425,0.632085919380188,-0.247810289263725,-0.734205186367035,0.632085919380188,-0.247810170054436,-0.734205186367035,0.299037754535675,0.804310321807861,0.513479709625244,0.299037754535675,0.804310321807861,0.513479709625244,0.299037754535675,0.804310321807861,0.513479709625244,-0.68424779176712,0.555799961090088,-0.472113907337189,0.288896411657333,0.436539471149445,-0.852039933204651,0.28889536857605,0.436540007591248,-0.85204017162323,0.299037545919418,0.804311037063599,0.513478875160217,0.299037545919418,0.804311037063599,0.513478875160217,0.299037545919418,0.804311037063599,0.513478875160217,0.601097345352173,0.418652892112732,0.680743634700775,0.863719642162323,0.375205844640732,-0.33646547794342,0.863719284534454,0.375205516815186,-0.336466491222382,0.299038529396057,0.804310619831085,0.513478815555573,0.299038529396057,0.804310619831085,0.513478815555573,0.299038529396057,0.804310619831085,0.513478815555573,0.947771906852722,0.299915432929993,0.10853199660778,0.175637438893318,0.677690744400024,0.71406352519989,0.175637498497963,0.677690804004669,0.71406364440918,-0.742192268371582,-0.627404391765594,0.23561517894268,-0.742192268371582,-0.627404391765594,0.23561517894268,-0.742192268371582,-0.627404391765594,0.23561517894268,-0.610028743743896,0.486864805221558,-0.625162243843079,-0.521031975746155,0.761296570301056,0.385944813489914,-0.52103191614151,0.761296510696411,0.385944813489914,-0.742192447185516,-0.627403974533081,0.235615342855453,-0.742192447185516,-0.627403974533081,0.235615342855453,-0.742192447185516,-0.627403974533081,0.235615342855453,-0.337743639945984,-0.786780059337616,0.516629934310913,-0.399262547492981,0.191705673933029,0.896570324897766,-0.399262517690659,0.191705644130707,0.896570324897766,-0.742192685604095,-0.627403736114502,0.235615491867065,-0.742192685604095,-0.627403736114502,0.235615491867065,
-0.742192685604095,-0.627403736114502,0.235615491867065,-0.330637395381927,0.626018524169922,0.706243455410004,-0.506646871566772,0.802446663379669,-0.315258800983429,-0.506646990776062,0.802446722984314,-0.315258860588074,-0.140794187784195,-0.800443291664124,-0.582638502120972,-0.140794187784195,-0.800443291664124,-0.582638502120972,-0.140794187784195,-0.800443291664124,-0.582638502120972,0.905136466026306,0.134371012449265,-0.403326660394669,0.0460081063210964,0.582575142383575,-0.811473608016968,0.0460082665085793,0.582575142383575,-0.811473608016968,-0.140793636441231,-0.800443232059479,-0.582638740539551,-0.140793636441231,-0.800443232059479,-0.582638740539551,-0.140793636441231,-0.800443232059479,-0.582638740539551,-0.574499011039734,-0.760389626026154,-0.302916884422302,-0.615705192089081,0.182425886392593,-0.766568958759308,-0.615705072879791,0.182425975799561,-0.766568958759308,-0.140793889760971,-0.800443291664124,-0.582638621330261,-0.140793889760971,-0.800443291664124,-0.582638621330261,-0.140793889760971,-0.800443291664124,-0.582638621330261,0.672359883785248,-0.651015758514404,0.35229355096817,0.41159188747406,0.217122763395309,0.885127067565918,0.411592781543732,0.21712139248848,0.885126948356628,-0.947163462638855,0.312169969081879,-0.0736980736255646,-0.947163462638855,0.312169969081879,-0.0736980736255646,-0.947163462638855,0.312169969081879,-0.0736980736255646,0.295814633369446,0.761336445808411,-0.576940596103668,0.243196308612823,0.84874564409256,0.469559818506241,0.2431960105896,0.848744988441467,0.469561100006104,-0.947164237499237,0.312167942523956,-0.0736967995762825,-0.947164237499237,0.312167942523956,-0.0736967995762825,-0.947164237499237,0.312167942523956,-0.0736967995762825,-0.968175053596497,-0.110320724546909,0.224647149443626,-0.322076916694641,0.458927869796753,0.828040897846222,-0.322076857089996,0.45892795920372,0.828040957450867,-0.947163820266724,0.312168836593628,-0.0736977159976959,-0.947163820266724,0.312168836593628,-0.0736977159976959,-0.947163820266724,0.312168836593628,-0.0736977159976959,
0.762508153915405,0.569933295249939,0.306198179721832,0.266003489494324,0.752488434314728,-0.602497398853302,0.266003102064133,0.752488315105438,-0.602497756481171,-0.981112241744995,-0.191227033734322,-0.0291727352887392,-0.981112241744995,-0.191227033734322,-0.0291727352887392,-0.981112241744995,-0.191227033734322,-0.0291727352887392,0.174319833517075,-0.808651626110077,-0.561867654323578,0.00295731914229691,0.135967090725899,-0.990709006786346,0.00295735662803054,0.135966926813126,-0.990708947181702,-0.98111218214035,-0.191227003931999,-0.0291731804609299,-0.98111218214035,-0.191227003931999,-0.0291731804609299,-0.98111218214035,-0.191227003931999,-0.0291731804609299,-0.936827898025513,0.238718554377556,0.255669623613358,-0.493962973356247,0.60436487197876,-0.625094950199127,-0.493963211774826,0.60436487197876,-0.625094771385193,-0.981112360954285,-0.191226631402969,-0.0291733555495739,-0.981112360954285,-0.191226631402969,-0.0291733555495739,-0.981112360954285,-0.191226631402969,-0.0291733555495739,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0.0294398255646229,0.999566555023193,-0,-0.0294398255646229,0.999566555023193,-0,-0.0294398255646229,0.999566555023193,-0,-0.0294398255646229,0.999566555023193,-0,-0.0294398255646229,0.999566555023193,-0,-0.0294398255646229,0.999566555023193,-0,0,0.999566555023193,-0.0294398367404938,0,0.999566555023193,-0.0294398348778486,0,0.999566555023193,-0.0294398367404938,0,0.999566555023193,-0.0294398348778486,0,0.999566555023193,-0.0294398367404938,0,0.999566555023193,-0.0294398367404938,0.0294398162513971,0.999566555023193,-0,0.0294398162513971,0.999566555023193,-0,0.0294398162513971,0.999566555023193,-0,0.0294398162513971,0.999566555023193,-0,0.0294398162513971,0.999566555023193,-0,0.0294398162513971,0.999566555023193,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,
-0,0,1,-0,0,1,-0,1,2.60770264048915e-07,-0,1,2.60770264048915e-07,-0,1,2.60770264048915e-07,-0,1,2.60770264048915e-07,-0,1,2.60770264048915e-07,-0,1,2.60770264048915e-07,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-4.90966378663416e-08,1,2.51173361220935e-07,-3.33344090108767e-08,0.999999940395355,-6.51583631494645e-09,-4.12155038986839e-08,1,1.22328771112734e-07,-3.33344090108767e-08,0.999999940395355,-6.51583631494645e-09,-4.90966378663416e-08,1,2.51173361220935e-07,-4.12155038986839e-08,1,1.22328771112734e-07,0,1,-0,0,1,-0,0,1.00000011920929,-0,0,1,-0,0,1,-0,0,1.00000011920929,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-5.41998324205591e-14,1,2.60770264048915e-07,0,1,-0,-1.35499581051398e-14,1,1.30385132024458e-07,0,1,-0,-5.41998324205591e-14,1,2.60770264048915e-07,-1.35499581051398e-14,1,1.30385132024458e-07,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,0.902613580226898,0.430451810359955,0,0.902613520622253,0.430451840162277,0,0.902613520622253,0.430451810359955,0,0.902613520622253,0.430451840162277,0,0.902613580226898,0.430451810359955,0,0.902613520622253,0.430451810359955,-0.430451840162277,0.902613580226898,0,-0.430451810359955,0.902613580226898,0,-0.430451810359955,0.902613580226898,0,-0.430451810359955,0.902613580226898,0,-0.430451840162277,0.902613580226898,0,-0.430451810359955,0.902613580226898,0,0.43045175075531,0.902613639831543,-0,0.430451720952988,0.902613580226898,-0,0.43045175075531,0.902613639831543,-0,0.430451720952988,0.902613580226898,-0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0,0.902613580226898,-0.430451810359955,0,0.902613520622253,-0.430451840162277,0,0.902613520622253,-0.430451810359955,0,0.902613520622253,-0.430451840162277,0,0.902613580226898,-0.430451810359955,0,0.902613520622253,-0.430451810359955
			} 
			BinormalsW: *234 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *702 {
				a: 0.764458537101746,0.644672930240631,0,0.764458537101746,0.644672930240631,0,0.764458537101746,0.644672930240631,0,-0.764458477497101,0.644672989845276,0,-0.764458477497101,0.644672989845276,0,-0.764458477497101,0.644672989845276,0,0.742094099521637,-0.0791815966367722,0.665602445602417,0.589880585670471,0.768296420574188,0.248518362641335,0.215033039450645,-0.854181170463562,0.473429173231125,0.684246599674225,-0.555801093578339,0.472114056348801,0.684246599674225,-0.555801093578339,0.472114056348801,0.684246599674225,-0.555801093578339,0.472114056348801,-0.299036294221878,-0.80431079864502,-0.513479828834534,0.601922690868378,-0.774899125099182,-0.192926108837128,-0.926274478435516,-0.0974988788366318,-0.364018738269806,0.684246242046356,-0.55580061674118,0.472115308046341,0.684246242046356,-0.55580061674118,0.472115308046341,0.684246242046356,-0.55580061674118,0.472115308046341,-0.443057298660278,0.883492588996887,-0.152122154831886,-0.442078292369843,0.24350306391716,-0.86329197883606,-0.0384823121130466,0.714779496192932,0.698290288448334,0.68424665927887,-0.555801093578339,0.472114145755768,0.68424665927887,-0.555801093578339,0.472114145755768,0.68424665927887,-0.555801093578339,0.472114145755768,0.157204195857048,-0.735339105129242,0.659214079380035,-0.731238424777985,-0.395828068256378,0.55552738904953,0.901749849319458,-0.401758432388306,0.159491270780563,0.610028445720673,-0.486863762140274,0.62516325712204,0.610028445720673,-0.486863762140274,0.62516325712204,0.610028445720673,-0.486863762140274,0.62516325712204,0.742192566394806,0.627403914928436,-0.235615491867065,0.756654918193817,0.202736914157867,0.621587574481964,0.0483657866716385,0.477778196334839,-0.87714809179306,0.610028445720673,-0.486864238977432,0.625162839889526,0.610028445720673,-0.486864238977432,0.625162839889526,0.610028445720673,-0.486864238977432,0.625162839889526,-0.899396419525146,0.107935629785061,-0.423598825931549,-0.338234812021255,0.878119349479675,-0.338383883237839,-0.637297034263611,-0.761047720909119,-0.121074363589287,
0.610028207302094,-0.486864000558853,0.625163316726685,0.610028207302094,-0.486864000558853,0.625163316726685,0.610028207302094,-0.486864000558853,0.625163316726685,-0.854268014431,-0.516590297222137,0.0579717010259628,-0.365546256303787,-0.531107366085052,-0.764395713806152,-0.561037361621857,-0.0292133279144764,0.827274918556213,-0.905136466026306,-0.134370401501656,0.403326988220215,-0.905136466026306,-0.134370401501656,0.403326988220215,-0.905136466026306,-0.134370401501656,0.403326988220215,0.140793636441231,0.800443053245544,0.58263885974884,-0.7545445561409,0.552612245082855,0.353952348232269,0.907256424427032,0.315590173006058,0.278008162975311,-0.905136525630951,-0.134370878338814,0.403326630592346,-0.905136525630951,-0.134370878338814,0.403326630592346,-0.905136525630951,-0.134370878338814,0.403326630592346,0.71347451210022,-0.283852696418762,-0.640610575675964,0.667935729026794,0.636955142021179,-0.384902596473694,0.105935864150524,-0.944836854934692,-0.309936940670013,-0.905136525630951,-0.134370654821396,0.403326839208603,-0.905136525630951,-0.134370654821396,0.403326839208603,-0.905136525630951,-0.134370654821396,0.403326839208603,-0.729765236377716,-0.503251314163208,0.462796807289124,-0.854439914226532,0.429805099964142,0.291890382766724,0.0628977417945862,-0.975658774375916,0.210080742835999,-0.295815676450729,-0.761336624622345,0.576939940452576,-0.295815676450729,-0.761336624622345,0.576939940452576,-0.295815676450729,-0.761336624622345,0.576939940452576,0.9471635222435,-0.312169998884201,0.0736963748931885,0.33796501159668,-0.527903914451599,0.779164433479309,0.689378440380096,0.189310058951378,-0.699227511882782,-0.295813262462616,-0.761337220668793,0.576940476894379,-0.295813262462616,-0.761337220668793,0.576940476894379,-0.295813262462616,-0.761337220668793,0.576940476894379,-0.217398345470428,0.815421760082245,-0.536493539810181,0.656242072582245,0.738641679286957,-0.154126226902008,-0.892043828964233,0.145807281136513,-0.427782833576202,-0.295814543962479,-0.761336922645569,0.57694011926651,
-0.295814543962479,-0.761336922645569,0.57694011926651,-0.295814543962479,-0.761336922645569,0.57694011926651,-0.641521513462067,0.60469925403595,0.472005248069763,-0.95242178440094,0.108693987131119,-0.284742385149002,0.25659441947937,0.547194540500641,0.796704053878784,-0.174319744110107,0.808651447296143,0.561867952346802,-0.174319744110107,0.808651447296143,0.561867952346802,-0.174319744110107,0.808651447296143,0.561867952346802,0.981112241744995,0.191227078437805,0.0291727595031261,0.369580268859863,0.920420408248901,0.127423748373985,0.694585084915161,-0.713005900382996,-0.0957809910178185,-0.174319908022881,0.808650970458984,0.561868488788605,-0.174319908022881,0.808650970458984,0.561868488788605,-0.174319908022881,0.808650970458984,0.561868488788605,-0.339591026306152,-0.79592627286911,-0.501177966594696,0.488321751356125,-0.40200412273407,-0.774554371833801,-0.856659591197968,-0.461298763751984,0.230949908494949,-0.174319744110107,0.808651387691498,0.561868011951447,-0.174319744110107,0.808651387691498,0.561868011951447,-0.174319744110107,0.808651387691498,0.561868011951447,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.981426477432251,3.81360702633488e-15,0.191838547587395,0.981426537036896,3.39652608261076e-08,0.191838249564171,0.981426537036896,1.69826304130538e-08,0.191838413476944,0.981426537036896,3.39652608261076e-08,0.191838249564171,0.981426477432251,3.81360702633488e-15,0.191838547587395,0.981426537036896,1.69826304130538e-08,0.191838413476944,-0.981426537036896,0,0.191838338971138,-0.981426537036896,0,0.19183836877346,-0.981426596641541,0,0.19183836877346,-0.981426537036896,0,0.19183836877346,-0.981426537036896,0,0.191838338971138,-0.981426596641541,0,0.19183836877346,1,0,0,
1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,2.07845147315311e-07,1,0,0,1,0,1.03922573657655e-07,1,0,0,1,0,2.07845147315311e-07,1,0,1.03922573657655e-07,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			} 
			TangentsW: *234 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *376 {
				a: 0.0939525216817856,-0.450727015733719,0.0363789722323418,-0.450727015733719,0.115165740251541,0.269835203886032,0.0151657462120056,0.269835203886032,0.0404626727104187,-0.452989220619202,-0.0171108841896057,-0.452989220619202,0.0616758801043034,0.267572999000549,-0.0383241176605225,0.267572999000549,-0.0363789722323418,-0.451414495706558,-0.0939525216817856,-0.451414495706558,-0.0151657462120056,0.269147723913193,-0.115165740251541,0.269147723913193,0.0171108841896057,-0.449152290821075,-0.0404626727104187,-0.449152290821075,0.0383241176605225,0.271409928798676,-0.0616758801043034,0.271409928798676,-0.115165740251541,0.0616759061813354,-0.0151657462120056,0.0616759061813354,-0.115165740251541,-0.0383241176605225,-0.0151657462120056,-0.0383241176605225,0.12931989133358,-0.0524782538414001,0.00101161003112793,-0.0524782538414001,0.12931989133358,0.0758300423622131,0.00101161003112793,0.0758300423622131,0.115165740251541,0.268135726451874,0.0151657462120056,0.268135726451874,0.115165740251541,0.368135750293732,0.0151657462120056,0.368135750293732,0.0383241176605225,0.268135696649551,-0.0616759061813354,0.268135696649551,0.0383241176605225,0.368135720491409,-0.0616759061813354,0.368135720491409,-0.0151657462120056,0.268135696649551,-0.115165740251541,0.268135696649551,-0.0151657462120056,0.368135720491409,-0.115165740251541,0.368135720491409,0.184453412890434,0.0616758801043034,0.184453412890434,-0.0383241176605225,-0.150156289339066,0.0116758942604065,0.0267158821225166,0.368135720491409,0.0267158783972263,0.268135696649551,-0.23392029106617,0.583849549293518,-0.23392029106617,0.0524219125509262,-0.0222361013293266,0.268135696649551,-0.0222361013293266,0.368135720491409,0.238400056958199,0.052421897649765,0.238400056958199,0.583849549293518,0.225733578205109,-0.0383241176605225,0.225733578205109,0.0616759061813354,0.560343325138092,0.0116758942604065,0.115165740251541,-0.0383241176605225,0.115165740251541,0.0616758801043034,0.240629494190216,-0.0383241176605225,0.240629494190216,0.0616759061813354,
-0.115165740251541,0.368135720491409,-0.115165740251541,0.268135696649551,-0.240629494190216,0.368135720491409,-0.240629494190216,0.268135696649551,0.115165755152702,0.268135726451874,0.115165755152702,0.368135750293732,0.240629509091377,0.268135726451874,0.240629509091377,0.368135750293732,-0.115165740251541,0.0616759061813354,-0.115165740251541,-0.0383241176605225,-0.240629494190216,0.0616759061813354,-0.240629494190216,-0.0383241176605225,-0.0939525216817856,-0.452114194631577,-0.0363789722323418,-0.452114194631577,-0.0939525216817856,-0.509687781333923,-0.0363789722323418,-0.509687781333923,-0.0404626727104187,-0.452114194631577,0.0171108841896057,-0.452114194631577,-0.0404626727104187,-0.509687781333923,0.0171108841896057,-0.509687781333923,-0.0171108841896057,-0.452114194631577,0.0404626727104187,-0.452114194631577,-0.0171108841896057,-0.509687781333923,0.0404626727104187,-0.509687781333923,0.0363789722323418,-0.452114194631577,0.0939525216817856,-0.452114194631577,0.0363789722323418,-0.509687781333923,0.0939525216817856,-0.509687781333923,-0.0939525216817856,-0.467416524887085,-0.0363789722323418,-0.467416524887085,-0.12931989133358,-0.549579918384552,-0.00101161003112793,-0.549579918384552,-0.0404626727104187,-0.500493168830872,0.0171108841896057,-0.500493168830872,-0.0758300423622131,-0.582656502723694,0.0524782538414001,-0.582656502723694,-0.0171108841896057,-0.44439172744751,0.0404626727104187,-0.44439172744751,-0.0524782538414001,-0.526555061340332,0.0758300423622131,-0.526555061340332,0.0363789722323418,-0.477468341588974,0.0939525216817856,-0.477468341588974,0.00101161003112793,-0.559631705284119,0.12931989133358,-0.559631705284119,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,0.0675515681505203,-0.211390003561974,0.142551571130753,-0.168088719248772,0.117551542818546,-0.211389973759651,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,0.136137545108795,-0.176895618438721,0.136137515306473,-0.263498157262802,0.111137516796589,-0.2201968729496,0,0,0.173205092549324,0,0.086602546274662,
0.206155300140381,0.146971851587296,-0.264649391174316,0.0719718337059021,-0.221348151564598,0.121971800923347,-0.221348121762276,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,-0.156327903270721,-0.177946090698242,-0.0813279151916504,-0.134644821286201,-0.106327913701534,-0.177946090698242,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,-0.0911435559391975,-0.136728048324585,-0.0911435708403587,-0.223330572247505,-0.116143591701984,-0.180029332637787,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,-0.0844317227602005,-0.230789482593536,-0.159431725740433,-0.187488228082657,-0.109431713819504,-0.187488242983818,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,0.031915158033371,-0.123817950487137,0.106915146112442,-0.0805166810750961,0.0819151625037193,-0.123817920684814,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,0.103037655353546,-0.0819642543792725,0.103037625551224,-0.168566793203354,0.0780376344919205,-0.125265538692474,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,0.106229916214943,-0.171201169490814,0.0312299132347107,-0.127899929881096,0.0812298804521561,-0.127899914979935,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,-0.135188654065132,-0.217199802398682,-0.0601886361837387,-0.17389851808548,-0.0851886719465256,-0.217199802398682,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,-0.0695874840021133,-0.175170004367828,-0.0695874094963074,-0.26177254319191,-0.0945874452590942,-0.218471318483353,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,-0.0637868791818619,-0.269276529550552,-0.138786911964417,-0.225975304841995,-0.0887869298458099,-0.22597524523735,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,0.0641350075602531,-0.150301948189735,0.139135032892227,-0.107000663876534,0.114135034382343,-0.150301963090897,0,0,0.173205092549324,0,0.086602546274662,0.206155300140381,0.133624255657196,-0.10865344107151,0.133624270558357,-0.195255979895592,0.108624272048473,-0.151954710483551,0,0,0.173205092549324,
0,0.086602546274662,0.206155300140381,0.137811049818993,-0.199202001094818,0.0628110468387604,-0.155900716781616,0.112811058759689,-0.155900731682777
			} 
			UVIndex: *234 {
				a: 36,38,37,47,49,48,100,98,99,101,103,102,106,104,105,107,109,108,112,110,111,113,115,114,118,116,117,119,121,120,124,122,123,125,127,126,130,128,129,131,133,132,136,134,135,137,139,138,142,140,141,143,145,144,148,146,147,149,151,150,154,152,153,155,157,156,160,158,159,161,163,162,166,164,165,167,169,168,172,170,171,173,175,174,178,176,177,179,181,180,184,182,183,185,187,186,0,3,1,3,0,2,4,7,5,7,4,6,8,11,9,11,8,10,12,15,13,15,12,14,16,19,17,19,16,18,20,23,21,23,20,22,24,27,25,27,24,26,28,31,29,31,28,30,32,35,33,35,32,34,39,42,40,42,39,41,43,46,44,46,43,45,50,53,51,53,50,52,54,57,55,57,54,56,58,61,59,61,58,60,62,65,63,65,62,64,66,69,67,69,66,68,70,73,71,73,70,72,74,77,75,77,74,76,78,81,79,81,78,80,82,85,83,85,82,84,86,89,87,89,86,88,90,93,91,93,90,92,94,97,95,97,94,96
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2383164109968, "Model::C02_Axe", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2385724811312, "Material::Green_Corr", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2390756574416, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Corr green.jpg"
			P: "RelPath", "KString", "XRefUrl", "", "..\Sprites\Corr green.jpg"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Corr green.jpg"
		RelativeFilename: "..\Sprites\Corr green.jpg"
	}
	Texture: 2390756575376, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Corr green.jpg"
		RelativeFilename: "..\Sprites\Corr green.jpg"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::C02_Axe, Model::RootNode
	C: "OO",2383164109968,0
	
	;Material::Green_Corr, Model::C02_Axe
	C: "OO",2385724811312,2383164109968
	
	;Geometry::Scene, Model::C02_Axe
	C: "OO",2388697396288,2383164109968
	
	;Texture::DiffuseColor_Texture, Material::Green_Corr
	C: "OO",2390756575376,2385724811312
	
	;Texture::DiffuseColor_Texture, Material::Green_Corr
	C: "OP",2390756575376,2385724811312, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",2390756574416,2390756575376
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
