; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 6
		Day: 13
		Hour: 4
		Minute: 16
		Second: 6
		Millisecond: 577
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\sw0y9aih_Holo Sword Model.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\sw0y9aih_Holo Sword Model.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2012552749984, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2010873139264, "Geometry::Scene", "Mesh" {
		Vertices: *90 {
			a: 1.97672210633755,-29.9312919378281,-1.99997425079346,1.97672210633755,-32.8018009662628,-1.99999809265137,12.7056121826172,-35.6648057699203,0,1.97672210633755,-29.9312919378281,2.00002193450928,1.97672210633755,-32.801827788353,2.00002193450928,-1.97672210633755,-32.801827788353,2.00002193450928,-1.97672210633755,-32.8018009662628,-1.99999809265137,-12.7056121826172,-35.6648057699203,0,-1.97672210633755,-29.9312919378281,2.00002193450928,-1.97672210633755,-29.9312919378281,-1.99997425079346,-1.99999809265137,-48.5096544027328,-2.00002193450928,-1.99999809265137,-48.5096544027328,1.99997425079346,-7.76841640472412,-44.8438197374344,-2.38418579101562e-05,-1.99999809265137,-50.918710231781,-2.00002193450928,-1.99999809265137,-50.918710231781,1.99997425079346,1.99999809265137,-48.5096544027328,1.99997425079346,1.99999809265137,-48.5096544027328,-2.00002193450928,7.76841640472412,-44.8438197374344,-2.38418579101562e-05,1.99999809265137,-50.918710231781,1.99997425079346,1.99999809265137,-50.918710231781,-2.00002193450928,1.99999809265137,51.4741897583008,1.99997425079346,-1.99999809265137,26.4903575181961,1.99997425079346,-0,55.3645133972168,-2.38418579101562e-05,1.99999809265137,51.4741897583008,-2.00002193450928,-1.99999809265137,51.4741897583008,-2.00002193450928,-0,-55.3645133972168,0,-1.99999809265137,-37.3429924249649,1.99999809265137,1.99999809265137,-37.3429924249649,1.99999809265137,-1.99999809265137,-37.3429208993912,-1.99999809265137,1.99999809265137,-37.3429208993912,-1.99999809265137
		} 
		PolygonVertexIndex: *156 {
			a: 0,2,-2,3,2,-1,2,4,-2,2,3,-5,5,7,-7,8,7,-6,7,9,-7,7,8,-10,10,12,-12,13,12,-11,11,12,-15,14,12,-14,15,17,-17,18,17,-16,16,17,-20,19,17,-19,20,22,-22,23,22,-21,21,22,-25,24,22,-24,19,25,-14,18,25,-20,13,25,-15,14,25,-19,26,15,-28,15,26,-12,11,18,-16,18,11,-15,11,28,-11,28,11,-27,16,28,-30,28,16,-11,16,13,-11,13,16,-20,15,29,-28,29,15,-17,27,21,-27,21,27,-21,26,24,-29,24,26,-22,23,28,-25,28,23,-30,23,27,-30,27,23,-21,9,5,-7,5,9,-9,3,1,-5,1,3,-1,28,27,-30,27,28,-27,26,29,-28,29,26,-29
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *468 {
				a: 0.183257669210434,8.16513511381345e-06,-0.983064889907837,0.183257669210434,8.16513511381345e-06,-0.983064889907837,0.183257669210434,8.16513511381345e-06,-0.983064889907837,0.47132021188736,0.881962180137634,0,0.47132021188736,0.881962180137634,0,0.47132021188736,0.881962180137634,0,-0.257826954126358,-0.966191112995148,-6.47878368909005e-06,-0.257826954126358,-0.966191112995148,-6.47878368909005e-06,-0.257826954126358,-0.966191112995148,-6.47878368909005e-06,0.183257669210434,0,0.983064889907837,0.183257669210434,0,0.983064889907837,0.183257669210434,0,0.983064889907837,0.257826954126358,-0.966191112995148,-6.47878368909005e-06,0.257826954126358,-0.966191112995148,-6.47878368909005e-06,0.257826954126358,-0.966191112995148,-6.47878368909005e-06,-0.183257669210434,0,0.983064889907837,-0.183257669210434,0,0.983064889907837,-0.183257669210434,0,0.983064889907837,-0.183257669210434,8.16513511381345e-06,-0.983064889907837,-0.183257669210434,8.16513511381345e-06,-0.983064889907837,-0.183257669210434,8.16513511381345e-06,-0.983064889907837,-0.47132021188736,0.881962180137634,0,-0.47132021188736,0.881962180137634,0,-0.47132021188736,0.881962180137634,0,0.536357045173645,0.843991160392761,0,0.536357045173645,0.843991160392761,0,0.536357045173645,0.843991160392761,0,-0.327584117650986,0,-0.944822013378143,-0.327584117650986,0,-0.944822013378143,-0.327584117650986,0,-0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.725161969661713,-0.688578367233276,0,-0.725161969661713,-0.688578367233276,0,-0.725161969661713,-0.688578367233276,0,-0.536357045173645,0.843991160392761,0,-0.536357045173645,0.843991160392761,0,-0.536357045173645,0.843991160392761,0,0.327584117650986,0,0.944822013378143,0.327584117650986,0,0.944822013378143,0.327584117650986,0,0.944822013378143,0.327584117650986,0,-0.944822013378143,0.327584117650986,0,-0.944822013378143,0.327584117650986,0,-0.944822013378143,0.725161969661713,-0.688578367233276,0,0.725161969661713,-0.688578367233276,0,
0.725161969661713,-0.688578367233276,0,-0.60351949930191,0.0966255143284798,0.791471898555756,-0.60351949930191,0.0966255143284798,0.791471898555756,-0.60351949930191,0.0966255143284798,0.791471898555756,0.889356553554535,0.457214266061783,0,0.889356553554535,0.457214266061783,0,0.889356553554535,0.457214266061783,0,-0.791471898555756,0.0966255143284798,0.60351949930191,-0.791471898555756,0.0966255143284798,0.60351949930191,-0.791471898555756,0.0966255143284798,0.60351949930191,-0,0.457214266061783,-0.889356553554535,-0,0.457214266061783,-0.889356553554535,-0,0.457214266061783,-0.889356553554535,-0,-0.410264104604721,-0.911966741085052,-0,-0.410264104604721,-0.911966741085052,-0,-0.410264104604721,-0.911966741085052,0.911968588829041,-0.410260051488876,0,0.911968588829041,-0.410260051488876,0,0.911968588829041,-0.410260051488876,0,-0.911968588829041,-0.410260051488876,0,-0.911968588829041,-0.410260051488876,0,-0.911968588829041,-0.410260051488876,0,-0,-0.410255968570709,0.911970436573029,-0,-0.410255968570709,0.911970436573029,-0,-0.410255968570709,0.911970436573029,-0,-2.13509269997303e-06,1,-0,-1.06754634998651e-06,1,-0,-2.13509269997303e-06,1,-0,-1.06754634998651e-06,1,-0,-2.13509269997303e-06,1,-0,-1.06754634998651e-06,1,-0,-1.06754634998651e-06,1,-0,0,1,-0,-1.06754634998651e-06,1,-0,0,1,-0,-1.06754634998651e-06,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,1.06753952877625e-06,-1,-0,2.1350790575525e-06,-1,-0,2.1350790575525e-06,-1,-0,2.1350790575525e-06,-1,-0,1.06753952877625e-06,-1,-0,1.06753952877625e-06,-1,-0,1.06753952877625e-06,-1,-0,0,-1,-0,1.06753952877625e-06,-1,-0,0,-1,-0,1.06753952877625e-06,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-8.38325320273725e-07,3.20969519407299e-07,1,-8.38325320273725e-07,3.20969519407299e-07,1,-0,3.7350159232119e-07,1,-8.38325320273725e-07,3.20969519407299e-07,1,-8.38325320273725e-07,3.20969519407299e-07,1,-1.67665064054745e-06,2.68437446493408e-07,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,-2.68437673867084e-07,-1,-0,-2.68437673867084e-07,-1,-0,-2.68437673867084e-07,-1,
-0,-2.68437673867084e-07,-1,-0,-2.68437673867084e-07,-1,-0,-2.68437673867084e-07,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,-1,-1.78814098035218e-05,-0,-1,-1.78814098035218e-05,-0,-1,-1.78814098035218e-05,-0,-1,-1.78814098035218e-05,-0,-1,-1.78814098035218e-05,-0,-1,-1.78814098035218e-05
			} 
			NormalsW: *156 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *468 {
				a: -1.49632364809804e-06,1,8.02685826784e-06,-1.49632364809804e-06,1,8.02685826784e-06,-1.49632364809804e-06,1,8.02685826784e-06,0,-0,1,0,-0,1,0,-0,1,-1.67040502674354e-06,-6.25974325885181e-06,1,-1.67040502674354e-06,-6.25974325885181e-06,1,-1.67040502674354e-06,-6.25974325885181e-06,1,-0,1,-0,-0,1,-0,-0,1,-0,1.67040502674354e-06,-6.25974325885181e-06,1,1.67040502674354e-06,-6.25974325885181e-06,1,1.67040502674354e-06,-6.25974325885181e-06,1,0,1,-0,0,1,-0,0,1,-0,1.49632364809804e-06,1,8.02685826784e-06,1.49632364809804e-06,1,8.02685826784e-06,1.49632364809804e-06,1,8.02685826784e-06,0,-0,1,0,-0,1,0,-0,1,1.22933465718233e-06,-7.81243159053702e-07,-0.999999940395355,1.22933465718233e-06,-7.81243159053702e-07,-0.999999940395355,1.22933465718233e-06,-7.81243159053702e-07,-0.999999940395355,-2.7230396426603e-07,-1,9.4411909401515e-08,-2.7230396426603e-07,-1,9.4411909401515e-08,-2.7230396426603e-07,-1,9.4411909401515e-08,-5.73344323129277e-08,-1,-1.98787173388837e-08,-5.73344323129277e-08,-1,-1.98787173388837e-08,-5.73344323129277e-08,-1,-1.98787173388837e-08,0.688578367233276,-0.725161969661713,-7.9815617937129e-07,0.688578367233276,-0.725161969661713,-7.9815617937129e-07,0.688578367233276,-0.725161969661713,-7.9815617937129e-07,-5.71783260738812e-08,-3.63368712896772e-08,-0.999999940395355,-5.71783260738812e-08,-3.63368712896772e-08,-0.999999940395355,-5.71783260738812e-08,-3.63368712896772e-08,-0.999999940395355,2.7230396426603e-07,-1,-9.4411909401515e-08,2.7230396426603e-07,-1,-9.4411909401515e-08,2.7230396426603e-07,-1,-9.4411909401515e-08,5.73344323129277e-08,-1,1.98787173388837e-08,5.73344323129277e-08,-1,1.98787173388837e-08,5.73344323129277e-08,-1,1.98787173388837e-08,-0.688578367233276,-0.725161969661713,7.9815617937129e-07,-0.688578367233276,-0.725161969661713,7.9815617937129e-07,-0.688578367233276,-0.725161969661713,7.9815617937129e-07,-0.00297233602032065,0.99235063791275,-0.123415946960449,-0.00297233602032065,0.99235063791275,-0.123415946960449,-0.00297233602032065,0.99235063791275,-0.123415946960449,
-0.429824531078339,0.836079001426697,0.340914636850357,-0.429824531078339,0.836079001426697,0.340914636850357,-0.429824531078339,0.836079001426697,0.340914636850357,0.123409070074558,0.992351531982422,0.00296317553147674,0.123409070074558,0.992351531982422,0.00296317553147674,0.123409070074558,0.992351531982422,0.00296317553147674,-0.340921729803085,0.836076736450195,0.429823338985443,-0.340921729803085,0.836076736450195,0.429823338985443,-0.340921729803085,0.836076736450195,0.429823338985443,-0.456589072942734,0.811356544494629,-0.36500284075737,-0.456589072942734,0.811356544494629,-0.36500284075737,-0.456589072942734,0.811356544494629,-0.36500284075737,0.365000784397125,0.811361610889435,0.456581681966782,0.365000784397125,0.811361610889435,0.456581681966782,0.365000784397125,0.811361610889435,0.456581681966782,-0.385869413614273,0.857750535011292,0.339659988880157,-0.385869413614273,0.857750535011292,0.339659988880157,-0.385869413614273,0.857750535011292,0.339659988880157,-0.339666813611984,0.857749998569489,0.385864555835724,-0.339666813611984,0.857749998569489,0.385864555835724,-0.339666813611984,0.857749998569489,0.385864555835724,-0.360612213611603,0.932715833187103,1.99143482859654e-06,-0.360612213611603,0.932715833187103,9.95717414298269e-07,-0.36061292886734,0.932715594768524,1.99143414647551e-06,-0.360612213611603,0.932715833187103,9.95717414298269e-07,-0.360612213611603,0.932715833187103,1.99143482859654e-06,-0.360611468553543,0.932716131210327,9.95717755358783e-07,-0.360612362623215,0.932715833187103,9.95717414298269e-07,-0.360612362623215,0.932715833187103,-0,-0.360610276460648,0.932716608047485,9.95718210106133e-07,-0.360612362623215,0.932715833187103,-0,-0.360612362623215,0.932715833187103,9.95717414298269e-07,-0.360614418983459,0.932714998722076,-0,0,0.93271791934967,0.360606849193573,0,0.93271791934967,0.360606849193573,0,0.932717621326447,0.360607653856277,0,0.93271791934967,0.360606849193573,0,0.93271791934967,0.360606849193573,0,0.932718217372894,0.360606044530869,-0.360614329576492,0.932715058326721,9.95710138340655e-07,
-0.360614329576492,0.932715058326721,1.99142027668131e-06,-0.36061355471611,0.932715356349945,1.99142095880234e-06,-0.360614329576492,0.932715058326721,1.99142027668131e-06,-0.360614329576492,0.932715058326721,9.95710138340655e-07,-0.360615015029907,0.932714760303497,9.9570991096698e-07,-0.360615015029907,0.932714819908142,9.9570991096698e-07,-0.360615015029907,0.932714819908142,0,-0.360615700483322,0.932714521884918,9.95709569906467e-07,-0.360615015029907,0.932714819908142,0,-0.360615015029907,0.932714819908142,9.9570991096698e-07,-0.36061429977417,0.932715058326721,0,-0,0.932716906070709,0.360609382390976,-0,0.932716906070709,0.360609382390976,-0,0.932717680931091,0.360607475042343,-0,0.932716906070709,0.360609382390976,-0,0.932716906070709,0.360609382390976,-0,0.932716190814972,0.360611319541931,-0.36061218380928,0.932715892791748,-6.0168372328917e-07,-0.36061218380928,0.932715892791748,-6.0168372328917e-07,-0.360612899065018,0.932715594768524,-3.48370747360605e-07,-0.36061218380928,0.932715892791748,-6.0168372328917e-07,-0.36061218380928,0.932715892791748,-6.0168372328917e-07,-0.360611408948898,0.932716190814972,-8.54995278132265e-07,0,0.932718396186829,0.360605716705322,0,0.932718396186829,0.360605716705322,0,0.932718336582184,0.360605895519257,0,0.932718396186829,0.360605716705322,0,0.932718396186829,0.360605716705322,0,0.932718455791473,0.360605508089066,-0.360613495111465,0.93271541595459,-2.50375961741156e-07,-0.360613495111465,0.93271541595459,-2.50375961741156e-07,-0.360612899065018,0.932715594768524,-2.50376018584575e-07,-0.360613495111465,0.93271541595459,-2.50375961741156e-07,-0.360613495111465,0.93271541595459,-2.50375961741156e-07,-0.360614061355591,0.932715117931366,-2.50375876476028e-07,-0,0.932717978954315,0.360606789588928,-0,0.932717978954315,0.360606789588928,-0,0.932717800140381,0.360607087612152,-0,0.932717978954315,0.360606789588928,-0,0.932717978954315,0.360606789588928,-0,0.93271803855896,0.360606491565704,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,
-0.707130968570709,-1.26436334539903e-05,0.707082569599152,-0.707130968570709,-1.26436334539903e-05,0.707082569599152,-0.707130968570709,-1.2643634363485e-05,0.707082629203796,-0.707130968570709,-1.26436334539903e-05,0.707082569599152,-0.707130968570709,-1.26436334539903e-05,0.707082569599152,-0.707130968570709,-1.26436334539903e-05,0.707082569599152,-0.707130968570709,-1.26436334539903e-05,0.707082569599152,-0.707130968570709,-1.26436334539903e-05,0.707082569599152,-0.707129538059235,-1.2643658919842e-05,0.707084000110626,-0.707130968570709,-1.26436334539903e-05,0.707082569599152,-0.707130968570709,-1.26436334539903e-05,0.707082569599152,-0.707132399082184,-1.26436088976334e-05,0.707081198692322
			} 
			BinormalsW: *156 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *468 {
				a: -0.983064949512482,1.2166999450048e-13,-0.183257684111595,-0.983064949512482,1.2166999450048e-13,-0.183257684111595,-0.983064949512482,1.2166999450048e-13,-0.183257684111595,-0.881962180137634,0.47132021188736,0,-0.881962180137634,0.47132021188736,0,-0.881962180137634,0.47132021188736,0,0.966191112995148,-0.257826954126358,-9.65414068674654e-14,0.966191112995148,-0.257826954126358,-9.65414068674654e-14,0.966191112995148,-0.257826954126358,-9.65414068674654e-14,0.983064949512482,0,-0.183257684111595,0.983064949512482,0,-0.183257684111595,0.983064949512482,0,-0.183257684111595,0.966191112995148,0.257826954126358,9.65414068674654e-14,0.966191112995148,0.257826954126358,9.65414068674654e-14,0.966191112995148,0.257826954126358,9.65414068674654e-14,0.983064949512482,0,0.183257684111595,0.983064949512482,0,0.183257684111595,0.983064949512482,0,0.183257684111595,-0.983064949512482,-1.2166999450048e-13,0.183257684111595,-0.983064949512482,-1.2166999450048e-13,0.183257684111595,-0.983064949512482,-1.2166999450048e-13,0.183257684111595,-0.881962180137634,-0.47132021188736,0,-0.881962180137634,-0.47132021188736,0,-0.881962180137634,-0.47132021188736,0,-0.843991160392761,0.536357045173645,-1.45657293160184e-06,-0.843991160392761,0.536357045173645,-1.45657293160184e-06,-0.843991160392761,0.536357045173645,-1.45657293160184e-06,-0.944822013378143,2.88206621235076e-07,0.327584117650986,-0.944822013378143,2.88206621235076e-07,0.327584117650986,-0.944822013378143,2.88206621235076e-07,0.327584117650986,0.944822013378143,-6.06827867954962e-08,0.327584117650986,0.944822013378143,-6.06827867954962e-08,0.327584117650986,0.944822013378143,-6.06827867954962e-08,0.327584117650986,5.49593039522733e-07,-5.78792480609991e-07,1,5.49593039522733e-07,-5.78792480609991e-07,1,5.49593039522733e-07,-5.78792480609991e-07,1,-0.843991160392761,-0.536357045173645,6.77475426869023e-08,-0.843991160392761,-0.536357045173645,6.77475426869023e-08,-0.843991160392761,-0.536357045173645,6.77475426869023e-08,0.944822013378143,2.88206621235076e-07,-0.327584117650986,
0.944822013378143,2.88206621235076e-07,-0.327584117650986,0.944822013378143,2.88206621235076e-07,-0.327584117650986,-0.944822013378143,-6.06827867954962e-08,-0.327584117650986,-0.944822013378143,-6.06827867954962e-08,-0.327584117650986,-0.944822013378143,-6.06827867954962e-08,-0.327584117650986,-5.49593039522733e-07,-5.78792480609991e-07,-1,-5.49593039522733e-07,-5.78792480609991e-07,-1,-5.49593039522733e-07,-5.78792480609991e-07,-1,0.797342777252197,0.0768364444375038,0.598615705966949,0.797342777252197,0.0768364444375038,0.598615705966949,0.797342777252197,0.0768364444375038,0.598615705966949,0.155871033668518,-0.303194671869278,0.940094292163849,0.155871033668518,-0.303194671869278,0.940094292163849,0.155871033668518,-0.303194671869278,0.940094292163849,0.598617136478424,-0.0768250450491905,0.797342777252197,0.598617136478424,-0.0768250450491905,0.797342777252197,0.598617136478424,-0.0768250450491905,0.797342777252197,0.940091729164124,0.303200989961624,0.155874282121658,0.940091729164124,0.303200989961624,0.155874282121658,0.940091729164124,0.303200989961624,0.155874282121658,0.889677762985229,0.416394054889679,-0.187322109937668,0.889677762985229,0.416394054889679,-0.187322109937668,0.889677762985229,0.416394054889679,-0.187322109937668,-0.187317222356796,-0.416388154029846,0.88968151807785,-0.187317222356796,-0.416388154029846,0.88968151807785,-0.187317222356796,-0.416388154029846,0.88968151807785,0.139348924160004,-0.309759229421616,0.940548360347748,0.139348924160004,-0.309759229421616,0.940548360347748,0.139348924160004,-0.309759229421616,0.940548360347748,0.940545856952667,0.309766083955765,0.139350339770317,0.940545856952667,0.309766083955765,0.139350339770317,0.940545856952667,0.309766083955765,0.139350339770317,0.932715833187103,0.360612213611603,7.69940527334256e-07,0.932715833187103,0.360612213611603,3.84970263667128e-07,0.932715594768524,0.36061292886734,7.69942005263147e-07,0.932715833187103,0.360612213611603,3.84970263667128e-07,0.932715833187103,0.360612213611603,7.69940527334256e-07,0.932716131210327,0.360611468553543,3.84969467859264e-07,
0.932715833187103,0.360612362623215,3.84970405775675e-07,0.932715833187103,0.360612362623215,0,0.932716608047485,0.360610276460648,3.8496818888234e-07,0.932715833187103,0.360612362623215,0,0.932715833187103,0.360612362623215,3.84970405775675e-07,0.932714998722076,0.360614418983459,0,-0,-0.360606849193573,0.93271791934967,-0,-0.360606849193573,0.93271791934967,-0,-0.360607653856277,0.932717621326447,-0,-0.360606849193573,0.93271791934967,-0,-0.360606849193573,0.93271791934967,-0,-0.360606044530869,0.932718217372894,0.932715058326721,0.360614329576492,3.84970036293453e-07,0.932715058326721,0.360614329576492,7.69940072586905e-07,0.932715356349945,0.36061355471611,7.69938424127758e-07,0.932715058326721,0.360614329576492,7.69940072586905e-07,0.932715058326721,0.360614329576492,3.84970036293453e-07,0.932714760303497,0.360615015029907,3.84970775257898e-07,0.932714819908142,0.360615015029907,3.84970775257898e-07,0.932714819908142,0.360615015029907,0,0.932714521884918,0.360615700483322,3.84971514222343e-07,0.932714819908142,0.360615015029907,0,0.932714819908142,0.360615015029907,3.84970775257898e-07,0.932715058326721,0.36061429977417,0,-0,-0.360609382390976,0.932716906070709,-0,-0.360609382390976,0.932716906070709,-0,-0.360607475042343,0.932717680931091,-0,-0.360609382390976,0.932716906070709,-0,-0.360609382390976,0.932716906070709,-0,-0.360611319541931,0.932716190814972,0.932715892791748,0.36061218380928,6.66173889385391e-07,0.932715892791748,0.36061218380928,6.66173889385391e-07,0.932715594768524,0.360612899065018,-1.34689486230855e-07,0.932715892791748,0.36061218380928,6.66173889385391e-07,0.932715892791748,0.36061218380928,6.66173889385391e-07,0.932716190814972,0.360611408948898,1.46703757764044e-06,-0,-0.360605716705322,0.932718396186829,-0,-0.360605716705322,0.932718396186829,-0,-0.360605895519257,0.932718336582184,-0,-0.360605716705322,0.932718396186829,-0,-0.360605716705322,0.932718396186829,-0,-0.360605508089066,0.932718455791473,0.93271541595459,0.360613495111465,-9.68022462188856e-08,0.93271541595459,0.360613495111465,-9.68022462188856e-08,
0.932715594768524,0.360612899065018,-9.68020898994837e-08,0.93271541595459,0.360613495111465,-9.68022462188856e-08,0.93271541595459,0.360613495111465,-9.68022462188856e-08,0.932715117931366,0.360614061355591,-9.68024025382874e-08,-0,-0.360606789588928,0.932717978954315,-0,-0.360606789588928,0.932717978954315,-0,-0.360607087612152,0.932717800140381,-0,-0.360606789588928,0.932717978954315,-0,-0.360606789588928,0.932717978954315,-0,-0.360606491565704,0.93271803855896,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0.707082569599152,-1.26444983834517e-05,0.707130968570709,0.707082569599152,-1.26444983834517e-05,0.707130968570709,0.707082629203796,-1.26444983834517e-05,0.707130968570709,0.707082569599152,-1.26444983834517e-05,0.707130968570709,0.707082569599152,-1.26444983834517e-05,0.707130968570709,0.707082569599152,-1.26444983834517e-05,0.707130968570709,0.707082569599152,-1.26444983834517e-05,0.707130968570709,0.707082569599152,-1.26444983834517e-05,0.707130968570709,0.707084000110626,-1.26444729176001e-05,0.707129538059235,0.707082569599152,-1.26444983834517e-05,0.707130968570709,0.707082569599152,-1.26444983834517e-05,0.707130968570709,0.707081198692322,-1.26445238493034e-05,0.707132399082184
			} 
			TangentsW: *156 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *256 {
				a: 0.107288897037506,0.0573351383209229,0.107288897037506,0.0286300480365753,0,0,0.107288897037506,0.0399999618530273,0.107288897037506,0,0,0.0199997425079346,0.254112243652344,0.0199999809265137,0.146823346614838,0,0.146823346614838,0.0400002002716064,0.254112243652344,0,0.146823346614838,0.0286297798156738,0.146823346614838,0.0573351383209229,0.107288897037506,0.0400002002716064,0.107288897037506,0,0,0.0199999809265137,0.107288897037506,0.0573351383209229,0.107288897037506,0.0286297798156738,0,0,0.254112243652344,0,0.146823346614838,0.0286300480365753,0.146823346614838,0.0573351383209229,0.254112243652344,0.0199997425079346,0.146823346614838,0,0.146823346614838,0.0399999618530273,0.628726243972778,0.390999525785446,0.628726363182068,0.133547067642212,0.999999940395355,0.262272655963898,0.628726243972778,0.390999525785446,0.628726243972778,0.235944956541061,0.999999940395355,1.78813934326172e-07,0.371273815631866,0.235944956541061,0.371273845434189,0.390999525785446,0,8.94069671630859e-08,0.257452577352524,0.390999555587769,0,0.39099970459938,0.128726363182068,2.38418579101562e-07,0.371273815631866,0.1335469186306,0.371273845434189,0.390999525785446,-1.19209289550781e-07,0.262273192405701,0.628726243972778,0.390999525785446,0.628726243972778,0.235944956541061,0.999999940395355,1.78813934326172e-07,0.371273815631866,0.235944956541061,0.371273845434189,0.390999525785446,0,8.94069671630859e-08,0.257452577352524,0.390999555587769,0,0.39099970459938,0.128726363182068,2.38418579101562e-07,0.966334939002991,8.36887073516846,0.472655653953552,4.77952194213867,0.472650706768036,8.96466255187988,0.472646445035934,8.1320333480835,0.966334939002991,8.36887073516846,0.472650706768036,8.96466255187988,0.472655653953552,4.77952194213867,-0.0210335552692413,8.36887550354004,0.472650706768036,8.96466255187988,-0.0210335552692413,8.36887550354004,0.472646445035934,8.1320333480835,0.472650706768036,8.96466255187988,0.472646445035934,-7.54909515380859,-0.0210335552692413,-7.31225395202637,0.472653806209564,-7.99311447143555,
0.966334939002991,-7.31225872039795,0.472646445035934,-7.54909515380859,0.472653806209564,-7.99311447143555,-0.0210335552692413,-7.31225395202637,0.472655653953552,-7.07541561126709,0.472653806209564,-7.99311447143555,0.472655653953552,-7.07541561126709,0.966334939002991,-7.31225872039795,0.472653806209564,-7.99311447143555,0.472658753395081,-4.99633979797363,0.966337442398071,-5.23318195343018,0.966334939002991,-6.94331836700439,0.472655653953552,-6.70647716522217,0.472655653953552,-6.70647716522217,0.966334939002991,-6.94331836700439,0.966334939002991,-7.31225872039795,0.472655653953552,-7.07541561126709,0.472655653953552,-6.70647716522217,-0.0210335552692413,-6.94331550598145,-0.0210307538509369,-5.2331657409668,0.472658753395081,-4.99633979797363,0.472646445035934,-7.18015956878662,0.47264888882637,-5.47000885009766,-0.0210307538509369,-5.2331657409668,-0.0210335552692413,-6.94331550598145,0.472646445035934,-7.18015956878662,-0.0210335552692413,-6.94331550598145,-0.0210335552692413,-7.31225395202637,0.472646445035934,-7.54909515380859,0.966334939002991,-6.94331836700439,0.966337442398071,-5.23318195343018,0.47264888882637,-5.47000885009766,0.472646445035934,-7.18015956878662,0.966337442398071,-5.23318195343018,0.472658753395081,-4.99633979797363,0.472655653953552,4.77952194213867,0.966334939002991,8.36887073516846,0.472658753395081,-4.99633979797363,-0.0210307538509369,-5.2331657409668,-0.0210335552692413,8.36887550354004,0.472655653953552,4.77952194213867,0.472646445035934,8.1320333480835,-0.0210335552692413,8.36887550354004,-0.0210307538509369,-5.2331657409668,0.47264888882637,-5.47000885009766,0.472646445035934,8.1320333480835,0.47264888882637,-5.47000885009766,0.966337442398071,-5.23318195343018,0.966334939002991,8.36887073516846,0.0399999618530273,0.028705358505249,0.0400002002716064,2.68220901489258e-07,0,0,0,0.028705358505249,0.0400002002716064,0.028705358505249,0.0400002002716064,0,0,2.68220901489258e-07,2.38418579101562e-07,0.028705358505249,-0.0210307538509369,-5.2331657409668,0.47264888882637,-5.47000885009766,
0.966337442398071,-5.23318195343018,0.472658753395081,-4.99633979797363,0.472658753395081,-4.99633979797363,0.966337442398071,-5.23318195343018,0.47264888882637,-5.47000885009766,-0.0210307538509369,-5.2331657409668
			} 
			UVIndex: *156 {
				a: 0,2,1,3,5,4,6,8,7,9,11,10,12,14,13,15,17,16,18,20,19,21,23,22,24,26,25,27,29,28,30,32,31,33,35,34,36,38,37,39,41,40,42,44,43,45,47,46,48,50,49,51,53,52,54,56,55,57,59,58,60,62,61,63,65,64,66,68,67,69,71,70,72,74,73,74,72,75,76,78,77,78,76,79,80,82,81,82,80,83,84,86,85,86,84,87,88,90,89,90,88,91,92,94,93,94,92,95,96,98,97,98,96,99,100,102,101,102,100,103,104,106,105,106,104,107,108,110,109,110,108,111,112,114,113,114,112,115,116,118,117,118,116,119,120,122,121,122,120,123,124,126,125,126,124,127
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2012768145552, "Model::Holo_Sword_Model", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",2,2,2
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2018664749456, "Material::Hologram_2", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2017421259824, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Environment\Hologram 1.png"
			P: "RelPath", "KString", "XRefUrl", "", "..\Sprites\Environment\Hologram 1.png"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Environment\Hologram 1.png"
		RelativeFilename: "..\Sprites\Environment\Hologram 1.png"
	}
	Texture: 2017421261744, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Environment\Hologram 1.png"
		RelativeFilename: "..\Sprites\Environment\Hologram 1.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Holo_Sword_Model, Model::RootNode
	C: "OO",2012768145552,0
	
	;Material::Hologram_2, Model::Holo_Sword_Model
	C: "OO",2018664749456,2012768145552
	
	;Geometry::Scene, Model::Holo_Sword_Model
	C: "OO",2010873139264,2012768145552
	
	;Texture::DiffuseColor_Texture, Material::Hologram_2
	C: "OO",2017421261744,2018664749456
	
	;Texture::DiffuseColor_Texture, Material::Hologram_2
	C: "OP",2017421261744,2018664749456, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",2017421259824,2017421261744
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
