; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 5
		Day: 7
		Hour: 21
		Minute: 26
		Second: 3
		Millisecond: 746
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\f3arna44_Mini Carrier Handle Model.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\f3arna44_Mini Carrier Handle Model.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2275805285632, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2280193598800, "Geometry::Scene", "Mesh" {
		Vertices: *48 {
			a: -1.99999809265137,-10.6702782213688,1.99999809265137,1.99999809265137,-10.6702782213688,1.99999809265137,1.99999809265137,-26.1033594608307,1.99997425079346,-1.99999809265137,-26.1033594608307,1.99997425079346,1.99999809265137,-28.5124123096466,1.99997425079346,4.49999570846558,-27.3078888654709,-2.38418579101562e-05,-1.99999809265137,-28.5124123096466,1.99997425079346,-4.49999570846558,-27.3078888654709,-2.38418579101562e-05,-1.99999809265137,-10.6702066957951,-1.99999809265137,-1.99999809265137,-26.1033594608307,-2.00002193450928,-0,-27.3078858852386,4.49997186660767,-1.99999809265137,-28.5124242305756,-2.00002193450928,-0,-27.3078918457031,-4.50001955032349,1.99999809265137,-26.1033594608307,-2.00002193450928,1.99999809265137,-10.6702066957951,-1.99999809265137,1.99999809265137,-28.5124242305756,-2.00002193450928
		} 
		PolygonVertexIndex: *84 {
			a: 4,5,-3,3,7,-7,6,10,-4,9,12,-12,2,5,-14,13,12,-10,9,7,-4,3,10,-3,13,5,-16,15,5,-5,11,12,-16,6,7,-12,4,10,-7,11,7,-10,15,12,-14,2,10,-5,0,2,-2,2,0,-4,8,3,-1,3,8,-10,13,8,-15,8,13,-10,2,14,-2,14,2,-14,4,11,-16,11,4,-7,8,1,-15,1,8,-1
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *252 {
				a: 0.624695062637329,0,0.780868828296661,0.624695062637329,0,0.780868828296661,0.624695062637329,0,0.780868828296661,-0.624695062637329,0,0.780868828296661,-0.624695062637329,0,0.780868828296661,-0.624695062637329,0,0.780868828296661,-0.780868828296661,0,0.624695062637329,-0.780868828296661,0,0.624695062637329,-0.780868828296661,0,0.624695062637329,-0.780868828296661,0,-0.624695062637329,-0.780868828296661,0,-0.624695062637329,-0.780868828296661,0,-0.624695062637329,0.434057503938675,0.900885224342346,0,0.434057503938675,0.900885224342346,0,0.434057503938675,0.900885224342346,0,-0,0.900884747505188,-0.434058368206024,-0,0.900884747505188,-0.434058368206024,-0,0.900884747505188,-0.434058368206024,-0.434057503938675,0.900885224342346,0,-0.434057503938675,0.900885224342346,0,-0.434057503938675,0.900885224342346,0,-0,0.900885581970215,0.434056609869003,-0,0.900885581970215,0.434056609869003,-0,0.900885581970215,0.434056609869003,0.624695062637329,0,-0.780868828296661,0.624695062637329,0,-0.780868828296661,0.624695062637329,0,-0.780868828296661,0.151333078742027,-0.976830124855042,-0.151331409811974,0.434057503938675,-0.900885224342346,2.68484973275918e-06,0.151332959532738,-0.976829648017883,0.151334628462791,-0.151333078742027,-0.976830124855042,-0.151331409811974,-0,-0.900884747505188,-0.434058368206024,0.151333078742027,-0.976830124855042,-0.151331409811974,-0.151332959532738,-0.976829648017883,0.151334628462791,-0.434057503938675,-0.900885224342346,2.68484973275918e-06,-0.151333078742027,-0.976830124855042,-0.151331409811974,0.151332959532738,-0.976829648017883,0.151334628462791,-0,-0.900885581970215,0.434056609869003,-0.151332959532738,-0.976829648017883,0.151334628462791,-0.624695062637329,0,-0.780868828296661,-0.624695062637329,0,-0.780868828296661,-0.624695062637329,0,-0.780868828296661,0.780868828296661,0,-0.624695062637329,0.780868828296661,0,-0.624695062637329,0.780868828296661,0,-0.624695062637329,0.780868828296661,0,0.624695062637329,0.780868828296661,0,0.624695062637329,0.780868828296661,0,0.624695062637329,
-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-0,-1.54485394432413e-06,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,-0,1.54484678205336e-06,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.151332959532738,-0.976829648017883,0.151334628462791,-0.151333078742027,-0.976830124855042,-0.151331409811974,0.151333078742027,-0.976830124855042,-0.151331409811974,-0.151333078742027,-0.976830124855042,-0.151331409811974,0.151332959532738,-0.976829648017883,0.151334628462791,-0.151332959532738,-0.976829648017883,0.151334628462791,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05
			} 
			NormalsW: *84 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *252 {
				a: -3.63441955641974e-07,1,2.90753575882263e-07,-3.63441955641974e-07,1,2.90753575882263e-07,-3.63441955641974e-07,1,2.90753575882263e-07,3.63441955641974e-07,1,2.90753575882263e-07,3.63441955641974e-07,1,2.90753575882263e-07,3.63441955641974e-07,1,2.90753575882263e-07,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0.89626932144165,0.431833505630493,0.101100087165833,-0.89626932144165,0.431833505630493,0.101100087165833,-0.89626932144165,0.431833505630493,0.101100087165833,0,0.434058368206024,0.900884747505188,0,0.434058368206024,0.900884747505188,0,0.434058368206024,0.900884747505188,0.89626932144165,0.431833505630493,0.101100087165833,0.89626932144165,0.431833505630493,0.101100087165833,0.89626932144165,0.431833505630493,0.101100087165833,-0,0.434056609869003,-0.900885581970215,-0,0.434056609869003,-0.900885581970215,-0,0.434056609869003,-0.900885581970215,-0,1,0,-0,1,0,-0,1,0,-0.981144785881042,-0.167058601975441,0.0971921011805534,-0.89626932144165,-0.431833207607269,0.10110168159008,-0.985765874385834,-0.1377944201231,0.096324235200882,-1.54472967750507e-09,-0.153094634413719,0.988211572170258,0,-0.434058368206024,0.900884747505188,1.54472967750507e-09,-0.153094634413719,0.988211572170258,0.985765874385834,-0.1377944201231,0.096324235200882,0.89626932144165,-0.431833207607269,0.10110168159008,0.981144785881042,-0.167058601975441,0.0971921011805534,2.18035367538505e-10,-0.153097882866859,-0.988211095333099,0,-0.434056609869003,-0.900885581970215,-2.18035367538505e-10,-0.153097882866859,-0.988211095333099,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,-0,1,-0,-0,1,-0,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,-0,1,1.54485394432413e-06,0,1,-8.63255322260115e-13,0,1,-8.63255322260115e-13,0,1,-1.72651064452023e-12,0,1,-8.63255322260115e-13,0,1,-8.63255322260115e-13,0,1,-0,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,0,1,1.54484678205336e-06,
0,1,-8.63255322260115e-13,0,1,-8.63255322260115e-13,0,1,-1.72651064452023e-12,0,1,-8.63255322260115e-13,0,1,-8.63255322260115e-13,-0,1,0,-2.18035367538505e-10,0.153097882866859,0.988211095333099,-1.54472967750507e-09,-0.153094634413719,0.988211572170258,1.54472967750507e-09,-0.153094634413719,0.988211572170258,-1.54472967750507e-09,-0.153094634413719,0.988211572170258,-2.18035367538505e-10,0.153097882866859,0.988211095333099,2.18035367538505e-10,0.153097882866859,0.988211095333099,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1
			} 
			BinormalsW: *84 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *252 {
				a: 0.780868828296661,4.65432805185628e-07,-0.624695062637329,0.780868828296661,4.65432805185628e-07,-0.624695062637329,0.780868828296661,4.65432805185628e-07,-0.624695062637329,0.780868828296661,-4.65432805185628e-07,0.624695062637329,0.780868828296661,-4.65432805185628e-07,0.624695062637329,0.780868828296661,-4.65432805185628e-07,0.624695062637329,0.624695062637329,0,0.780868768692017,0.624695062637329,0,0.780868768692017,0.624695062637329,0,0.780868768692017,-0.624695062637329,0,0.780868828296661,-0.624695062637329,0,0.780868828296661,-0.624695062637329,0,0.780868828296661,0.0910795629024506,-0.0438832454383373,0.994876265525818,0.0910795629024506,-0.0438832454383373,0.994876265525818,0.0910795629024506,-0.0438832454383373,0.994876265525818,-1,0,0,-1,0,0,-1,0,0,0.0910795629024506,0.0438832454383373,-0.994876265525818,0.0910795629024506,0.0438832454383373,-0.994876265525818,0.0910795629024506,0.0438832454383373,-0.994876265525818,-1,0,0,-1,0,0,-1,0,0,0.780868828296661,0,0.624695062637329,0.780868828296661,0,0.624695062637329,0.780868828296661,0,0.624695062637329,0.120221391320229,-0.133769661188126,0.983693301677704,0.0910798385739326,0.0438863448798656,0.994876146316528,0.073239304125309,0.163757547736168,0.983778178691864,0.988482892513275,-0.1495491117239,-0.0231682825833559,1,0,0,0.988482892513275,0.1495491117239,0.0231682825833559,0.073239304125309,-0.163757547736168,-0.983778178691864,0.0910798385739326,-0.0438863448798656,-0.994876146316528,0.120221391320229,0.133769661188126,-0.983693301677704,0.988482892513275,0.149548903107643,-0.0231687556952238,1,0,0,0.988482892513275,-0.149548903107643,0.0231687556952238,0.780868828296661,0,-0.624695062637329,0.780868828296661,0,-0.624695062637329,0.780868828296661,0,-0.624695062637329,0.624695062637329,0,0.780868768692017,0.624695062637329,0,0.780868768692017,0.624695062637329,0,0.780868768692017,-0.624695062637329,0,0.780868768692017,-0.624695062637329,0,0.780868768692017,-0.624695062637329,0,0.780868768692017,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-0,8.63255322260115e-13,1,-0,8.63255322260115e-13,1,-0,1.72651064452023e-12,1,-0,8.63255322260115e-13,1,-0,8.63255322260115e-13,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,-8.63255322260115e-13,-1,-0,-8.63255322260115e-13,-1,-0,-1.72651064452023e-12,-1,-0,-8.63255322260115e-13,-1,-0,-8.63255322260115e-13,-1,-0,0,-1,0.988482892513275,0.149548903107643,-0.0231687556952238,0.988482892513275,-0.1495491117239,-0.0231682825833559,0.988482892513275,0.1495491117239,0.0231682825833559,0.988482892513275,-0.1495491117239,-0.0231682825833559,0.988482892513275,0.149548903107643,-0.0231687556952238,0.988482892513275,-0.149548903107643,0.0231687556952238,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			TangentsW: *84 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *144 {
				a: -0.0199999809265137,-0.106702759861946,0.0199999809265137,-0.106702759861946,0.0199999809265137,-0.261033564805984,-0.0199999809265137,-0.261033564805984,0.0199999809265137,-0.285124123096466,0.0199999809265137,-0.261033564805984,0.0325000882148743,-0.273078888654709,-0.0199999809265137,-0.261033564805984,-0.0199999809265137,-0.285124123096466,-0.0325000882148743,-0.273078888654709,-0.0199999809265137,-0.106702074408531,0.0199999809265137,-0.106702789664268,0.0199997425079346,-0.261033594608307,-0.0200002193450928,-0.261033594608307,0.0199997425079346,-0.285124123096466,0.0199997425079346,-0.261033594608307,0.0324998497962952,-0.273078858852386,-0.0200002193450928,-0.261033594608307,-0.0200002193450928,-0.285124242305756,-0.0325000882148743,-0.273078918457031,-0.0199999809265137,-0.0453906618058681,-0.0199999809265137,-0.0303222760558128,0.0199999809265137,-0.0303222760558128,0.0199999809265137,-0.0453906618058681,0.0183417722582817,-0.261033594608307,0.0183417536318302,-0.106702789664268,0.0215308740735054,-0.106702074408531,0.0215308926999569,-0.261033594608307,0.0325000882148743,-0.273078888654709,-0.0199999809265137,0.0199997425079346,-0.0199999809265137,-0.0200002193450928,-0.0325000882148743,-0.273078918457031,-0.0199999809265137,-0.0200002193450928,0.0199999809265137,-0.0200002193450928,-0.0325000882148743,-0.273078888654709,0.0199999809265137,-0.0200002193450928,0.0199999809265137,0.0199997425079346,0.0199999809265137,0.0199997425079346,-0.0199999809265137,0.0199997425079346,0.0324998497962952,-0.273078858852386,-0.0199999809265137,-0.261033594608307,-0.0199999809265137,-0.285124242305756,0.0325000882148743,-0.273078888654709,0.0325000882148743,-0.273078888654709,0.0199999809265137,-0.0200010687112808,0.0199999809265137,0.0199988931417465,0.0199999809265137,0.0199988931417465,0.0199999809265137,-0.0200010687112808,-0.0199999809265137,-0.0200010687112808,-0.0199999809265137,-0.0200010687112808,0.0199999809265137,-0.0200010687112808,-0.0325000882148743,-0.273078918457031,-0.0199999809265137,
0.0199988931417465,-0.0199999809265137,0.0199988931417465,-0.0199999809265137,-0.0200010687112808,-0.0325000882148743,-0.273078888654709,0.0199999809265137,0.0199988931417465,-0.0199999809265137,0.0199988931417465,0.0324998497962952,-0.273078858852386,0.0199999809265137,-0.285124242305756,0.0199999809265137,-0.261033594608307,-0.0325000882148743,-0.273078888654709,0.0200002193450928,-0.285124242305756,0.0200002193450928,-0.261033594608307,-0.0325000882148743,-0.273078918457031,-0.0199997425079346,-0.261033594608307,-0.0199997425079346,-0.285124123096466,0.0324998497962952,-0.273078858852386,0.0199999809265137,0.0200018882751465,-0.0199999809265137,0.0200018882751465,-0.0199999809265137,-0.0199980735778809,0.0199999809265137,-0.0199980735778809
			} 
			UVIndex: *84 {
				a: 4,6,5,7,9,8,14,16,15,17,19,18,29,28,30,32,31,33,35,34,36,37,39,38,40,42,41,44,43,45,49,51,50,53,55,54,56,58,57,59,61,60,62,64,63,65,67,66,0,2,1,2,0,3,10,12,11,12,10,13,20,22,21,22,20,23,24,26,25,26,24,27,46,48,47,48,46,52,71,69,70,69,71,68
			} 
		}
		LayerElementUV: 1 {
			Version: 101
			Name: "UVSet2"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *144 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			} 
			UVIndex: *84 {
				a: 4,6,5,7,9,8,14,16,15,17,19,18,29,28,30,32,31,33,35,34,36,37,39,38,40,42,41,44,43,45,49,51,50,53,55,54,56,58,57,59,61,60,62,64,63,65,67,66,0,2,1,2,0,3,10,12,11,12,10,13,20,22,21,22,20,23,24,26,25,26,24,27,46,48,47,48,46,52,71,69,70,69,71,68
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		Layer: 1 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 1
			}
		}
	}
	Model: 2283759424256, "Model::Mini_Carrier_Handle_Model", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2275668484432, "Material::Black_Metal", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0.377358466386795,0.377358466386795,0.377358466386795
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0.377358466386795,0.377358466386795,0.377358466386795
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mini_Carrier_Handle_Model, Model::RootNode
	C: "OO",2283759424256,0
	
	;Material::Black_Metal, Model::Mini_Carrier_Handle_Model
	C: "OO",2275668484432,2283759424256
	
	;Geometry::Scene, Model::Mini_Carrier_Handle_Model
	C: "OO",2280193598800,2283759424256
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
