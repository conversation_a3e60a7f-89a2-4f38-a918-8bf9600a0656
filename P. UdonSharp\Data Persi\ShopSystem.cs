﻿using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class ShopSystem : UdonSharpBehaviour
{
    //Required Inputs
    public GameObject SpawnMenuSystemObject, PlayerSystemObject;
    public GameObject PurchaseButton, OwnedButton, PurchaseButton2, OwnedButton2;
    public TextMeshPro Item1PriceText, Item2PriceText;
    public GameObject CannotAfford1, CannotAfford2;

    //Items, Costs and Images
    public int Item1, Item2, ItemPrice1, ItemPrice2;
    public int[] ItemsPriceMin, ItemsPriceMax, ItemsPriceFixed;
    public int[] ItemRotation1, ItemRotation2;
    public Image Item1Image, Item2Image;
    public Sprite[] ItemsSprite;
    public Color[] OptionalImageColor;
    public string[] ItemName;

    public int PlayerMoney;

    //Required
    public GameObject Menu;
    public bool PlayerInProximity;
    private VRCPlayerApi localPlayer;
    public LayerMask whatIsPlayer;

    //Settings
    public bool HasPredeterminedPrices1, HasPredeterminedPrices2;

    private void Start()
    {
        SpawnMenuSystemObject = GameObject.Find("SpawnMenuSystem");
        PlayerSystemObject = GameObject.Find("PLAYER_MAINSYSTEM");
        localPlayer = Networking.LocalPlayer;

        bool playerInSightRange = Physics.CheckSphere(transform.position, 15f, whatIsPlayer);

        if(playerInSightRange){Menu.SetActive(true);}
        else{Menu.SetActive(false);}
        SendCustomEventDelayedSeconds(nameof(RefreshShop), 1f);
        SendCustomEventDelayedSeconds(nameof(ItemUnlockedCheck), 1f);
    }

    private void OnEnable()
    {
        int RandomLength = Random.Range(0, 3);
        if(RandomLength == 0){Item1 = ItemRotation1[0];}
        else if(RandomLength == 1){Item1 = ItemRotation1[1];}
        else if(RandomLength == 2){Item1 = ItemRotation1[2];}
        int RandomLength2 = Random.Range(3, 6);
        if(RandomLength2 == 3){Item2 = ItemRotation2[0];}
        else if(RandomLength2 == 4){Item2 = ItemRotation2[1];}
        else if(RandomLength2 == 5){Item2 = ItemRotation2[2];}

        if(HasPredeterminedPrices1 == false){ItemPrice1 = Random.Range(ItemsPriceMin[0], ItemsPriceMax[0]);}
        else{ItemPrice1 = ItemsPriceFixed[RandomLength];}

        if(HasPredeterminedPrices2 == false){ItemPrice2 = Random.Range(ItemsPriceMin[1], ItemsPriceMax[1]);}
        else{ItemPrice2 = ItemsPriceFixed[RandomLength2];}

        Item1Image.sprite = ItemsSprite[RandomLength];
        Item2Image.sprite = ItemsSprite[RandomLength2];

        if(OptionalImageColor[RandomLength] != null){Item1Image.color = OptionalImageColor[RandomLength];}
        else{Item1Image.color = Color.white;}

        if(OptionalImageColor[RandomLength2] != null){Item2Image.color = OptionalImageColor[RandomLength2];}
        else{Item2Image.color = Color.white;}

        Item1PriceText.text = ItemName[RandomLength] + " for " + ItemPrice1.ToString();
        Item2PriceText.text = ItemName[RandomLength2] + " for " + ItemPrice2.ToString();

        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            if(spawnMenuDataSystem != null){
                if(spawnMenuDataSystem.IsItemUnlocked(Item1)){
                    PurchaseButton.SetActive(false);
                    OwnedButton.SetActive(true);
                }
                else{
                    PurchaseButton.SetActive(true);
                    OwnedButton.SetActive(false);
                }
                if(spawnMenuDataSystem.IsItemUnlocked(Item2)){
                    PurchaseButton2.SetActive(false);
                    OwnedButton2.SetActive(true);
                }
                else{
                    PurchaseButton2.SetActive(true);
                    OwnedButton2.SetActive(false);
                }
            }
        }
    }
    public void RefreshShop(){
        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            if(spawnMenuDataSystem != null){
                if(spawnMenuDataSystem.IsItemUnlocked(Item1)){
                    PurchaseButton.SetActive(false);
                    OwnedButton.SetActive(true);
                }
                else{
                    PurchaseButton.SetActive(true);
                    OwnedButton.SetActive(false);
                }
                if(spawnMenuDataSystem.IsItemUnlocked(Item2)){
                    PurchaseButton2.SetActive(false);
                    OwnedButton2.SetActive(true);
                }
                else{
                    PurchaseButton2.SetActive(true);
                    OwnedButton2.SetActive(false);
                }
            }
        }
    }

    public void ItemUnlockedCheck(){
        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            if(spawnMenuDataSystem != null){
                if(spawnMenuDataSystem.IsItemUnlocked(Item1)){
                    PurchaseButton.SetActive(false);
                    OwnedButton.SetActive(true);
                }
                else{
                    PurchaseButton.SetActive(true);
                    OwnedButton.SetActive(false);
                }
                if(spawnMenuDataSystem.IsItemUnlocked(Item2)){
                    PurchaseButton2.SetActive(false);
                    OwnedButton2.SetActive(true);
                }
                else{
                    PurchaseButton2.SetActive(true);
                    OwnedButton2.SetActive(false);
                }
            }
        }
        SendCustomEventDelayedSeconds(nameof(ItemUnlockedCheck), 5f);
    }

    public void PurchaseItem1(){
        PlayerMoney = PlayerData.GetInt(localPlayer, "EmperCoins");


        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            MainPlayerCurrencySystem mainPlayerCurrencySystem = PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>();
            if(spawnMenuDataSystem != null && mainPlayerCurrencySystem != null && PlayerMoney >= ItemPrice1 && spawnMenuDataSystem.IsItemUnlocked(Item1) == false){
                spawnMenuDataSystem.UnlockItem(Item1);
                mainPlayerCurrencySystem.RemovePoints(ItemPrice1);
                PurchaseButton.SetActive(false);
                OwnedButton.SetActive(true);
            }
            else{
                if(spawnMenuDataSystem.IsItemUnlocked(Item1) == true){return;}
                CannotAfford1.SetActive(true);
                SendCustomEventDelayedSeconds(nameof(CloseCannotAfford1), 1f);
            }
        }
    }
    public void PurchaseItem2(){
        PlayerMoney = PlayerData.GetInt(localPlayer, "EmperCoins");


        if(SpawnMenuSystemObject != null){
            SpawnMenuDataSystem spawnMenuDataSystem = SpawnMenuSystemObject.GetComponent<SpawnMenuDataSystem>();
            MainPlayerCurrencySystem mainPlayerCurrencySystem = PlayerSystemObject.GetComponent<MainPlayerCurrencySystem>();
            if(spawnMenuDataSystem != null && mainPlayerCurrencySystem != null && PlayerMoney >= ItemPrice2 && spawnMenuDataSystem.IsItemUnlocked(Item2) == false){
                spawnMenuDataSystem.UnlockItem(Item2);
                mainPlayerCurrencySystem.RemovePoints(ItemPrice2);
                PurchaseButton2.SetActive(false);
                OwnedButton2.SetActive(true);
            }
            else{
                if(spawnMenuDataSystem.IsItemUnlocked(Item2) == true){return;}
                CannotAfford2.SetActive(true);
                SendCustomEventDelayedSeconds(nameof(CloseCannotAfford2), 1f);
            }
        }
    }

    public void CloseCannotAfford1(){CannotAfford1.SetActive(false);}
    public void CloseCannotAfford2(){CannotAfford2.SetActive(false);}

    #region CollisionFunctions
    //Collision Related
    public override void OnPlayerTriggerEnter(VRCPlayerApi player)
    {
        if(player != localPlayer)return;
        Menu.SetActive(true);
        PlayerInProximity = true;
    }
    public override void OnPlayerTriggerExit(VRCPlayerApi player)
    {
        if(player != localPlayer)return;
        Menu.SetActive(false);
        PlayerInProximity = false;
    }
    #endregion CollisionFunctions
}
