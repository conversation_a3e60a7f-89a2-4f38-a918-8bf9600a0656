using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.SDK3.Persistence;
using TMPro;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class MainPlayerCurrencySystem : UdonSharpBehaviour
{
    private int currentPoints = 0;
    private const string POINTS_KEY = "EmperCoins";
    public TextMeshProUGUI pointsText;

    public override void OnPlayerRestored(VRCPlayerApi player)
    {
        if (player.isLocal)
        {
            LoadPlayerPoints();
        }
    }

    public void AddPoints(int pointsToAdd)
    {
        currentPoints += pointsToAdd;
        SavePlayerPoints();
        UpdateUI();
    }

    public void RemovePoints(int pointsToRemove)
    {
        currentPoints = Mathf.Max(0, currentPoints - pointsToRemove);
        SavePlayerPoints();
        UpdateUI();
    }

    private void SavePlayerPoints()
    {
        PlayerData.SetInt(POINTS_KEY, currentPoints);
    }

    private void LoadPlayerPoints()
    {
        currentPoints = PlayerData.GetInt(Networking.LocalPlayer, POINTS_KEY);
        UpdateUI();
    }

    private void UpdateUI()
    {
        if (pointsText != null)
        {
            pointsText.text = currentPoints.ToString();
        }
    }
}