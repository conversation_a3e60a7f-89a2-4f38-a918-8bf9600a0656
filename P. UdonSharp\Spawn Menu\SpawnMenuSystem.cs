using TMPro;
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.Data;
using VRC.SDKBase;
using VRC.Udon.Common;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class SpawnMenuSystem : UdonSharpBehaviour
{
    public PlayerWorldStartSystem PlayerWorldStartSystem;

    public GameObject MenuContainer;
    public MeshRenderer CanvasVisibleChecker;
    public float lastVertical;
    public bool MaximizedMode,MinimizedMode,SpawnerInMenu,UpdatedInSpawnMenu;
    private VRCPlayerApi localPlayer;
    private float lastToggleTime = 0f;

    public GameObject[] Panels;

    public GameObject[] Props,Misc,Weapons;
    public GameObject[] proplimit;
    public GameObject LimitReachedText;
    public Transform spawnpoint,SpawnMenutransform;
    public GameObject MinimizedMenu;
    public Collider PickupCollider;

    //Classes
    public Slider[] ClassSliders;
    public int[] ClassCooldowns;
    public Image ButtonImage;
    public Sprite[] ClassSprites;
    public TextMeshProUGUI[] ClassText;
    
    //Extra Settings
    public GameObject[] Tips;
    public int CustomColorSelected;
    public Image[] CustomizableImage;
    public Color[] CustomizableColor;
    public TextMeshProUGUI CustomizableColorText;
    public string[] CustomizableColorString;
    public bool KeyReactivate;

    //Spawn Sound
    public AudioSource AudioSource;
    public AudioClip SpawnSound,buzz,RefreshSound,ClickSound;

    //PlayerStats
    public Slider[] WalkSlider,RunSlider,StrafeSlider,JumpImpulseSlider,GravitySlider;
    public TextMeshProUGUI[] WalkText,RunText,StrafeText,JumpImpulseText,GravityText;


    void Start(){
        PickupCollider.enabled = false;
        LimitReachedText.SetActive(false);
        CanvasVisibleChecker.enabled = false; 
        MinimizedMode = true;
        KeyReactivate = true;
        spawnpoint.gameObject.SetActive(false);
        MenuContainer.SetActive(false);
        MinimizedMenu.SetActive(true);
        localPlayer = Networking.LocalPlayer;

        for (int i = 0; i < Panels.Length; i++){Panels[i].SetActive(false);}
        Panels[0].SetActive(true);

        for (int i = 0; i < CustomizableImage.Length; i++){CustomizableImage[i].color = CustomizableColor[CustomColorSelected];}
        CustomizableColorText.text = CustomizableColorString[CustomColorSelected];

        //Class stuff
        for (int i = 0; i < ClassCooldowns.Length; i++){
            ClassCooldowns[i] = 120;
        }
        for (int i = 0; i < ClassSliders.Length; i++){
            ClassSliders[i].maxValue = ClassCooldowns[i];
            ClassSliders[i].minValue = 0;
            ClassSliders[i].value = ClassCooldowns[i];
        }
        
        SendCustomEventDelayedSeconds(nameof(UpdateTimer), 1f);
    }

    public void UpdateTimer()
    {
        if (Input.GetKey(KeyCode.Q) && !MaximizedMode && Time.time - lastToggleTime > 1f){
            if (MenuContainer.activeSelf) {
                ToggleMenu(); // This will close the menu
                lastToggleTime = Time.time;
            }
            else if (!MenuContainer.activeSelf) {
                ToggleMenu();
                CanvasVisibleChecker.enabled = true;
                PickupCollider.enabled = true;
                spawnpoint.gameObject.SetActive(true);
                MinimizedMode = false;
                MinimizedMenu.SetActive(false);
                lastToggleTime = Time.time;
            }
        }
        if (Input.GetKey(KeyCode.Q) && MaximizedMode){MaximizedModeToggle();}

        SpawnerParentBase();
        UpdateStats();

        if(MaximizedMode && !MinimizedMode){
            transform.localScale = Vector3.one * 3;
            Vector3 spawnOffset = localPlayer.GetRotation() * Vector3.forward * 1.5f;
            Vector3 targetPosition = localPlayer.GetPosition() + spawnOffset + new Vector3(0,1.5f,0);
            Quaternion targetRotation = localPlayer.GetRotation();

            transform.position = Vector3.Lerp(transform.position, targetPosition, Time.deltaTime * 16f);
            transform.rotation = Quaternion.Lerp(transform.rotation, targetRotation, Time.deltaTime * 16f);
            SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.01f);
        }
        else if(!MaximizedMode && MinimizedMode){
            transform.localScale = Vector3.one * 1;
            Vector3 spawnOffset = localPlayer.GetRotation() * Vector3.forward * 1.3f;
            Vector3 targetPosition = localPlayer.GetPosition() + spawnOffset + new Vector3(0,1.5f,0);
            Quaternion targetRotation = localPlayer.GetRotation();

            transform.position = Vector3.Lerp(transform.position, targetPosition, Time.deltaTime * 24f);
            transform.rotation = Quaternion.Lerp(transform.rotation, targetRotation, Time.deltaTime * 24f);
            SendCustomEventDelayedSeconds(nameof(UpdateTimer), 0.01f);
        }
        else{SendCustomEventDelayedSeconds(nameof(UpdateTimer), 1f);}
    }

    public override void InputLookVertical(float value, UdonInputEventArgs args)
    {
        if (!localPlayer.IsUserInVR()) return;
        
        if (lastVertical > -0.5f && value <= -0.5f && !MaximizedMode && Time.time - lastToggleTime > 1f){
            if (MenuContainer.activeSelf) {
                ToggleMenu(); // This will close the menu
                lastToggleTime = Time.time;
            } else {
                ToggleMenu();
                CanvasVisibleChecker.enabled = true;
                PickupCollider.enabled = true;
                spawnpoint.gameObject.SetActive(true);
                MinimizedMode = false;
                MinimizedMenu.SetActive(false);
                lastToggleTime = Time.time;
            }
        }
        else if (lastVertical > -0.5f && value <= -0.5f && MaximizedMode){
            MaximizedModeToggle();
            ToggleMenu(); 
            CanvasVisibleChecker.enabled = true; 
            PickupCollider.enabled = true; 
            spawnpoint.gameObject.SetActive(true); 
            MinimizedMode = false;
            MinimizedMenu.SetActive(false);
        }
        
        lastVertical = value;
    }

    private void ToggleMenu()
    {
        if (MenuContainer.activeSelf){
            CloseMenu();
            MinimizedMode = true;
            MenuContainer.SetActive(false);
            spawnpoint.gameObject.SetActive(false);
            MinimizedMenu.SetActive(true);
            PickupCollider.enabled = false;
        }
        else{SpawnAtPlayer();}
    }

    public void MaximizedModeToggle(){
        if(!MinimizedMode){
            MaximizedMode = !MaximizedMode; 
            if(!MaximizedMode){transform.localScale = Vector3.one * localPlayer.GetAvatarEyeHeightAsMeters();}
            Keysound();
        }
    }
    public void MinimizedModeToggle(){
        MinimizedMode = !MinimizedMode; 
        MenuContainer.SetActive(false);
        spawnpoint.gameObject.SetActive(false);
        MinimizedMenu.SetActive(true);
        PickupCollider.enabled = false;
        if(MaximizedMode){MaximizedMode = false;}
        Keysound();
    }

    private void SpawnAtPlayer()
    {
        MenuContainer.SetActive(true);
        transform.localScale = Vector3.one * localPlayer.GetAvatarEyeHeightAsMeters();
        Vector3 spawnOffset = localPlayer.GetRotation() * Vector3.forward * (1f * localPlayer.GetAvatarEyeHeightAsMeters());
        transform.position = localPlayer.GetPosition() + spawnOffset + (new Vector3(0,1f,0) * localPlayer.GetAvatarEyeHeightAsMeters());
        transform.rotation = localPlayer.GetRotation();
    }

    public void CloseMenu(){
        spawnpoint.gameObject.SetActive(false);
        MenuContainer.SetActive(false);
        PickupCollider.enabled = false;
    }

    #region Objects

    public void PropObject1(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[0]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject2(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[1]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject3(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[2]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject4(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[3]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject5(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[4]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject6(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[5]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject7(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[6]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject8(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[7]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject9(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[8]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject10(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[9]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject11(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[10]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject12(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[11]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject13(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[12]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject14(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[13]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject15(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[14]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject16(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[15]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject17(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[16]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject18(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[17]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject19(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[18]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject20(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[19]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject21(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[20]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject22(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[21]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject23(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[22]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject24(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[23]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject25(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[24]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject26(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[25]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject27(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[26]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject28(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[27]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject29(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[28]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject30(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[29]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject31(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[30]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker(); 
    }
    public void PropObject32(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[31]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject33(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[32]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject34(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[33]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject35(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[34]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject36(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[35]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject37(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[36]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject38(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[37]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject39(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[38]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject40(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[39]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject41(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[40]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject42(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[41]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void PropObject43(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Props[42]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    #endregion Objects
    #region Misc

    public void MiscObject1(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[0]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject2(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                int RandomPrefab = Random.Range(0, 2);
                if(RandomPrefab == 0){
                    var prop = VRCInstantiate(Misc[1]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 1){
                    var prop = VRCInstantiate(Misc[25]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject3(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[2]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject4(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[3]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject5(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[4]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject6(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[5]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject7(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[6]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject8(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[7]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject9(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[8]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject10(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[9]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject11(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[10]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject12(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[11]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject13(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[12]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject14(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[13]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject15(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[14]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject16(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[15]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject17(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[16]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject18(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[17]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject19(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[18]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject20(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[19]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject21(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[20]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject22(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[21]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject23(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[22]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject24(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[23]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = new Quaternion(0, 0, 0, 0);
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject25(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[24]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject26(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[26]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject27(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[27]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject28(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                int RandomPrefab = Random.Range(0, 3);
                if(RandomPrefab == 0){
                    var prop = VRCInstantiate(Misc[28]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 1){
                    var prop = VRCInstantiate(Misc[29]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 2){
                    var prop = VRCInstantiate(Misc[30]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject29(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[31]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject30(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[32]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject31(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[33]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject32(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[34]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject33(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[35]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject34(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[36]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject35(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[37]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject36(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[38]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject37(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                int RandomPrefab = Random.Range(0, 3);
                if(RandomPrefab == 0){
                    var prop = VRCInstantiate(Misc[39]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 1){
                    var prop = VRCInstantiate(Misc[40]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 2){
                    var prop = VRCInstantiate(Misc[41]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject38(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                int RandomPrefab = Random.Range(0, 3);
                if(RandomPrefab == 0){
                    var prop = VRCInstantiate(Misc[42]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 1){
                    var prop = VRCInstantiate(Misc[43]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 2){
                    var prop = VRCInstantiate(Misc[44]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject39(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Misc[45]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject40(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                int RandomPrefab = Random.Range(0, 3);
                if(RandomPrefab == 0){
                    var prop = VRCInstantiate(Misc[46]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 1){
                    var prop = VRCInstantiate(Misc[47]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 2){
                    var prop = VRCInstantiate(Misc[48]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject41(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                int RandomPrefab = Random.Range(0, 3);
                if(RandomPrefab == 0){
                    var prop = VRCInstantiate(Misc[49]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 1){
                    var prop = VRCInstantiate(Misc[50]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 2){
                    var prop = VRCInstantiate(Misc[51]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                break;
            }
        }
        LimitChecker();
    }
    public void MiscObject42(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                int RandomPrefab = Random.Range(0, 3);
                if(RandomPrefab == 0){
                    var prop = VRCInstantiate(Misc[52]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 1){
                    var prop = VRCInstantiate(Misc[53]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                else if(RandomPrefab == 2){
                    var prop = VRCInstantiate(Misc[54]);
                    prop.transform.position = spawnpoint.position;
                    prop.transform.rotation = spawnpoint.rotation;
                    proplimit[i] = prop;
                }
                break;
            }
        }
        LimitChecker();
    }
    #endregion Misc
    #region Weapons

    public void WeaponObject1(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[0]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject2(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[1]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject3(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[2]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject4(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[3]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject5(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[4]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject6(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[5]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject7(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[6]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject8(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[7]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject9(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[8]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject10(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[9]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject11(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[10]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject12(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[11]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject13(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[12]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject14(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[13]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject15(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[14]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject16(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[15]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject17(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[16]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject18(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[17]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject19(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[18]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject20(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[19]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject21(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[20]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject22(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[21]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject23(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[22]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject24(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[23]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject25(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[24]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject26(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[25]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject27(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[26]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject28(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[27]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject29(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[28]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject30(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[29]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject31(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[30]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject32(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[31]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject33(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[32]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject34(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[33]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject35(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[34]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject36(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[35]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject37(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[36]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject38(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[37]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject39(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[38]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    public void WeaponObject40(){
        for (int i = 0; i < proplimit.Length; i++){
            if (proplimit[i] == null){
                AudioSource.PlayOneShot(SpawnSound);

                var prop = VRCInstantiate(Weapons[39]);
                prop.transform.position = spawnpoint.position;
                prop.transform.rotation = spawnpoint.rotation;
                proplimit[i] = prop;
                break;
            }
        }
        LimitChecker();
    }
    #endregion Weapons

    #region Classes

    public void Class1Activate(){
        if(ClassCooldowns[0] >= 120){
            ButtonImage.sprite = ClassSprites[0];
            ClassCooldowns[0] = 0;
            ClassSliders[0].value = 0;

            PlayerWorldStartSystem.Class1();

            for (int i = 0; i < ClassText.Length; i++){
                ClassText[i].text = "[Choose Module]";
            }
            ClassText[0].text = "[Equipped]";

            Keysound();
            SendCustomEventDelayedSeconds(nameof(Class1OnCooldown), 1f);
        }
        else{
            Buzzsound();
        }
    }
    public void Class1OnCooldown(){
        if(ClassCooldowns[0] < 120){
            ClassCooldowns[0]++;
            ClassSliders[0].value = ClassCooldowns[0];
            SendCustomEventDelayedSeconds(nameof(Class1OnCooldown), 1f);
        }
    }
    public void Class2Activate(){
        if(ClassCooldowns[1] >= 120){
            ButtonImage.sprite = ClassSprites[1];
            ClassCooldowns[1] = 0;
            ClassSliders[1].value = 0;

            PlayerWorldStartSystem.Class2();

            for (int i = 0; i < ClassText.Length; i++){
                ClassText[i].text = "[Choose Module]";
            }
            ClassText[1].text = "[Equipped]";

            Keysound();
            SendCustomEventDelayedSeconds(nameof(Class2OnCooldown), 1f);
        }
        else{
            Buzzsound();
        }
    }
    public void Class2OnCooldown(){
        if(ClassCooldowns[1] < 120){
            ClassCooldowns[1]++;
            ClassSliders[1].value = ClassCooldowns[1];
            SendCustomEventDelayedSeconds(nameof(Class2OnCooldown), 1f);
        }
    }
    public void Class3Activate(){
        if(ClassCooldowns[2] >= 120){
            ButtonImage.sprite = ClassSprites[2];
            ClassCooldowns[2] = 0;
            ClassSliders[2].value = 0;

            PlayerWorldStartSystem.Class3();

            for (int i = 0; i < ClassText.Length; i++){
                ClassText[i].text = "[Choose Module]";
            }
            ClassText[2].text = "[Equipped]";

            Keysound();
            SendCustomEventDelayedSeconds(nameof(Class3OnCooldown), 1f);
        }
        else{
            Buzzsound();
        }
    }
    public void Class3OnCooldown(){
        if(ClassCooldowns[2] < 120){
            ClassCooldowns[2]++;
            ClassSliders[2].value = ClassCooldowns[2];
            SendCustomEventDelayedSeconds(nameof(Class3OnCooldown), 1f);
        }
    }

    #endregion Classes


    #region Other Required Functions

    //Spawner Stuff
    public void ReturnSpawnPoint(){
        AudioSource.PlayOneShot(RefreshSound);
        spawnpoint.position = transform.position;
        spawnpoint.rotation = transform.rotation;
    }
    public void ToggleSpawnerParent(){
        SpawnerInMenu = !SpawnerInMenu;
        UpdatedInSpawnMenu = false;
        Keysound();
    }
    void SpawnerParentBase(){
        if(SpawnerInMenu && !UpdatedInSpawnMenu){
            spawnpoint.parent = SpawnMenutransform;
            UpdatedInSpawnMenu = true;
        }
        else if(!SpawnerInMenu && !UpdatedInSpawnMenu){
            Vector3 OldLocation = spawnpoint.position;
            spawnpoint.parent = null;
            spawnpoint.position = OldLocation;
            UpdatedInSpawnMenu = true;
        }
    }
    public void ChangeColorButton(){
        CustomColorSelected++;
        if(CustomColorSelected > CustomizableColor.Length - 1){CustomColorSelected = 0;}
        for (int i = 0; i < CustomizableImage.Length; i++){
            CustomizableImage[i].color = CustomizableColor[CustomColorSelected];
        }
        CustomizableColorText.text = CustomizableColorString[CustomColorSelected];
    }
    //PlayerStatsStuff
    public void UpdateStats(){
        for (int i = 0; i < WalkText.Length; i++){
            float GetWalkSpeedValue = localPlayer.GetWalkSpeed();
            WalkText[i].text = GetWalkSpeedValue.ToString();
            WalkSlider[i].value = GetWalkSpeedValue;

            float GetRunSpeedValue = localPlayer.GetRunSpeed();
            RunText[i].text = GetRunSpeedValue.ToString();
            RunSlider[i].value = GetRunSpeedValue;

            float GetStrafeSpeedValue = localPlayer.GetStrafeSpeed();
            StrafeText[i].text = GetStrafeSpeedValue.ToString();
            StrafeSlider[i].value = GetStrafeSpeedValue;

            float GetJumpImpulseValue = localPlayer.GetJumpImpulse();
            JumpImpulseText[i].text = GetJumpImpulseValue.ToString();
            JumpImpulseSlider[i].value = GetJumpImpulseValue;

            float GetGravityStrengthValue = localPlayer.GetGravityStrength();  
            GravityText[i].text = GetGravityStrengthValue.ToString();
            GravitySlider[i].value = GetGravityStrengthValue;
        }
    }
    public void TipsToggle(){
        for (int i = 0; i < Tips.Length; i++){
            Tips[i].SetActive(!Tips[i].activeSelf);
        }
    }

    void LimitChecker()
    {
        bool allFull = true;

        for (int i = 0; i < proplimit.Length; i++)
        {
            if (proplimit[i] == null){allFull = false; break;}
        }

        if (allFull){LimitReachedText.SetActive(true); Buzzsound();}
        else{LimitReachedText.SetActive(false);}
    }
    //Delete
    public void DeleteObjects(){
        for (int i = 0; i < proplimit.Length; i++){if (proplimit[i] != null){Destroy(proplimit[i]);}}
        LimitReachedText.SetActive(false);
        AudioSource.PlayOneShot(RefreshSound);
    }
    //DeleteLast
    public void DeleteLastObject(){
        for (int i = proplimit.Length - 1; i >= 0; i--){
            if (proplimit[i] != null){
                Destroy(proplimit[i]);
                break;
            }
        }
        LimitReachedText.SetActive(false);
        AudioSource.PlayOneShot(RefreshSound);
    }
    //Generic
    public void Keysound(){
        if(KeyReactivate){
            AudioSource.PlayOneShot(ClickSound);
            KeyReactivate = false;
        }
        SendCustomEventDelayedSeconds(nameof(KeyReactivateDelay), 0.4f);
    }
    public void KeyReactivateDelay(){KeyReactivate = true;}

    public void Buzzsound(){AudioSource.PlayOneShot(buzz);}
    public void DeleteEverything(){Destroy(gameObject);}

    #endregion Other Required Functions
}
