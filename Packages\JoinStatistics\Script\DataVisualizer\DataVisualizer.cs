
using System;
using System.Globalization;
using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDK3.Data;
using VRC.SDKBase;
using VRC.Udon;

namespace Oikki.JoinStats
{
    public class DataVisualizer : UdonSharpBehaviour
    {
        public Transform plotLabelContainerX;
        public Transform plotLabelContainerY;
        public GameObject plotYvaluePrefab;
        public GameObject plotXvaluePrefab;
        public LineRenderer lineRenderer;

        public float graphMaxX = 100;
        public float graphMaxY = 60;

        public int maxYvalues = 4;

        void Start()
        {
        }

        private void OnValidate()
        {
#if !COMPILER_UDONSHARP && UNITY_EDITOR
            foreach (TimeZoneInfo z in TimeZoneInfo.GetSystemTimeZones())
            {
                Debug.Log(z.Id);
            }
#endif
        }


        public void Visualize(DataList dataList, TimePeriod timePeriod)
        {
            if (dataList.Count <= 1)
            {
                return;
            }

            RemoveLabels();
            int maxValue = 0;

            int[] data = new int[dataList.Count];

            for (int i = 0; i < dataList.Count; i++)
            {
                if (!dataList.TryGetValue(i, TokenType.Double, out DataToken valueToken))
                {
                    return;
                }
                int value = Mathf.RoundToInt((float)valueToken.Double);
                data[i] = value;
                if (value > maxValue)
                {
                    maxValue = value;
                }
            }

            GetNiceMaxValue(maxValue, out int increment, out int labelAmount);
            int niceMaxValue = increment * labelAmount;

            Vector3[] dataPositions = new Vector3[dataList.Count];



            for (int i = 0; i < dataList.Count; i++)
            {
                int index = dataList.Count - 1 - i;
                if (!dataList.TryGetValue(i, TokenType.Double, out DataToken valueToken))
                {
                    Debug.LogError($"fail {valueToken.TokenType}");
                    return;
                }

                int value = Mathf.RoundToInt((float)valueToken.Double);

                float posX = (index / (float)(dataList.Count - 1)) * graphMaxX;
                float posY = 0;
                if (niceMaxValue > 0)
                {
                    posY = (value / (float)niceMaxValue) * graphMaxY;
                }


                dataPositions[i] = new Vector3(posX, posY, 0);
            }

            for (int i = 0; i < labelAmount + 1; i++)
            {
                float posY = (i / (float)labelAmount) * graphMaxY;
                int value = i * increment;

                AddValueY(posY, value);
            }

            if (timePeriod == TimePeriod.今日)
            {
                for (int i = 0; i < 13; i++)
                {
                    DateTime now = DateTime.Now;
                    string adjustedTime = now.AddHours(-i * 2).ToString("%H", CultureInfo.InvariantCulture);

                    float xPos = ((12 - i) / (float)12) * graphMaxX;
                    AddValueX(xPos, adjustedTime);
                }
            }
            else if (timePeriod == TimePeriod.一週間)
            {
                for (int i = 0; i < 7; i++)
                {
                    DateTime now = DateTime.UtcNow.AddHours(9);
                    string dayName = now.AddDays(-i).ToString("ddd", CultureInfo.InvariantCulture);
                    dayName = TranslateDayToJapanese(dayName);
                    string adjustedTime = now.AddDays(-i).ToString("%d", CultureInfo.InvariantCulture);
                    float xPos = ((6 - i) / (float)6) * graphMaxX;
                    string fullLable = $"{adjustedTime} ({dayName})";
                    AddValueX(xPos, fullLable);
                }
            }
            else if (timePeriod == TimePeriod.一月)
            {
                for (int i = 0; i < 11; i++)
                {
                    DateTime now = DateTime.UtcNow.AddHours(9);
                    string adjustedTime = "Day " + now.AddDays(-i * 3).ToString("%d", CultureInfo.InvariantCulture);
                    float xPos = ((10 - i) / (float)10) * graphMaxX;
                    AddValueX(xPos, adjustedTime);
                }
            }
            else if (timePeriod == TimePeriod.一年)
            {
                for (int i = 0; i < 12; i++)
                {
                    DateTime now = DateTime.UtcNow.AddHours(9);
                    DateTime currentMonth = new DateTime(now.Year, now.Month, 1);
                    string adjustedTime = currentMonth.AddMonths(-i).ToString("%M", CultureInfo.InvariantCulture);
                    float xPos = ((11 - i) / (float)11) * graphMaxX;
                    AddValueX(xPos, adjustedTime);
                }
            }
            else if (timePeriod == TimePeriod.五年)
            {
                for (int i = 0; i < 5; i++)
                {
                    DateTime now = DateTime.UtcNow.AddHours(9);
                    DateTime currentYear = new DateTime(now.Year, 1, 1);
                    string adjustedTime = currentYear.AddYears(-i).ToString("yyyy", CultureInfo.InvariantCulture);
                    float xPos = ((4 - i) / (float)4) * graphMaxX;
                    AddValueX(xPos, adjustedTime);
                }
            }

            lineRenderer.positionCount = dataPositions.Length;
            lineRenderer.SetPositions(dataPositions);
        }

        public void RemoveLabels()
        {
            foreach (Transform child in plotLabelContainerY)
            {
                Destroy(child.gameObject);
            }
            foreach (Transform child in plotLabelContainerX)
            {
                Destroy(child.gameObject);
            }
        }

        public void AddValueX(float xPos, string label)
        {
            GameObject p = Instantiate(plotXvaluePrefab, plotLabelContainerX);
            Text text = p.GetComponentInChildren<Text>();
            p.transform.localPosition = new Vector3(xPos, 0, 0);
            text.text = label;
            text.transform.name = label;
        }

        public void AddValueY(float yPos, int value)
        {
            //Debug.Log(yPos + " " + value);
            GameObject p = Instantiate(plotYvaluePrefab, plotLabelContainerY);
            Text text = p.GetComponentInChildren<Text>();
            p.transform.localPosition = new Vector3(0, yPos, 0);
            text.text = value.ToString();
            text.transform.name = value.ToString();
        }

        public void GetNiceMaxValue(int maxValue, out int increment, out int howManyNiceValues)
        {
            int[] niceNumbers = new int[] { 1, 2, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000, 25000, 50000, 100000, 250000, 500000, 1000000 };
            increment = 99999999;
            foreach (int n in niceNumbers)
            {
                if (n * maxYvalues >= maxValue)
                {
                    increment = n;
                    break;
                }
            }

            howManyNiceValues = 0;

            for (int i = 0; i < maxYvalues; i++)
            {
                howManyNiceValues++;
                if ((i + 1) * increment >= maxValue)
                {
                    break;
                }
            }
        }

        private string[] englishDays = { "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat" };
        private string[] japaneseDays = { "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat" };
        public string TranslateDayToJapanese(string englishDay)
        {
            int index = Array.IndexOf(englishDays, englishDay);
            return index >= 0 ? japaneseDays[index] : englishDay; // Fallback in case of an error
        }
    }
}