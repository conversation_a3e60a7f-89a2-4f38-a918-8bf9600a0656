; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 5
		Day: 18
		Hour: 16
		Minute: 32
		Second: 28
		Millisecond: 183
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\eatumpdr_C01 Sword.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\eatumpdr_C01 Sword.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 1977802795456, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "Path", "KString", "XRefUrl", "", ""
				P: "RelPath", "KString", "XRefUrl", "", ""
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "ClipIn", "KTime", "Time", "",0
				P: "ClipOut", "KTime", "Time", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "Mute", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "InterlaceMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 1973328310528, "Geometry::Scene", "Mesh" {
		Vertices: *51 {
			a: 5.00001944601536,26.010075211525,4.99999448657036,-4.9999788403511,26.010075211525,4.99999448657036,2.22120434045792e-05,59.188449382782,-4.19095158576965e-06,-4.9999788403511,26.010075211525,-5.00000528991222,5.00001944601536,26.010075211525,-5.00000528991222,2.87869852036238,-46.0149109363556,2.87867374718189,-2.878656424582,-46.0149109363556,2.87867374718189,-2.878656424582,-46.0149109363556,-2.87868194282055,-2.878656424582,-51.7722725868225,2.87867374718189,-2.878656424582,-51.7722725868225,-2.87868194282055,2.87869852036238,-46.0149109363556,-2.87868194282055,2.87869852036238,-51.7722725868225,-2.87868194282055,6.41543567180634,-59.188449382782,-6.41541853547096,-6.41539245843887,-59.188449382782,-6.41541853547096,-6.41539245843887,-59.188449382782,6.41540959477425,6.41543567180634,-59.188449382782,6.41540959477425,2.87869852036238,-51.7722725868225,2.87867374718189
		} 
		PolygonVertexIndex: *90 {
			a: 0,2,-2,3,2,-5,2,0,-5,1,2,-4,5,1,-7,1,5,-1,3,6,-2,6,3,-8,7,8,-7,8,7,-10,4,7,-4,7,4,-11,10,9,-8,9,10,-12,10,0,-6,0,10,-5,12,14,-14,14,12,-16,5,11,-11,11,5,-17,6,16,-6,16,6,-9,11,13,-10,13,11,-13,16,12,-12,12,16,-16,9,14,-9,14,9,-14,8,15,-17,15,8,-15
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *270 {
				a: -0,0.1490179002285,0.988834500312805,-0,0.1490179002285,0.988834500312805,-0,0.1490179002285,0.988834500312805,-0,0.149017974734306,-0.988834500312805,-0,0.149017974734306,-0.988834500312805,-0,0.149017974734306,-0.988834500312805,0.988834500312805,0.149017840623856,0,0.988834500312805,0.149017840623856,0,0.988834500312805,0.149017840623856,0,-0.988834500312805,0.149017974734306,0,-0.988834500312805,0.149017974734306,0,-0.988834500312805,0.149017974734306,0,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0,-0.0294397994875908,0.999566555023193,-0.999566555023193,-0.0294398255646229,0,-0.999891638755798,-0.0147215081378818,0,-0.999566555023193,-0.0294398237019777,0,-0.999891638755798,-0.0147215081378818,0,-0.999566555023193,-0.0294398255646229,0,-0.999891638755798,-0.0147215081378818,0,-0.999891638755798,-0.0147215081378818,0,-1,0,0,-0.999891638755798,-0.0147215081378818,0,-1,0,0,-0.999891638755798,-0.0147215081378818,0,-1,0,0,-0,-0.0294398367404938,-0.999566555023193,-0,-0.0147215137258172,-0.999891638755798,-0,-0.0294398348778486,-0.999566555023193,-0,-0.0147215137258172,-0.999891638755798,-0,-0.0294398367404938,-0.999566555023193,-0,-0.0147215137258172,-0.999891638755798,-0,-0.0147215137258172,-0.999891638755798,-0,0,-1,-0,-0.0147215137258172,-0.999891638755798,-0,0,-1,-0,-0.0147215137258172,-0.999891638755798,-0,0,-1,0.999566555023193,-0.0294398013502359,0,0.999566555023193,-0.0294398013502359,0,0.999566555023193,-0.0294398032128811,0,0.999566555023193,-0.0294398013502359,0,0.999566555023193,-0.0294398013502359,0,0.999566555023193,-0.0294397994875908,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0.430451810359955,-0.902613639831543,-0,0.430451810359955,-0.902613639831543,-0,0.430451780557632,-0.902613639831543,-0,0.430451810359955,-0.902613639831543,
-0,0.430451810359955,-0.902613639831543,-0,0.430451810359955,-0.902613580226898,0.902613520622253,0.430451840162277,0,0.902613520622253,0.430451840162277,0,0.902613580226898,0.430451840162277,0,0.902613520622253,0.430451840162277,0,0.902613520622253,0.430451840162277,0,0.902613520622253,0.430451840162277,0,-0.902613639831543,0.43045175075531,0,-0.902613639831543,0.43045175075531,0,-0.902613639831543,0.43045175075531,0,-0.902613639831543,0.43045175075531,0,-0.902613639831543,0.43045175075531,0,-0.902613580226898,0.430451780557632,0,-0,0.430451720952988,0.902613639831543,-0,0.430451720952988,0.902613639831543,-0,0.430451691150665,0.902613639831543,-0,0.430451720952988,0.902613639831543,-0,0.430451720952988,0.902613639831543,-0,0.43045175075531,0.902613639831543
			} 
			NormalsW: *90 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *270 {
				a: 0,0.988834500312805,-0.1490179002285,0,0.988834500312805,-0.1490179002285,0,0.988834500312805,-0.1490179002285,0,0.988834500312805,0.149017974734306,0,0.988834500312805,0.149017974734306,0,0.988834500312805,0.149017974734306,-0.149017840623856,0.988834500312805,0,-0.149017840623856,0.988834500312805,0,-0.149017840623856,0.988834500312805,0,0.149017974734306,0.988834500312805,-0,0.149017974734306,0.988834500312805,-0,0.149017974734306,0.988834500312805,-0,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0,0.999566555023193,0.0294397994875908,-0.0294398255646229,0.999566555023193,-0,-0.0147215081378818,0.999891638755798,-0,-0.0294398237019777,0.999566555023193,-0,-0.0147215081378818,0.999891638755798,-0,-0.0294398255646229,0.999566555023193,-0,-0.0147215081378818,0.999891638755798,-0,-0.0147215081378818,0.999891638755798,-0,0,1,-0,-0.0147215081378818,0.999891638755798,-0,0,1,-0,-0.0147215081378818,0.999891638755798,-0,0,1,-0,0,0.999566555023193,-0.0294398367404938,0,0.999891638755798,-0.0147215137258172,0,0.999566555023193,-0.0294398348778486,0,0.999891638755798,-0.0147215137258172,0,0.999566555023193,-0.0294398367404938,0,0.999891638755798,-0.0147215137258172,0,0.999891638755798,-0.0147215137258172,0,1,-0,0,0.999891638755798,-0.0147215137258172,0,1,-0,0,0.999891638755798,-0.0147215137258172,0,1,-0,0.0294398013502359,0.999566555023193,-0,0.0294398013502359,0.999566555023193,-0,0.0294398032128811,0.999566555023193,-0,0.0294398013502359,0.999566555023193,-0,0.0294398013502359,0.999566555023193,-0,0.0294397994875908,0.999566555023193,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,0.902613639831543,0.430451810359955,0,0.902613639831543,0.430451810359955,0,0.902613639831543,0.430451780557632,0,0.902613639831543,0.430451810359955,0,0.902613639831543,0.430451810359955,
0,0.902613580226898,0.430451810359955,-0.430451840162277,0.902613520622253,0,-0.430451840162277,0.902613520622253,0,-0.430451840162277,0.902613580226898,0,-0.430451840162277,0.902613520622253,0,-0.430451840162277,0.902613520622253,0,-0.430451840162277,0.902613520622253,0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.43045175075531,0.902613639831543,-0,0.430451780557632,0.902613580226898,-0,0,0.902613639831543,-0.430451720952988,0,0.902613639831543,-0.430451720952988,0,0.902613639831543,-0.430451691150665,0,0.902613639831543,-0.430451720952988,0,0.902613639831543,-0.430451720952988,0,0.902613639831543,-0.43045175075531
			} 
			BinormalsW: *90 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *270 {
				a: 1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			} 
			TangentsW: *90 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *128 {
				a: 0.0964327603578568,0.719722509384155,0.011956837028265,0.719722509384155,0.0541948191821575,1,0.0964327901601791,0.719722509384155,0.0119568621739745,0.719722509384155,0.0541948080062866,1,0.0541948154568672,1,0.0964327976107597,0.719722509384155,0.0119568556547165,0.719722509384155,0.0964327752590179,0.719722509384155,0.011956837028265,0.719722509384155,0.0541948191821575,1,0.0785127133131027,0.111284703016281,0.029876908287406,0.111284703016281,0.011956837028265,0.719722509384155,0.0964327603578568,0.719722509384155,0.011956837028265,0.719722509384155,0.0964327752590179,0.719722509384155,0.0785127207636833,0.111284703016281,0.0298769157379866,0.111284703016281,0.0298769157379866,0.111284703016281,0.0785127207636833,0.111284703016281,0.0785127207636833,0.0626488551497459,0.0298769157379866,0.0626488551497459,0.0119568621739745,0.719722509384155,0.0964327901601791,0.719722509384155,0.0785127133131027,0.111284703016281,0.0298769194632769,0.111284703016281,0.0298769194632769,0.111284703016281,0.0785127133131027,0.111284703016281,0.0785127133131027,0.0626488551497459,0.0298769194632769,0.0626488551497459,0.0785127133131027,0.111284703016281,0.0298769120126963,0.111284703016281,0.0119568556547165,0.719722509384155,0.0964327976107597,0.719722509384155,0.108389630913734,0,0,0,0,0.108389630913734,0.108389630913734,0.108389630913734,0.0298769120126963,0.111284703016281,0.0785127133131027,0.111284703016281,0.0785127133131027,0.0626488551497459,0.0298769120126963,0.0626488551497459,0.029876908287406,0.111284703016281,0.0785127133131027,0.111284703016281,0.0785127133131027,0.0626488551497459,0.029876908287406,0.0626488551497459,0.0298769194632769,0.0626488551497459,0.0785127133131027,0.0626488551497459,0.108389630913734,0,0,0,0.0298769120126963,0.0626488551497459,0.0785127133131027,0.0626488551497459,0.108389630913734,0,0,0,0.0298769157379866,0.0626488551497459,0.0785127207636833,0.0626488551497459,0.108389630913734,0,0,0,0.029876908287406,0.0626488551497459,0.0785127133131027,0.0626488551497459,0.108389630913734,
0,0,0
			} 
			UVIndex: *90 {
				a: 0,2,1,3,5,4,6,8,7,9,11,10,12,14,13,14,12,15,16,18,17,18,16,19,20,22,21,22,20,23,24,26,25,26,24,27,28,30,29,30,28,31,32,34,33,34,32,35,36,38,37,38,36,39,40,42,41,42,40,43,44,46,45,46,44,47,48,50,49,50,48,51,52,54,53,54,52,55,56,58,57,58,56,59,60,62,61,62,60,63
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 1976290116208, "Model::C01_Sword", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Rotation", "Lcl Rotation", "", "A",-55,90,90
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1.00000011920929,1.00000035762787,1.00000011920929
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 1971661025552, "Material::Blue_Corr", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 1974184908336, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Blue grass 1.jpg"
			P: "RelPath", "KString", "XRefUrl", "", "..\Sprites\Blue grass 1.jpg"
		}
		UseMipMap: 0
		Filename: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Blue grass 1.jpg"
		RelativeFilename: "..\Sprites\Blue grass 1.jpg"
	}
	Texture: 1974184907856, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Sprites\Blue grass 1.jpg"
		RelativeFilename: "..\Sprites\Blue grass 1.jpg"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::C01_Sword, Model::RootNode
	C: "OO",1976290116208,0
	
	;Material::Blue_Corr, Model::C01_Sword
	C: "OO",1971661025552,1976290116208
	
	;Geometry::Scene, Model::C01_Sword
	C: "OO",1973328310528,1976290116208
	
	;Texture::DiffuseColor_Texture, Material::Blue_Corr
	C: "OO",1974184907856,1971661025552
	
	;Texture::DiffuseColor_Texture, Material::Blue_Corr
	C: "OP",1974184907856,1971661025552, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",1974184908336,1974184907856
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
