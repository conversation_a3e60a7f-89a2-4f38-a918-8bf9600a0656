﻿
using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

public class LocalTeleportWithFade : UdonSharpBehaviour
{
    public Transform teleportTarget;
    public MeshRenderer[] fade;
    public AudioSource audioSource;

    public float fadeDuration = 0.5f;
    public float postTeleportDelay = 1.52f; // How long to wait after playing sound before fading in

    public float fadeTimer = 0f;
    public int state = 0; // 0 = idle, 1 = fade out, 2 = teleport + play sound, 3 = delay after sound, 4 = fade in
    private VRCPlayerApi localPlayer;

    public void Teleport()
    {
        if (!Networking.LocalPlayer.IsValid()) return;

        localPlayer = Networking.LocalPlayer;
        fadeTimer = 0f;
        state = 1; // Start fading out
    }

    private void Update()
    {
        if (state == 0) return;

        fadeTimer += Time.deltaTime;

        if (state == 1) // Fade to black
        {
            float t = Mathf.Clamp01(fadeTimer / fadeDuration);
            SetFadeAlpha(t);
            if (t >= 1f)
            {
                // Fade complete → teleport and play sound
                fadeTimer = 0f;

                if (audioSource != null) audioSource.Play();
                state = 3; // Wait before fading back in
            }
        }
        else if (state == 3) // Wait after sound plays
        {
            if (fadeTimer >= postTeleportDelay)
            {
                fadeTimer = 0f;
                state = 4; // Start fading back in
            }
        }
        else if (state == 4) // Fade in
        {
            float t = Mathf.Clamp01(fadeTimer / fadeDuration);
            SetFadeAlpha(1f - t);
            if (t >= 1f)
            {
                state = 1; // Done
            }
        }
    }

    private void SetFadeAlpha(float alpha)
    {
        if (fade == null) return;
        for (int i = 0; i < fade.Length; i++)
        {
            fade[i].material.color = new Color(0f, 0f, 0f, alpha);
        }
    }

    public override void Interact()
    {
        Teleport();
    }
}
