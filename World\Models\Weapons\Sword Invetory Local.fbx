; FBX 7.7.0 project file
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1004
	FBXVersion: 7700
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 4
		Day: 10
		Hour: 4
		Minute: 58
		Second: 15
		Millisecond: 820
	}
	Creator: "FBX SDK/FBX Plugins version 2020.3.2"
	OtherFlags:  {
		TCDefinition: 127
	}
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "Created by FBX Exporter from Unity Technologies"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "Nodes Meshes Materials Textures Cameras Lights Skins Animation"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\xfpmusoa_Sword Invetory Local.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\VRC Projects\Konrusk Empire Kingdom\Assets\World\Models\xfpmusoa_Sword Invetory Local.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.2.1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2339829553520, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2339815069616, "Geometry::Scene", "Mesh" {
		Vertices: *102 {
			a: 1.99999809265137,-48.3571588993073,1.99997425079346,-1.99999809265137,-48.3571588993073,1.99997425079346,-1.99999809265137,-37.1904969215393,1.99999809265137,1.99999809265137,-37.1904969215393,1.99999809265137,-1.99999809265137,-48.3571588993073,-2.00002193450928,-1.99999809265137,-37.1904253959656,-1.99999809265137,1.99999809265137,-48.3571588993073,-2.00002193450928,1.99999809265137,-37.1904253959656,-1.99999809265137,-1.99999809265137,-25.523966550827,1.99999809265137,-1.99999809265137,26.6428530216217,1.99997425079346,1.99999809265137,26.6428530216217,1.99997425079346,1.99999809265137,-25.523966550827,1.99999809265137,-1.99999809265137,-25.5239188671112,-1.99999809265137,-1.99999809265137,26.6428530216217,-2.00002193450928,1.99999809265137,-25.5239188671112,-1.99999809265137,1.99999809265137,26.6428530216217,-2.00002193450928,5.28960227966309,-32.6493084430695,-1.99999809265137,12.7056121826172,-31.2140583992004,0,5.28960227966309,-29.7787964344025,-1.99997425079346,5.28960227966309,-29.7787964344025,2.00002193450928,5.28960227966309,-32.6493322849274,2.00002193450928,-5.28960227966309,-32.6493084430695,-1.99999809265137,-12.7056121826172,-31.2140583992004,0,-5.28960227966309,-32.6493322849274,2.00002193450928,-5.28960227966309,-29.7787964344025,2.00002193450928,-5.28960227966309,-29.7787964344025,-1.99997425079346,1.99999809265137,-50.7662177085876,-2.00002193450928,-1.99999809265137,-50.7662177085876,-2.00002193450928,-1.99999809265137,-50.7662117481232,1.99997425079346,1.99999809265137,-50.7662117481232,1.99997425079346,-7.76841640472412,-49.5616793632507,-2.38418579101562e-05,7.76841640472412,-49.5616793632507,-2.38418579101562e-05,-0,30.5331766605377,-2.38418579101562e-05,-0,-55.212014913559,0
		} 
		PolygonVertexIndex: *156 {
			a: 18,17,-17,19,17,-19,17,20,-17,17,19,-21,23,22,-22,24,22,-24,22,25,-22,22,24,-26,4,30,-2,27,30,-5,1,30,-29,28,30,-28,0,31,-7,29,31,-1,6,31,-27,26,31,-30,10,32,-10,15,32,-11,9,32,-14,13,32,-16,26,33,-28,29,33,-27,27,33,-29,28,33,-30,0,2,-2,2,0,-4,1,5,-5,5,1,-3,6,5,-8,5,6,-5,0,7,-4,7,0,-7,11,9,-9,9,11,-11,8,13,-13,13,8,-10,15,12,-14,12,15,-15,15,11,-15,11,15,-11,25,23,-22,23,25,-25,19,16,-21,16,19,-19,5,3,-8,3,5,-3,8,14,-12,14,8,-13,6,27,-5,27,6,-27,1,29,-1,29,1,-29
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *468 {
				a: 0.260382324457169,8.01928308646893e-06,-0.965505599975586,0.260382324457169,8.01928308646893e-06,-0.965505599975586,0.260382324457169,8.01928308646893e-06,-0.965505599975586,0.190009817481041,0.981782197952271,0,0.190009817481041,0.981782197952271,0,0.190009817481041,0.981782197952271,0,0.190009817481041,-0.981782197952271,-5.85184852752718e-06,0.190009817481041,-0.981782197952271,-5.85184852752718e-06,0.190009817481041,-0.981782197952271,-5.85184852752718e-06,0.260386645793915,0,0.96550440788269,0.260386645793915,0,0.96550440788269,0.260386645793915,0,0.96550440788269,-0.190009817481041,-0.981782197952271,-5.85184852752718e-06,-0.190009817481041,-0.981782197952271,-5.85184852752718e-06,-0.190009817481041,-0.981782197952271,-5.85184852752718e-06,-0.260386645793915,0,0.96550440788269,-0.260386645793915,0,0.96550440788269,-0.260386645793915,0,0.96550440788269,-0.260382324457169,8.01928308646893e-06,-0.965505599975586,-0.260382324457169,8.01928308646893e-06,-0.965505599975586,-0.260382324457169,8.01928308646893e-06,-0.965505599975586,-0.190009817481041,0.981782197952271,0,-0.190009817481041,0.981782197952271,0,-0.190009817481041,0.981782197952271,0,-0.204404190182686,0.978886604309082,0,-0.204404190182686,0.978886604309082,0,-0.204404190182686,0.978886604309082,0,-0.327584117650986,0,-0.944822013378143,-0.327584117650986,0,-0.944822013378143,-0.327584117650986,0,-0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.327584117650986,0,0.944822013378143,-0.20440661907196,-0.978886127471924,1.45865533340839e-06,-0.20440661907196,-0.978886127471924,1.45865533340839e-06,-0.20440661907196,-0.978886127471924,1.45865533340839e-06,0.204404190182686,0.978886604309082,0,0.204404190182686,0.978886604309082,0,0.204404190182686,0.978886604309082,0,0.327584117650986,0,0.944822013378143,0.327584117650986,0,0.944822013378143,0.327584117650986,0,0.944822013378143,0.327584117650986,0,-0.944822013378143,0.327584117650986,0,-0.944822013378143,0.327584117650986,0,-0.944822013378143,
0.20440661907196,-0.978886127471924,1.45865533340839e-06,0.20440661907196,-0.978886127471924,1.45865533340839e-06,0.20440661907196,-0.978886127471924,1.45865533340839e-06,-0,0.457214266061783,0.889356553554535,-0,0.457214266061783,0.889356553554535,-0,0.457214266061783,0.889356553554535,0.889356553554535,0.457214266061783,0,0.889356553554535,0.457214266061783,0,0.889356553554535,0.457214266061783,0,-0.889356553554535,0.457214266061783,0,-0.889356553554535,0.457214266061783,0,-0.889356553554535,0.457214266061783,0,-0,0.457214266061783,-0.889356553554535,-0,0.457214266061783,-0.889356553554535,-0,0.457214266061783,-0.889356553554535,-0,-0.410264611244202,-0.911966562271118,-0,-0.410264611244202,-0.911966562271118,-0,-0.410264611244202,-0.911966562271118,0.911968469619751,-0.410260260105133,6.1133602002883e-07,0.911968469619751,-0.410260260105133,6.1133602002883e-07,0.911968469619751,-0.410260260105133,6.1133602002883e-07,-0.911968469619751,-0.410260260105133,6.1133602002883e-07,-0.911968469619751,-0.410260260105133,6.1133602002883e-07,-0.911968469619751,-0.410260260105133,6.1133602002883e-07,-0,-0.410255968570709,0.911970436573029,-0,-0.410255968570709,0.911970436573029,-0,-0.410255968570709,0.911970436573029,-0,-2.13509269997303e-06,1,-0,-2.13509269997303e-06,1,-0,-2.13509269997303e-06,1,-0,-2.13509269997303e-06,1,-0,-2.13509269997303e-06,1,-0,-2.13509269997303e-06,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,2.1350790575525e-06,-1,-0,2.1350790575525e-06,-1,-0,2.1350790575525e-06,-1,-0,2.1350790575525e-06,-1,-0,2.1350790575525e-06,-1,-0,2.1350790575525e-06,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,4.57031063660907e-07,1,-0,4.57031063660907e-07,1,-0,4.57031063660907e-07,1,-0,4.57031063660907e-07,1,-0,4.57031063660907e-07,1,-0,4.57031063660907e-07,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,-4.57031518408257e-07,-1,-0,-4.57031518408257e-07,-1,-0,-4.57031518408257e-07,-1,-0,-4.57031518408257e-07,-1,-0,-4.57031518408257e-07,-1,-0,-4.57031518408257e-07,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,1,1.78814098035218e-05,-0,-1,-1.19209407785092e-05,-0,-1,-1.19209407785092e-05,-0,-1,-1.19209407785092e-05,-0,-1,-1.19209407785092e-05,-0,-1,-1.19209407785092e-05,-0,-1,-1.19209407785092e-05,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1
			} 
			NormalsW: *156 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *468 {
				a: -2.08807955459633e-06,1,7.7426630014088e-06,-2.08807955459633e-06,1,7.7426630014088e-06,-2.08807955459633e-06,1,7.7426630014088e-06,0,-0,1,0,-0,1,0,-0,1,1.11190854568122e-06,-5.74524074181681e-06,1,1.11190854568122e-06,-5.74524074181681e-06,1,1.11190854568122e-06,-5.74524074181681e-06,1,-0,1,-0,-0,1,-0,-0,1,-0,-1.11190854568122e-06,-5.74524074181681e-06,1,-1.11190854568122e-06,-5.74524074181681e-06,1,-1.11190854568122e-06,-5.74524074181681e-06,1,0,1,-0,0,1,-0,0,1,-0,2.08807955459633e-06,1,7.7426630014088e-06,2.08807955459633e-06,1,7.7426630014088e-06,2.08807955459633e-06,1,7.7426630014088e-06,0,-0,1,0,-0,1,0,-0,1,0,-0,1.00000011920929,0,-0,1.00000011920929,0,-0,1.00000011920929,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,2.98158795430936e-07,1.42785734169593e-06,1,2.98158795430936e-07,1.42785734169593e-06,1,2.98158795430936e-07,1.42785734169593e-06,1,0,-0,1.00000011920929,0,-0,1.00000011920929,0,-0,1.00000011920929,-0,1,-0,-0,1,-0,-0,1,-0,0,1,0,0,1,0,0,1,0,-2.98158795430936e-07,1.42785734169593e-06,1,-2.98158795430936e-07,1.42785734169593e-06,1,-2.98158795430936e-07,1.42785734169593e-06,1,0,0.889356553554535,-0.457214266061783,0,0.889356553554535,-0.457214266061783,0,0.889356553554535,-0.457214266061783,-0.457214266061783,0.889356553554535,0,-0.457214266061783,0.889356553554535,0,-0.457214266061783,0.889356553554535,0,0.457214266061783,0.889356553554535,-0,0.457214266061783,0.889356553554535,-0,0.457214266061783,0.889356553554535,-0,0,0.889356553554535,0.457214266061783,0,0.889356553554535,0.457214266061783,0,0.889356553554535,0.457214266061783,0,0.911966562271118,-0.410264611244202,0,0.911966562271118,-0.410264611244202,0,0.911966562271118,-0.410264611244202,0.410260260105133,0.911968469619751,8.76234821589605e-07,0.410260260105133,0.911968469619751,8.76234821589605e-07,0.410260260105133,0.911968469619751,8.76234821589605e-07,-0.410260260105133,0.911968469619751,8.76234821589605e-07,-0.410260260105133,0.911968469619751,8.76234821589605e-07,-0.410260260105133,0.911968469619751,8.76234821589605e-07,
-0,0.911970436573029,0.410255968570709,-0,0.911970436573029,0.410255968570709,-0,0.911970436573029,0.410255968570709,-0,1,2.13509269997303e-06,-0,1,2.13509269997303e-06,-0,1,2.13509269997303e-06,-0,1,2.13509269997303e-06,-0,1,2.13509269997303e-06,-0,1,2.13509269997303e-06,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,2.1350790575525e-06,0,1,2.1350790575525e-06,0,1,2.1350790575525e-06,0,1,2.1350790575525e-06,0,1,2.1350790575525e-06,0,1,2.1350790575525e-06,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-4.57031063660907e-07,0,1,-4.57031063660907e-07,0,1,-4.57031063660907e-07,0,1,-4.57031063660907e-07,0,1,-4.57031063660907e-07,0,1,-4.57031063660907e-07,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-4.57031518408257e-07,0,1,-4.57031518408257e-07,0,1,-4.57031518408257e-07,0,1,-4.57031518408257e-07,0,1,-4.57031518408257e-07,0,1,-4.57031518408257e-07,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.78814098035218e-05,1,0,-1.19209407785092e-05,1,0,-1.19209407785092e-05,1,0,-1.19209407785092e-05,1,0,-1.19209407785092e-05,1,0,-1.19209407785092e-05,1,0,-1.19209407785092e-05,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0
			} 
			BinormalsW: *156 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *468 {
				a: -0.965505599975586,0,-0.260382324457169,-0.965505599975586,0,-0.260382324457169,-0.965505599975586,0,-0.260382324457169,-0.981782197952271,0.190009817481041,0,-0.981782197952271,0.190009817481041,0,-0.981782197952271,0.190009817481041,0,0.981782197952271,0.190009817481041,8.71993449669262e-14,0.981782197952271,0.190009817481041,8.71993449669262e-14,0.981782197952271,0.190009817481041,8.71993449669262e-14,0.965504467487335,0,-0.260386645793915,0.965504467487335,0,-0.260386645793915,0.965504467487335,0,-0.260386645793915,0.981782197952271,-0.190009817481041,-8.71993449669262e-14,0.981782197952271,-0.190009817481041,-8.71993449669262e-14,0.981782197952271,-0.190009817481041,-8.71993449669262e-14,0.965504467487335,0,0.260386645793915,0.965504467487335,0,0.260386645793915,0.965504467487335,0,0.260386645793915,-0.965505599975586,0,0.260382324457169,-0.965505599975586,0,0.260382324457169,-0.965505599975586,0,0.260382324457169,-0.981782197952271,-0.190009817481041,0,-0.981782197952271,-0.190009817481041,0,-0.981782197952271,-0.190009817481041,0,-0.978886604309082,-0.204404205083847,0,-0.978886604309082,-0.204404205083847,0,-0.978886604309082,-0.204404205083847,0,-0.944822013378143,0,0.327584117650986,-0.944822013378143,0,0.327584117650986,-0.944822013378143,0,0.327584117650986,0.944822013378143,0,0.327584117650986,0.944822013378143,0,0.327584117650986,0.944822013378143,0,0.327584117650986,0.978886067867279,-0.204406604170799,-2.1735658249384e-14,0.978886067867279,-0.204406604170799,-2.1735658249384e-14,0.978886067867279,-0.204406604170799,-2.1735658249384e-14,-0.978886604309082,0.204404205083847,0,-0.978886604309082,0.204404205083847,0,-0.978886604309082,0.204404205083847,0,0.944822013378143,0,-0.327584117650986,0.944822013378143,0,-0.327584117650986,0.944822013378143,0,-0.327584117650986,-0.944822013378143,0,-0.327584117650986,-0.944822013378143,0,-0.327584117650986,-0.944822013378143,0,-0.327584117650986,0.978886067867279,0.204406604170799,2.1735658249384e-14,0.978886067867279,0.204406604170799,2.1735658249384e-14,
0.978886067867279,0.204406604170799,2.1735658249384e-14,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,9.17003490030766e-07,5.48291666291334e-07,-1,9.17003490030766e-07,5.48291666291334e-07,-1,9.17003490030766e-07,5.48291666291334e-07,-1,9.17003490030766e-07,-5.48291666291334e-07,1,9.17003490030766e-07,-5.48291666291334e-07,1,9.17003490030766e-07,-5.48291666291334e-07,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			} 
			TangentsW: *156 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *256 {
				a: 0.0199999809265137,-0.48357155919075,-0.0199999809265137,-0.48357155919075,-0.0199999809265137,-0.371904939413071,0.0199999809265137,-0.371904939413071,0.0199997425079346,-0.483571588993073,-0.0200002193450928,-0.483571588993073,-0.0199999809265137,-0.371904253959656,0.0199999809265137,-0.371904969215393,0.0199999809265137,-0.483571618795395,-0.0199999809265137,-0.483571618795395,-0.0199999809265137,-0.371904283761978,0.0199999809265137,-0.371904283761978,0.0200002193450928,-0.483571588993073,-0.0199997425079346,-0.483571588993073,-0.0199999809265137,-0.371904969215393,0.0199999809265137,-0.371904253959656,-0.0199999809265137,-0.255239635705948,-0.0199999809265137,0.266428560018539,0.0199999809265137,0.266428560018539,0.0199999809265137,-0.255239635705948,-0.0199999809265137,-0.255239188671112,-0.0200002193450928,0.266428530216217,0.0199997425079346,0.266428530216217,0.0199999809265137,-0.25523966550827,-0.0199999809265137,-0.255239218473434,-0.0199999809265137,0.266428500413895,0.0199999809265137,0.266428500413895,0.0199999809265137,-0.255239218473434,-0.0199999809265137,-0.25523966550827,-0.0199997425079346,0.266428530216217,0.0200002193450928,0.266428530216217,0.0199999809265137,-0.255239188671112,-0.0458637662231922,-0.326493352651596,-0.122673399746418,-0.312140852212906,-0.045863825827837,-0.297788232564926,-0.108515009284019,-0.0199997425079346,-0.184051215648651,0,-0.108515009284019,0.0200002193450928,-0.010104563087225,0.0200021546334028,0.0654316619038582,1.93459754882497e-06,-0.0101045174524188,-0.0199980456382036,0.0458635538816452,-0.297787964344025,0.122673243284225,-0.312140583992004,0.0458635538816452,-0.326493322849274,0.0101045174524188,-0.0199980456382036,-0.0654316619038582,1.93459754882497e-06,0.010104563087225,0.0200021546334028,-0.0458635538816452,-0.326493322849274,-0.122673243284225,-0.312140583992004,-0.0458635538816452,-0.297787964344025,0.045863825827837,-0.297788232564926,0.122673399746418,-0.312140852212906,0.0458637662231922,-0.326493352651596,0.108515009284019,
0.0200002193450928,0.184051215648651,0,0.108515009284019,-0.0199997425079346,-0.0200002193450928,-0.326493322849274,0.0199999809265137,-0.326493084430695,0.0199997425079346,-0.297787964344025,-0.0200002193450928,-0.297787964344025,-0.0199997425079346,-0.297787964344025,-0.0199999809265137,-0.326493084430695,0.0200002193450928,-0.326493322849274,0.0200002193450928,-0.297787964344025,0.0199999809265137,0.0200066305696964,-0.0199999809265137,0.0200066305696964,-0.0199999809265137,-0.0199933312833309,0.0199999809265137,-0.0199933312833309,0.0199999809265137,0.0200030244886875,-0.0199999809265137,0.0200030244886875,-0.0199999809265137,-0.0199969373643398,0.0199999809265137,-0.0199969373643398,-0.0199999809265137,-0.483571588993073,0.0199999809265137,-0.483571588993073,-0.0199999809265137,-0.507662177085876,0.0199999809265137,-0.507662177085876,-0.0199999809265137,-0.483571588993073,0.0199999809265137,-0.483571588993073,-0.0199999809265137,-0.507662117481232,0.0199999809265137,-0.507662117481232,0.118421770632267,-0.0200002193450928,0.118421770632267,0.0199997425079346,0.177350133657455,-2.38418579101562e-07,0.0123446676880121,-0.507662177085876,0.0123446676880121,-0.483571588993073,0.0733976289629936,-0.495616793632507,-0.0123448241502047,-0.483571588993073,-0.0123448241502047,-0.507662117481232,-0.0733977854251862,-0.495616793632507,0.0841917917132378,0.0199990123510361,0.084191806614399,-0.0200009495019913,0.0252634026110172,-9.69250891103002e-07,-0.118421770632267,0.0199997425079346,-0.118421770632267,-0.0200002193450928,-0.177350133657455,-2.38418579101562e-07,0.0123448241502047,-0.507662117481232,0.0123448241502047,-0.483571588993073,0.0733977854251862,-0.495616793632507,-0.0123446676880121,-0.483571588993073,-0.0123446676880121,-0.507662177085876,-0.0733976289629936,-0.495616793632507,-0.084191806614399,-0.0200009495019913,-0.0841917917132378,0.0199990123510361,-0.0252634026110172,-9.69250891103002e-07,0.0199999809265137,0.227805808186531,-0.0199999809265137,0.227805808186531,0,0.271548926830292,0.0200002193450928,
0.227805703878403,-0.0199997425079346,0.227805703878403,2.38418579101562e-07,0.271548837423325,0.0199997425079346,0.227805703878403,-0.0200002193450928,0.227805703878403,-2.38418579101562e-07,0.271548837423325,0.0199999809265137,0.227805584669113,-0.0199999809265137,0.227805584669113,0,0.271548718214035,-0.0199999809265137,-0.454765558242798,0.0199999809265137,-0.454765558242798,0,-0.503515124320984,-0.0199997294694185,-0.454766631126404,0.0200002323836088,-0.454766720533371,-0,-0.50351619720459,-0.0200002323836088,-0.454766720533371,0.0199997294694185,-0.454766631126404,0,-0.50351619720459,-0.0199999809265137,-0.454767823219299,0.0199999809265137,-0.454767823219299,0,-0.503517270088196
			} 
			UVIndex: *156 {
				a: 34,33,32,37,36,35,39,38,40,42,41,43,46,45,44,49,48,47,51,50,52,54,53,55,80,82,81,83,85,84,86,88,87,89,91,90,92,94,93,95,97,96,98,100,99,101,103,102,104,106,105,107,109,108,110,112,111,113,115,114,116,118,117,119,121,120,122,124,123,125,127,126,0,2,1,2,0,3,4,6,5,6,4,7,9,11,10,11,9,8,13,15,14,15,13,12,19,17,16,17,19,18,23,21,20,21,23,22,25,27,26,27,25,24,30,28,31,28,30,29,58,56,57,56,58,59,63,61,62,61,63,60,67,65,66,65,67,64,69,71,68,71,69,70,72,75,73,75,72,74,76,79,77,79,76,78
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2336634038864, "Model::Sword_Invetory_Local", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 2344101507584, "Material::BlackHole_small", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",0,0,0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0,0,0
			P: "Opacity", "double", "Number", "",1
		}
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Sword_Invetory_Local, Model::RootNode
	C: "OO",2336634038864,0
	
	;Material::BlackHole_small, Model::Sword_Invetory_Local
	C: "OO",2344101507584,2336634038864
	
	;Geometry::Scene, Model::Sword_Invetory_Local
	C: "OO",2339815069616,2336634038864
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
