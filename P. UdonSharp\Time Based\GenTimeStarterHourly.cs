using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class GenTimeStarterHourly : UdonSharpBehaviour
{
    public GameObject[] TimedEvents;
    public int[] activationHour;
    public int currentHour,LastHour;
    public bool IsEnabled;
    private bool timerRunning = false;

    public VRCUrl[] RandomDefaultURL;

    void Start()
    {
        IsEnabled = true;
        StartTimer();
    }

    void OnEnable()
    {
        IsEnabled = true;
        if (!timerRunning){StartTimer();}
    }

    void OnDisable(){IsEnabled = false;}

    private void StartTimer()
    {
        if (!timerRunning)
        {
            timerRunning = true;
            SendCustomEventDelayedSeconds(nameof(UpdateTime), 60f);
        }
    }

    public void UpdateTime()
    {
        if (!IsEnabled)
        {
            timerRunning = false;
            return;
        }

        System.DateTime networkTime = Networking.GetNetworkDateTime().ToUniversalTime();
        currentHour = networkTime.Hour;

        for (int i = 0; i < activationHour.Length; i++){
            if (networkTime.Hour == activationHour[i] && currentHour != LastHour)
            {
                if (TimedEvents[i] != null){TimedEvents[i].SetActive(true);}
                Debug.Log("GenTimeStarter triggered with a value of " + currentHour);
            }
            else if (networkTime.Hour != activationHour[i] && currentHour != LastHour)
            {
                if (TimedEvents[i] != null){TimedEvents[i].SetActive(false);}
                Debug.Log("GenTimeStarter triggered with a value of " + currentHour);
            }
        }

        if(currentHour != LastHour){LastHour = currentHour;}

        if(IsEnabled){SendCustomEventDelayedSeconds(nameof(UpdateTime), 60f);}
        else{timerRunning = false;}
    }

    public void DeleteEverything(){
        for (int i = 0; i < TimedEvents.Length; i++){
            if (TimedEvents[i] != null){Destroy(TimedEvents[i]);}
        }
        Destroy(gameObject);
    }
}
